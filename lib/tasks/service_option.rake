namespace :service_option do
  desc "It will create service option to hide/unhide asset risk center"
  task asset_risk_center_sections: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "other",
      service_name: "asset_risk_center_sections"
    )
  end
  
  desc "Create import service options for all modules"
  task create_import_options: :environment do
    modules = ["assets", "phone_numbers", "help_tickets", "ip_addresses", "contracts", 
               "company_users", "apps", "locations", "general_transactions", "vendors", "telecom_services"]
    
    modules.each do |module_name|
      ServiceOption.find_or_create_by(service_name: module_name, service_type: ServiceOption.service_types[:imports]) do |option|
        option.status = false
        option.company_ids = []
      end
    end
    
    puts "Import service options created successfully."
  end

  desc "It will create feature flag to enable/disable google workspace integration"
  task assets_google_workspace_integration: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "discovery_tools/google_workspace_integration",
      status: true
    )
  end

  desc "It will create feature flag for ticket comments feature"
  task help_tickets_ai_comments: :environment do
    ServiceOption.find_or_create_by!(
      service_type: "feature",
      service_name: "help_tickets/ai_comments",
      status: true
    )
  end

  desc "Delete unused feature flags from serviceoption table"
  task delete_unused_feature_flags: :environment do
    service_names = [
      "managed_assets/people_tab",
      "help_tickets/drafts",
      "help_tickets/surveys",
      "help_ticket_modern_view",
      "helpdesk/people",
      "knowledge_base_google_drive_integration",
      "knowledge_base_one_drive_integration",
      "help_tickets/kanban_view",
      "contracts/calendar",
      "hubspot_integration",
      "help_tickets/ai_summary",
      "knowledge_base_PDF_import"
    ]

    ServiceOption.where(service_type: :feature, service_name: service_names).destroy_all
  end
end
