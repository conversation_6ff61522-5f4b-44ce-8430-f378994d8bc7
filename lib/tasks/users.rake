namespace :users do
  desc "It will replace db user guid with cognito user guid if both doesn't match"
  task replace_user_guid_with_cognito_user_guid: :environment do
    @client = Aws::CognitoIdentityProvider::Client.new(
      region: Rails.application.credentials.aws[:region],
      access_key_id: Rails.application.credentials.aws[:cognito][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:cognito][:secret_access_key]
    )

    pt = nil

    loop do
      res = @client.list_users({ user_pool_id: Rails.application.credentials.aws[:cognito][:user_pool_id], pagination_token: pt })
      pt = res.pagination_token
      res.users.each do |usr|
        cog_email = usr.attributes.find { |att| att.name  == "email" }.value
        cog_guid = usr.username
        user = User.find_by(email: cog_email)
        user.update_column(:guid, cog_guid) if user && user.guid != cog_guid
      end

      break if pt.blank?
    end
  end

  desc "It will set users emails to confirmed if they have confirmed in the past"
  task update_has_confirmed_email: :environment do
    User.all.each do |user|
      user.update(has_confirmed_email: true) if user.confirmed_at.present?
    end
  end

  desc "It will remind users who have signed up that they still need to confirm email"
  task send_unconfirmed_email_reminder: :environment do
    SendUnconfirmedEmailReminderWorker.perform_async
  end

  desc "Convert all existing emails to downcase"
  task convert_emails_to_downcase: :environment do
    users = User.all
    users.find_each do |user|
      user.update_columns(email: user.email&.downcase)
    end
  end

  desc "Populate first and last name for users if name is missing"
  task update_user_name: :environment do
    users =  User.where(first_name: [nil, '']).where(last_name: [nil, ''])
    users.find_each do |user|
      name = user.email.split("@")[0]
      full_name = name.split(/\s*[._-]\s*/x)
      if full_name.count > 1
        user.update_columns(first_name: full_name[0], last_name: full_name[1])
      else
        user.update_columns(first_name: full_name[0])
      end
    end
  end

  desc "Update walkthough status for old users who have already seen it"
  task update_walktroughs_status: :environment do
    User.where(has_seen_module_onboarding: true).find_each do |user|
      user.has_seen_module_walkthroughs["contracts"] = true
      user.has_seen_module_walkthroughs["vendors"] = true
      user.save!
    end
  end

  desc "Adds missing confirmation token for users"
  task adds_missing_confirmation_tokens: :environment do
    User.where(confirmation_token: nil, sso_user: false).each do |u|
      u.update_columns(confirmation_token: SecureRandom.hex(32))
    end
  end

  desc "Delete all users that has no company user"
  task delete_user_with_no_company_users: :environment do
    User.all.find_each do |u|
      u.destroy if u.company_users.count == 0
    end
  end

  def send_mailer user, company
    Devise::Mailer.confirmation_instructions(user, company).deliver
  end

  desc "Add ticket_modern_view => false to existing users' has_seen_module_walkthroughs"
  task add_ticket_modern_view: :environment do
    User.find_each do |user|
      data = user.has_seen_module_walkthroughs || {}
      unless data.key?("ticket_modern_view")
        data["ticket_modern_view"] = false
        user.update_column(:has_seen_module_walkthroughs, data)
        puts "Updated user ##{user.id}"
      end
    end
    puts "Done updating users."
  end

  desc "Add AI summary seen => false to existing users' has_seen_module_walkthroughs"
  task add_ai_summary_seen: :environment do
    User.find_each do |user|
      data = user.has_seen_module_walkthroughs || {}
      unless data.key?("ticket_ai_summary")
        data["ticket_ai_summary"] = false
        user.update_column(:has_seen_module_walkthroughs, data)
        puts "Updated user ##{user.id}"
      end
    end
    puts "Done updating users."
  end

  desc "Add comment AI tools => false to existing users' has_seen_module_walkthroughs"
  task add_comment_ai_tools_key: :environment do
    erroneous_user_ids = []
    User.find_each do |user|
      data = user.has_seen_module_walkthroughs || {}
      unless data.key?("comment_ai_tools")
        data["comment_ai_tools"] = false
        user.update_column(:has_seen_module_walkthroughs, data)
        puts "Updated user ##{user.id}"
      end
    rescue => e
      erroneous_user_ids << user.id
      puts "Error while updating user with ID: #{user.id} - #{e}"
      next
    end
    puts "updated users successfully"
    puts "error while updating users with IDs: #{erroneous_user_ids}"
  end
end
