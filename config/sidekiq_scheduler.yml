aa_slack_alert_for_high_latency_queues:
  cron: '*/2 * * * *'
  class: SlackAlertForHighLatencyQueuesWorker
  enabled: true
  queue: low_intensity_schedule
ab_send_sla_scheduled_emails:
  cron: '*/15 * * * *'
  class: ScheduledSlaWorker
  enabled: true
  queue: high_intensity_schedule
ac_ticket_state_based_timed_tasks:
  cron: '*/15 * * * *'
  class: TicketStateBasedTimedTasksWorker
  enabled: true
  queue: low_intensity_schedule
ad_scheduled_comments:
  cron: '*/15 * * * *'
  class: ScheduledCommentsWorker
  enabled: true
  queue: low_intensity_schedule
ae_check_services_status:
  cron: '0 * * * *'
  class: CheckServicesStatusTaskWorker
  enabled: true
  queue: low_intensity_schedule
af_automated_timed_tasks:
  cron: '0 * * * *'
  class: AutomatedTimedTasksWorker
  enabled: true
  queue: low_intensity_schedule
ag_check_assets_services_up_status:
  cron: '0 */4 * * *'
  class: CheckAssetsServicesUpStatusWorker
  enabled: true
  queue: low_intensity_schedule
ah_gsuite_signup_path_update:
  cron: '0 */6 * * *'
  class: UpdateGsuiteSignupPathWorker
  enabled: true
  queue: integrations
ai_send_email_limit_reach_alert:
  cron: '0 */8 * * *'
  class: SendEmailLimitReachAlertWorker
  enabled: true
  queue: low_intensity_schedule
aj_notify_slack_email_limit_reach:
  cron: '0 */8 * * *'
  class: EmailLimitReachSlackAlertWorker
  enabled: true
  queue: low_intensity_schedule
ak_set_up_pending_monitoring_companies:
  cron: '0 0 * * *'
  class: SetUpPendingMonitoringCompaniesWorker
  enabled: true
  queue: externals
al_delete_inactive_agent_locations:
  cron: '0 0 * * *'
  class: DeleteInactiveAgentLocationsWorker
  enabled: true
  queue: high_intensity_schedule
am_fetch_non_sample_companies_plaid_transactions:
  cron: '0 0 * * *'
  class: FetchNonSampleCompaniesPlaidTransactionsWorker
  enabled: true
  queue: low_intensity_schedule
an_fetch_sample_companies_plaid_transactions:
  cron: '0 0 1,15 * *'
  class: FetchSampleCompaniesPlaidTransactionsWorker
  enabled: true
  queue: low_intensity_schedule
ao_long_integrations_daily_tasks:
  cron: '0 2 * * *'
  class: LongIntegrationsDailyTasksWorker
  enabled: true
  queue: long_integrations
ap_short_integrations_daily_tasks_first:
  cron: '0 3 * * *'
  class: ShortIntegrationsDailyTasksFirstWorker
  enabled: true
  queue: integrations
aq_run_garbage_collector:
  cron: '0 3 * * *'
  class: RunGarbageCollectorWorker
  enabled: true
  queue: low_intensity_schedule
ar_short_integrations_daily_tasks_second:
  cron: '15 3 * * *'
  class: ShortIntegrationsDailyTasksSecondWorker
  enabled: true
  queue: integrations
as_short_integrations_daily_tasks_third:
  cron: '30 3 * * *'
  class: ShortIntegrationsDailyTasksThirdWorker
  enabled: true
  queue: integrations
at_check_recurring_general_transactions:
  cron: '0 4 * * *'
  class: CheckRecurringGeneralTransactionsWorker
  enabled: true
  queue: low_intensity_schedule
au_free_trial_reminder_email:
  cron: '20 4 * * *'
  class: FreeTrialReminderEmailWorker
  enabled: true
  queue: email
av_free_trial_expired_email:
  cron: '20 4 * * *'
  class: SendFreeTrialExpiredEmailTaskWorker
  enabled: true
  queue: email
aw_send_unconfirmed_email_reminder:
  cron: '0 5 * * *'
  class: SendUnconfirmedEmailReminderWorker
  enabled: true
  queue: email
ax_update_old_company_subscriptions:
  cron: '0 5 * * *'
  class: UpdateOldCompanySubscriptionsWorker
  enabled: true
  queue: high_intensity_schedule
ay_admin_panel_sync_microsoft_licenses_info:
  cron: '30 5 * * *'
  class: AzureLicensesDataWorker
  enabled: true
  queue: high_intensity_schedule
az_contracts_alert_emails:
  cron: '25 6 * * *'
  class: ContractAlertEmailsWorker
  enabled: true
  queue: email
ba_managed_asset_event_emails:
  cron: '0 7 * * *'
  class: ManagedAssetEventEmailsWorker
  enabled: true
  queue: email
bb_delete_api_event_logs:
  cron: '5 8 * * *'
  class: DeleteApiEventLogsWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_api_logs
bc_delete_plaid_event_logs:
  cron: '10 8 * * *'
  class: DeleteApiEventLogsWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_plaid_logs
bd_delete_asset_discovery_event_logs:
  cron: '15 8 * * *'
  class: DeleteApiEventLogsWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_asset_discovery_logs
be_send_helpdesk_scheduled_reports:
  cron: '0 9 * * *'
  class: SendHelpdeskScheduledReportsWorker
  enabled: true
  queue: email
bf_send_scheduled_tasks:
  cron: '*/15 * * * *'
  class: ScheduledTasksWorker
  enabled: true
  queue: low_intensity_schedule
bg_cognito_update_with_primary_guid:
  cron: '30 9 * * *'
  class: UpdateCognitoUsersWithPrimaryGuidsWorker
  enabled: true
  queue: low_intensity_schedule
bh_article_review_reminders_email:
  cron: '55 9 * * *'
  class: SendArticleReviewMailWorker
  enabled: true
  queue: email
bi_send_referred_reminder_emails:
  cron: '0 16 * * *'
  class: SendReferredReminderEmailsWorker
  enabled: true
  queue: referral_mailers
bj_vendor_spend_alert_send_email:
  cron: '0 12 * * TUE'
  class: VendorSpendAlertSendEmailWorker
  enabled: true
  queue: low_intensity_schedule
bk_long_integrations_weekly_tasks:
  cron: '0 3 * * SAT'
  class: LongIntegrationsWeeklyTasksWorker
  enabled: true
  queue: long_integrations
bl_cognito_users_backup:
  cron: '0 8 * * SAT'
  class: CognitoUsersBackupWorker
  enabled: true
  queue: low_intensity_schedule
bm_delete_help_ticket_emails_logs:
  cron: '0 8 * * SAT'
  class: DeleteApiEventLogsWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_help_ticket_emails_logs
bn_delete_email_logs:
  cron: '0 1 * * SUN'
  class: BulkDeleteEmailLogWorker
  enabled: true
  queue: high_intensity_schedule
bo_delete_help_ticket_emails_from_s3:
  cron: '0 2 * * SUN'
  class: DeleteHelpTicketEmailsFromS3Worker
  enabled: true
  queue: high_intensity_schedule
bp_delete_automated_tasks_execution_logs:
  cron: '0 3 * * SUN'
  class: BulkDeleteAutomatedTasksExecutionLogWorker
  enabled: true
  queue: high_intensity_schedule
bq_delete_ticket_sessions:
  cron: '0 6 * * SUN'
  class: DeleteTicketSessionsWorker
  enabled: true
  queue: high_intensity_schedule
br_delete_pg_dev_logs_from_s3:
  cron: '0 8 * * SUN'
  class: DeletePgDevLogsFromS3Worker
  enabled: true
  queue: high_intensity_schedule
bs_fetch_asset_warranty:
  cron: '0 8 * * SUN'
  class: FetchAssetWarrantyWorker
  enabled: true
  queue: low_intensity_schedule
bt_delete_unused_custom_form_attachment:
  cron: '0 8 * * SUN'
  class: DeleteUnusedCustomFormAttachmentWorker
  enabled: true
  queue: high_intensity_schedule
bu_delete_old_cognito_logs:
  cron: '0 8 * * SUN'
  class: DeleteOldCognitoLogsWorker
  enabled: true
  queue: high_intensity_schedule
bv_delete_windows_app_build_from_s3:
  cron: '0 0 1 1,7 *'
  class: DeleteWindowsAppBuildFromS3Worker
  enabled: true
  queue: high_intensity_schedule
bw_asset_integration_failed_task:
  cron: '0 9 * * TUE,FRI'
  class: AssetIntegrationFailedTaskWorker
  enabled: true
  queue: low_intensity_schedule
bx_agent_not_resynced_task:
  cron: '0 9 * * TUE,FRI'
  class: 'AgentNotResyncedTaskWorker'
  enabled: true
  queue: low_intensity_schedule
by_low_disk_space_task:
  cron: '0 9 * * TUE,FRI'
  class: 'LowDiskSpaceTaskWorker'
  enabled: true
  queue: low_intensity_schedule
bz_software_not_detected_worker:
  cron: '0 9 * * TUE,FRI'
  class: 'SoftwareNotDetectedTaskWorker'
  enabled: true
  queue: low_intensity_schedule
expired_companies_alert:
  cron: '0 2 1 * *'
  class: 'ExpiredCompaniesSlackAlertWorker'
  enabled: true
  queue: low_intensity_schedule
bw_delete_staff_import_logs:
  cron: '5 7 * * SUN'
  class: LogCleanupWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_staff_import_logs
bx_delete_stripe_webhook_logs:
  cron: '10 7 * * SUN'
  class: LogCleanupWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_stripe_webhook_logs
by_delete_super_admin_logs:
  cron: '15 7 * * SUN'
  class: LogCleanupWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_super_admin_logs
bz_delete_v3_incoming_logs:
  cron: '20 7 * * SUN'
  class: LogCleanupWorker
  enabled: true
  queue: high_intensity_schedule
  args: delete_v3_incoming_logs
# create_sample_company_data:
#   cron: '0 2 * * SAT'
#   class: CreateSampleCompanyDataTaskWorker
#   enabled: true
#   queue: high_intensity_schedule
# kaseya_async_weekly:
#   cron: '55 11 * * SAT'
#   class: KaseyaAsyncWeeklyTaskWorker
#   enabled: true
#   queue: long_integrations
# update_subscription_status:
#   cron: '30 8 * * *'
#   class: UpdateSubscriptionStatusTaskWorker
#   enabled: true
#   queue: low_intensity_schedule
# delete_custom_emails_for_expired_companies:
#   cron: '50 9 * * *'
#   class: DeleteExpiredCompaniesCustomEmailsWorker
#   enabled: true
#   queue: high_intensity_schedule
