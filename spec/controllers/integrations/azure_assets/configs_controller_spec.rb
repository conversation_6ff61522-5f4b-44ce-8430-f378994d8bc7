require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::AzureAssets::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    @azure_service = Integrations::AzureAssets::FetchData.new(company.id)
    controller.send(:set_privilege)
  end

  describe "Get #consent_callback" do
    it "authenticate and save credentials and redirect_to sync accounts page" do
      token_detail = {"token_type":"Bearer",
                      "expires_in"=>3600,
                      "ext_expires_in"=>3600,
                      "access_token"=>"mock_access_token",
                      "refresh_token"=>"OAQABAAAAAAAP0wLlqdLVTHIXIEwdWZ5FFiEbogAA",
                      "id_token"=>"mock_id_token"}
      allow_any_instance_of(Integrations::AzureAssets::FetchData).to receive(:token).and_return(token_detail)
      get :consent_callback , params: {"code"=>"OAQABAAIAAAAP0wLlqdLVToOpA4kwzSnxGu6c1Erydyxmy_E6SpKwPa3zRHTfdxzsSUWmz4lB8fwg8u",
                                        state: "company_id=#{company.id}&import_type=discovered_asset",
                                        "session_state"=>"da3d747e-cbe2-4fb7-9476-c93ed8d089e5",
                                        "controller"=>"integrations/azure/configs",
                                        "action"=>"consent_callback"}
      expect(response).to redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.root_domain}:3000/managed_assets/discovery_tools/connectors?direct_integration=true")
      expect(Integrations::AzureAssets::Config.where(company_id: company.id).count).to eq(1)
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      config = FactoryBot.create(:azure_assets_config, company_id: company.id)
      resync_params = {
        id: config.id,
        integration_name: 'azure_assets'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'Delete #destroy' do
    it 'will remove the integration from company and return http success' do
      config = FactoryBot.create(:azure_assets_config, company_id: company.id)
      delete :destroy, params: { id: config.id }
      result = JSON.parse(response.body)
      expect(response.status).to eq(200)
      expect(result['message']).to eq('Integration deleted successfully')
      expect(Integrations::AzureAssets::Config.where(company_id: company.id).count).to eq(0)
    end
  end
end
