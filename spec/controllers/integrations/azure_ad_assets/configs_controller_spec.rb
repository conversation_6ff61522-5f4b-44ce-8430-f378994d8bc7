require 'rails_helper'
include CompanyUserHelper

RSpec.describe Integrations::AzureAdAssets::ConfigsController, type: :controller do
  create_company_and_user

  before(:each) do
    Integrations::AzureAdAssets::Config.create(
      token: 'access_token',
      expires_in: 2321,
      refresh_token: 'refresh_token',
      company_id: company.id)

    @config ||= Company.find(company.id).azure_ad_assets_config
    controller.send(:set_privilege)
  end

  describe 'Get #consent_callback' do
    it 'authenticate credential and save credendial and redirect_to sync accounts page' do
      token_detail={'token_type':'Bearer',
        'scope': 'offline_access Device.Read.All',
        'expires_in'=>3600,
        'ext_expires_in'=>3600,
        'access_token'=>'mock_access_token',
        'refresh_token'=>'OAQABAAAAAAAP0wLlqdLVToOpA4kwzSnxiAMsfekyjT2BNGLdR',
        'id_token'=>'mock_id_token'}

      allow_any_instance_of(Integrations::AzureAdAssets::FetchData).to receive(:token).and_return(token_detail)
      get :consent_callback , params: {
        'code'=>'OAQABAAIAAAAP0wLlqdLVToOpA4kwzSnxGu6c1Erydyxmy',
        'state': "company_id=#{company.id}&import_type=discovered_asset",
        'session_state'=>'da3d747e-cbe2-4fb7-9476-c93ed8d089e5',
        'controller'=>'integrations/azure_ad_assets/configs',
        'action'=>'consent_callback'}
      response.should redirect_to("http://#{company.subdomain}.#{Rails.application.credentials.assets_connectors_path}")
      expect(Integrations::AzureAdAssets::Config.where(company_id: company.id).count).to eq(1)
    end
  end

  describe "Post #resync" do
    it "returns http success" do
      resync_params = {
        id: @config.id,
        integration_name: 'azure_ad_assets'
      }
      post :resync, params: resync_params
      expect(response).to have_http_status(:ok)
    end
  end

  describe 'Delete #destroy' do
    it 'Delete AzureAD config' do
      delete :destroy, params: { id: @config.id}
      expect(response.status).to eq 200
      expect(company.discovered_assets.joins(:asset_sources).where(asset_sources: { source: 'azure_ad_devices' }).count).to eq(0)
    end
  end

  describe 'Disable AzureAD' do
    it 'Disable AzureAD' do
      post :deactivate, params: { id: @config.id}
      expect(response.status).to eq 200
      expect(@config.company_integration.active).to eq(false)
      expect(@config.company_integration.status).to eq(false)
    end
  end
end
