# frozen_string_literal: true

class Integrations::MsIntuneAssets::FetchData
  include ReadReplicaDb

  attr_accessor :client, :error

  MICROSOFT_HOST = "https://graph.microsoft.com"
  MICROSOFT_AUTHORIZATION_URI = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize?prompt=consent"
  MICROSOFT_SCOPE = "offline_access email openid profile DeviceManagementManagedDevices.Read.All"
  INTUNE_CALLBACK = "#{Rails.application.credentials.domain_with_port}/integrations/ms_intune_assets/oauth2callback"
  MICROSOFT_TOKEN_URI= "https://login.microsoftonline.com/common/oauth2/v2.0/token"

  def initialize(company_id)
    @company_id = company_id
    set_read_replica_db do
      @config ||= Company.find_by_cache(id: @company_id)&.ms_intune_assets_config
      @intg_id = Integration.find_by_name("ms_intune_assets").id
    end
    @api_logs = []
    @requests_count = 0
  end

  def client(state_data = nil)
    #Following OAuth convention. params[:state] has company id and import type.
    @client ||= Signet::OAuth2::Client.new(
      authorization_uri: MICROSOFT_AUTHORIZATION_URI,
      client_id: Rails.application.credentials.ms_intune[:client_id],
      response_type: "code",
      response_mode: "query",
      scope: MICROSOFT_SCOPE,
      redirect_uri: INTUNE_CALLBACK,
      state: state_data
    )
  end

  def token(code)
    body = {
      grant_type: "authorization_code",
      code: code,
      client_id: Rails.application.credentials.ms_intune[:client_id],
      client_secret: Rails.application.credentials.ms_intune[:client_secret],
      scope: MICROSOFT_SCOPE,
      redirect_uri: INTUNE_CALLBACK,
    }
    headers = { 'Content-Type': "application/x-www-form-urlencoded" }
    detail_params = { code: code }
    response = HTTParty.post(MICROSOFT_TOKEN_URI, headers: headers, body: body)
    log_event(response, "token", detail_params)
    response.parsed_response
  end

  def refresh_token
    body = {
      grant_type: "refresh_token",
      client_id: Rails.application.credentials.ms_intune[:client_id],
      client_secret: Rails.application.credentials.ms_intune[:client_secret],
      tenant: Rails.application.credentials.ms_intune[:tenant],
      scope: MICROSOFT_SCOPE,
      refresh_token: @config.refresh_token,
      redirect_uri: INTUNE_CALLBACK,
    }
    headers = { 'Content-Type': "application/x-www-form-urlencoded" }
    response = HTTParty.post(MICROSOFT_TOKEN_URI, headers: headers, body: body)
    log_event(response, "refresh_token")
    response.parsed_response
  end

  def get_intune_devices(next_page_token = nil)
    url = "/v1.0/deviceManagement/managedDevices?$top=100"
    url += "&$skiptoken=#{next_page_token}" if next_page_token
    make_api_call(url, "get_intune_devices")
  end

  def check_token_expiry
    if @requests_count == 30
      response = refresh_token
      if response && response["access_token"] && response["refresh_token"]
        expiry_time = Time.now + (response["expires_in"] - 600)

        @config.update!(
          token: response["access_token"],
          expires_in: expiry_time,
          refresh_token: response["refresh_token"],
          skip_callbacks: true
        )

        @config.reload
      end
      @requests_count = 0
    end
    @requests_count += 1
  end

  def make_api_call(endpoint, api_type)
    url = endpoint
    check_token_expiry
    all_devices = []
    response = nil
    detail_params = { endpoint: url, api_type: api_type }
    headers = {
      Authorization: "Bearer #{@config.token}",
      'Content-Type': "application/json",
    }
    loop do
      response = HTTParty.get("#{MICROSOFT_HOST}#{url}", headers: headers)
      if response&.parsed_response["error"]
        self.error = response.parsed_response["error"]["message"]
      end

      log_event(response, api_type, detail_params)
      break if self.error.present?

      data = response.parsed_response
      devices = data['value']
      all_devices += devices
      break if data['@odata.nextLink'].nil?
      next_link = data['@odata.nextLink']
      
      url = "#{next_link.split("https://graph.microsoft.com")[1]}"
    end
    if response.ok?
      response["value"] = all_devices
    end
    response
  end

  def log_event(response, api_type, fur_detail = {}, excep = nil)
    status = response.present? && response.code == 200 ? :success : :error
    api_response = { code: response.code, message: response.message, body: response.body }.to_s if response
    log_params = {
      api_type: api_type,
      class_name: self.class,
      integration_id: @intg_id,
      company_id: @company_id,
      status: status,
      detail: details.merge(fur_detail || {}),
      activity: 1,
      response: api_response,
      created_at: DateTime.now,
      updated_at: DateTime.now,
      error_detail: excep.present? ? excep.backtrace : nil,
      error_message: excep.present? ? excep.message : nil,
    }
    @api_logs << log_params
    if Rails.env.test?
      Logs::ApiEvent.create(log_params)
    else
      LogCreationWorker.perform_async('Logs::ApiEvent', log_params.to_json)
    end
  end

  def ok?
    error.blank?
  end

  def details
    @config ? { token: @config.token, refresh_token: @config.refresh_token, expires_in: @config.expires_in } : {}
  end
end
