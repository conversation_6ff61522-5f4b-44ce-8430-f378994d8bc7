require 'google/apis/admin_directory_v1'

class Integrations::GoogleWorkspace::FetchData
  include Integrations::Shared::GsuiteHelpers

  SERVICE = 'Google::Apis::AdminDirectoryV1::DirectoryService'

  def initialize(company_id)
    @api_logs = []
    @details = {}
    @company_id = company_id
    @config = Integrations::GoogleWorkspace::Config.find_by(company_id: @company_id)
  end

  def get_credentials(state_data = nil)
    scope = [
    'https://www.googleapis.com/auth/admin.directory.device.mobile.readonly',
    'https://www.googleapis.com/auth/admin.directory.device.chromeos.readonly'
    ].join(' ')

    auth_url = "https://accounts.google.com/o/oauth2/v2/auth?" +
    {
        client_id: Rails.application.credentials.google_workspace[:client_id],
        redirect_uri: oauth2callback_url,
        response_type: 'code',
        scope: scope,
        access_type: 'offline',
        prompt: 'consent',
        state: state_data
    }.to_query

    log_event set_event_params('get auth url', nil, { client: SERVICE, url: auth_url })
    return auth_url
  end

  def get_authorizer
    client_id = Google::Auth::ClientId.new(
      Rails.application.credentials.google_workspace[:client_id], 
      Rails.application.credentials.google_workspace[:client_secret]
    )
    scope = [
      'https://www.googleapis.com/auth/admin.directory.device.chromeos'
    ]
    authorizer = Google::Auth::WebUserAuthorizer.new(client_id, scope, nil, oauth2callback_url)
    log_event set_event_params('get authorizer', nil, { client: SERVICE, authorized: authorizer })

    return authorizer
  end

  def fetch_all_mobile_devices(next_page_token)
    customer_id = 'my_customer'

    result = client.list_mobile_devices(
      customer_id,
      projection: 'FULL',
      page_token: next_page_token
    )
    log_event set_event_params('get_mobile_devices', result, { client: SERVICE, mobile_devices: result })

    return result
  rescue => e
    log_event set_event_params('get_mobile_devices', result, { client: SERVICE, mobile_devices: result }, error_messages_detail(e))
    raise e
  end

  def fetch_all_chrome_os_devices(next_page_token)
    customer_id = 'my_customer'

    result = client.list_chrome_os_devices(
      customer_id,
      projection: 'FULL',
      page_token: next_page_token
    )
    log_event set_event_params('get_chromeos_devices', result, { client: SERVICE, chrome_devices: result })

    return result
  rescue => e
    log_event set_event_params('get_chromeos_devices', result, { client: SERVICE, chrome_devices: result }, error_messages_detail(e))
  end

  def refresh_token_call
    body = {
        grant_type: 'refresh_token',
        client_id: Rails.application.credentials.google_workspace[:client_id],
        client_secret: Rails.application.credentials.google_workspace[:client_secret],
        scope: SCOPE,
        refresh_token: @config.refresh_token,
        redirect_uri: oauth2callback_url,
      }
    headers = {'Content-Type': "application/x-www-form-urlencoded"}
    response = HTTParty.post "https://www.googleapis.com/oauth2/v3/token", headers: headers, body: body
    response.parsed_response
  end

  def client
    access_token = @config.token
    service = Google::Apis::AdminDirectoryV1::DirectoryService.new
    service.authorization = access_token
    service
  end

  def oauth2callback_url
    "#{Rails.application.credentials.domain_with_port}/integrations/google_workspace/oauth2callback"
  end
end
