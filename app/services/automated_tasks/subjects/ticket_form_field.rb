module AutomatedTasks
  module Subjects
    class TicketFormField
      include AutomatedTasks::Concerns::Eventing
      include HandleCompanyCacheKeys
      attr_accessor :object

      def match?(obj)
        @object = obj
        return false unless matching_form? && matching_field?
        !!((any_value? && not_empty?) || (!negate_value? && matching_value?) || (!matching_value? && negate_value?))
      end

      def matching_form?
        custom_form_ids.include?(root.custom_form_id) || custom_form_ids.include?('all')
      end

      def root
        object.is_a?(HelpTicket) ? object : object.help_ticket
      end

      def matching_field?
        if object.is_a?(CustomFormValue)
           object.custom_form_field.name == form_field['name'] &&
           object.custom_form_field.field_attribute_type == form_field['field_attribute_type']
        elsif object.is_a?(HelpTicket)
          !!selected_field
        else
          false
        end
      end

      def not_empty?
        if object.is_a?(HelpTicket)
          !!form_values.find_by(custom_form_field_id: selected_field.id)&.value
        else
          true
        end
      end

      def selected_field
        @selected_field ||= root.custom_form.custom_form_fields.find_by(name: form_field['name'], field_attribute_type: form_field['field_attribute_type'])
      end

      def any_value?
        value['any']
      end

      def negate_value?
        value['negate']
      end

      def is_partial_match?
        value['partial_match']
      end

      def form_field
        value['form_field']
      end

      def custom_forms
        value['custom_forms']
      end

      def custom_form_ids
        @custom_form_ids ||= custom_forms ?  custom_forms.map { |f| f['id'] } : []
      end

      def form_values
        @form_values ||= begin
          root.custom_form_values.joins(:custom_form_field).where(custom_form_fields: { name: form_field['name'], field_attribute_type: form_field['field_attribute_type'] })
        end
      end

      def matching_value?
        return form_values.present? && form_values.pluck(:value_str).sort == value["text"].sort if special_field_attribute_type?

        matching = false
        form_values.each do |form_value|
          field = form_value.custom_form_field
          expected = subject_value(field)
          matching ||= case field.field_attribute_type
                       when "text"
                          form_value.value_str.include?(expected) if expected.present?
                       when "text_area"
                          form_value.value_str.include?(expected) if expected.present?
                       when "rich_text"
                          form_value.value_str.include?(expected) if expected.present?
                       when "number"
                          my_value = form_value.value_str.to_f
                          expected == my_value
                       when "list"
                          form_value.value_str == expected
                       when "radio_button"
                          form_value.value_str == expected
                       when "category"
                          form_value.value_str == expected
                       when "department"
                          form_value.value_str == expected
                       when "people_list"
                          expected.each do |v|
                            matching ||= (is_partial_match? ? check_partial_matches(form_value.value_int) : v == form_value.value_int)
                          end
                          matching
                       when "asset_list"
                          expected.each do |v|
                            matching ||= v == form_value.value_int
                          end
                          matching
                       when "contract_list"
                          expected.each do |v|
                            matching ||= v == form_value.value_int
                          end
                          matching
                       when "vendor_list"
                          expected.each do |v|
                            matching ||= v == form_value.value_int
                          end
                          matching
                       when "telecom_list"
                          expected.each do |v|
                            matching ||= v == form_value.value_int
                          end
                          matching
                       when "location_list"
                          expected.each do |v|
                            matching ||= v == form_value.value_int
                          end
                          matching
                       when "attachment"
                          expected.each do |v|
                            matching ||= v['filename'] == form_value.value_str
                          end
                          matching
                       when "date"
                          form_value.value_str == expected
                       when "phone"
                          form_value.value_str == expected
                       when "priority"
                          form_value.value_str == expected
                       when "status"
                          form_value.value_str == expected
                       when "external_user"
                          (form_value.value_int && form_value.value_int == expected["id"]) || 
                          (form_value.value_str && form_value.value_str == expected["name"])
                       when "email"
                          form_value.value_str == expected
                       end
          # Short circuit the matching
          return matching if matching
        end
        matching
      end

      def subject_value(field)
        case field.field_attribute_type
        when "text"
          value["text"]
        when "text_area"
          value["text"]
        when "rich_text"
          value["text"]
        when "number"
          value["text"].to_f
        when "category"
          value["text"]
        when "department"
          value["text"]
        when "list"
          value["text"]
        when "radio_button"
          value["text"]
        when "people_list"
          is_partial_match? ? [value['partialValue']] : value['contributors']
        when "asset_list"
          value["assets"]
        when "contract_list"
          value["contracts"]
        when "vendor_list"
          value["vendors"]
        when "telecom_list"
          value["telecoms"]
        when "location_list"
          value["locations"]
        when "attachment"
          value["attachments"]
        when "date"
          value["date"]
        when "phone"
          value["phone"]
        when "priority"
          value["priority"]
        when "status"
          value["status"]
        when "external_user"
          value["text"]
        when "email"
          value["text"]
        end
      end

      def special_field_attribute_type?
        ["checkbox", "tag"].include?(form_field['field_attribute_type'])
      end

      def check_partial_matches(contributor_id)
        contributor = find_contributor_by_id(contributor_id)
        return false unless contributor.present?

        (contributor.contributor_type == 'Group' && contributor.name.downcase.include?(value['partial_value'].downcase)) ||
        contributor.email.include?(value['partial_value'])
      end
    end
  end
end
