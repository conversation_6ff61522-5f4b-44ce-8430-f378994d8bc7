module AutomatedTasks
  module Actions
    class SendEmail < HelpTicketAction
      include AutomatedTasks::Concerns::Activity
      include AutomatedTasks::Concerns::ScanMention
      include ContributorOptionsHelper
      include HandleCompanyCacheKeys
      include HelpDesk::EmailBlocking

      def call
        check_sent_emails(object.company)
        EmailActionService.new(root, action, recipients).call
        create_recepients_activity("Email", @recipients_name.uniq) if object.class.name == 'HelpTicket'
      end

      def targets
        json_value['target']&.split(",")
      end

      def json_value
        @json_data ||= JSON.parse(action.value)
      end

      # "assigned", "agents", 'related', 'specified', 'ticket_creator', 'groups'
      def recipients
        cached_data = if is_cache_enabled?('send_email_recipients_cache')
                        key = { name: 'task_recipients', action_id: action.id }
                        Rails.cache.fetch(key, expires_in: 8.hours) do
                          collect_recipients_and_emails(%w[agents admins contributors specified])
                        end
                      else
                        collect_recipients_and_emails(%w[agents admins contributors specified])
                      end
        additional_data = collect_recipients_and_emails(%w[assigned ticket_creator form_field mentioned private related impacted])
        emails = filter_recipients(cached_data[:emails] + additional_data[:emails], 'email')
        if private_contributor_emails.present?
          emails &= private_contributor_emails
          @recipients_name = emails
        else
          @recipients_name = (cached_data[:recipients_name] + additional_data[:recipients_name]).compact_blank
        end
        emails
      end

      def collect_recipients_and_emails(target_list)
        recipients_name = []
        emails = []
        targets.each do |target|
          next unless target_list.include?(target)

          recipients_name << recipients_target_name[target.to_sym] unless ['private', 'contributors'].include?(target)
          emails += case target
                    when "agents"
                      agent_emails
                    when "admins"
                      admin_emails
                    when "contributors"
                      process_contributors(recipients_name)
                    when "form_field"
                      specific_field_email
                    when "assigned"
                      assigned_emails
                    when "ticket_creator"
                      creator_emails
                    when "mentioned"
                      mentioned_emails
                    when "private"
                      recipients_name << private_contributor_emails.join(', ')
                      private_contributor_emails
                    when "related"
                      recipients_name << "Users related to the ticket"
                      related_emails
                    when "impacted"
                      followers_emails
                    else
                      recipients_name << "Specified user #{json_value['recipients']}"
                      specified_users(json_value['recipients'].split(','))
                    end
        end

        { emails: emails, recipients_name: recipients_name }
      end

      def process_contributors(recipients_name)
        contributor_ids = []
        if json_value["contributors"].is_a?(Array)
          contributor_ids = json_value["contributors"].map { |cont| cont['id'] }
          recipients_name.concat(json_value["contributors"].map { |cont| cont['name'] })
        elsif json_value["contributors"] && json_value["contributors"]["id"]
          contributor_ids << json_value["contributors"]["id"]
          recipients_name << json_value["contributors"]["name"]
        end
        member_emails(contributor_ids) if contributor_ids.present?
      end

      def private_contributor_emails
        @private_contributor_emails ||= begin
          root&.has_attribute?(:private_flag) && root.private_flag ? member_emails(root.private_contributor_ids) : []
        end
      end

      def mentioned_emails
        con_ids = extract_contributor_ids_from_mentions
        return [] unless con_ids.present?

        contributors = Contributor.where(id: con_ids)
        contributor_ids = contributors.flat_map { |c| c.contributor_ids_only_users }
        company_users_emails(contributor_ids)
      end

      def assigned_emails
        value_ints = object.assigned_user_ids
        return [] unless value_ints.present?

        contributors = Contributor.where(id: value_ints)
        contributor_ids = contributors.flat_map { |c| c.contributor_ids_only_users }
        assigned_user_emails = company_users_emails(contributor_ids)
        guest_emails = Guest.where(contributor_id: contributor_ids).pluck(:email)
        assigned_user_emails | guest_emails
      end

      def related_emails
        people_ids = object.related_people_ids.concat(object.related_group_ids)
        return [] unless people_ids.present?

        contributors = Contributor.where(id: people_ids)
        contributor_ids = contributors.flat_map { |c| c.contributor_ids_only_users }
        field_emails = company_users_emails(contributor_ids)
        guest_emails = Guest.where(contributor_id: contributor_ids).pluck(:email)
        field_emails | guest_emails
      end

      def followers_emails
        followers_ids = object.custom_form_values
                          .joins(:custom_form_field)
                          .where(module_id: object.id)
                          .where("custom_form_fields.is_followers = true OR custom_form_fields.name = ?", 'followers')
                          .pluck(:value_int)
                          .uniq
        return [] unless followers_ids.present?

        contributors = Contributor.where(id: followers_ids)
        contributor_ids = contributors.flat_map { |c| c.contributor_ids_only_users }
        user_emails = company_users_emails(contributor_ids)
        guest_emails = Guest.where(contributor_id: contributor_ids).pluck(:email)
        user_emails | guest_emails
      end

      def creator_emails
        value_ints = object.creator_ids
        return [] unless value_ints.present?

        contributors = Contributor.where(id: value_ints)
        contributor_ids = contributors.flat_map { |c| c.contributor_ids_only_users }
        creator_user_emails = company_users_emails(contributor_ids)
        guest_emails = Guest.where(contributor_id: contributor_ids).pluck(:email)
        creator_user_emails | guest_emails
      end

      def specific_field_email
        if (json_value["selected_custom_forms"][0]["id"] != "all")
          ids = json_value["selected_custom_forms"].map { |form| form["id"] }
          if !ids.include?(object.custom_form.id)
            return []
          end
        end
        field_name = json_value["custom_field"]&.dig("name")
        if field_name == "assigned_to"
          return assigned_emails
        elsif field_name == "created_by"
          return creator_emails
        else
          emails = []
          custom_field_id = object.custom_form_fields.find_by(name: field_name)&.id
          custom_form_values = object.custom_form_values.where(custom_form_field_id: custom_field_id).includes(contributor: [:group, :company_user, :guest])
          custom_form_values.each_with_index do |custom_form_value, index|
            if custom_form_value.contributor&.group.present?
              group_contributors = custom_form_value.contributor.contributor_ids_only_users   
              contributors = Contributor.where(id: group_contributors).includes(:company_user, :guest)
              contributors.each do |contributor|
                if contributor.company_user.present?
                  emails << contributor.company_user.email
                elsif contributor.guest.present?
                  emails << contributor.guest.email
                end
              end
            elsif custom_form_value.contributor&.company_user.present?
              emails << custom_form_value.contributor.company_user.email
            elsif custom_form_value.contributor&.guest.present?
              emails << custom_form_value.contributor.guest.email
            end
          end
          emails
        end
      end

      def admin_emails
        object.company.admin_company_users.where(out_of_office: false).joins(:user).pluck(:email)
      end

      def agent_emails
        workspace = object.is_a?(AutomatedTasks::ExecutionDate) ? object.automated_task.workspace : object.workspace
        workspace.workspace_agent_company_users.where(out_of_office: false).joins(:user).pluck(:email)
      end

      def specified_users(specified_emails)
        emails = []

        users = User.where(email: specified_emails).includes(:company_users)
        company_users = users.each_with_object({}) do |user, hash|
          company_user = user.company_users.find { |cu| cu.company_id == object.company_id }
          hash[user.email.downcase] = company_user
        end

        specified_emails.each do |email|
          cu = company_users[email.downcase]
          if cu.present?
            emails << cu.email if cu.out_of_office == false
          else
            emails << email
          end
        end
        emails
      end

      def company_users_emails(contributor_ids)
        sql = """
        SELECT
          users.email
        FROM
          company_users
        INNER JOIN users
          ON users.id = company_users.user_id
        WHERE company_users.contributor_id IN (?)
          AND company_users.out_of_office = false
        """
        query = ActiveRecord::Base.send(:sanitize_sql, [sql, contributor_ids])
        results = ActiveRecord::Base.connection.execute(query).to_a
        results.map{ |r| r['email'] }
      end
    end
  end
end
