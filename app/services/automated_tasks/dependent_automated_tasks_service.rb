module AutomatedTasks
  class DependentAutomatedTasksService
    include ContributorHelper

    attr_accessor :workspace_ids, :contributor_ids, :company_users, :is_archive_modal, :delete_users, :is_bulk_delete, :is_group_delete

    def initialize(workspace_ids, contributor_ids, company_users, is_archive_modal, delete_users = false, is_bulk_delete = false, is_group_delete = false, company_id = nil)
      @workspace_ids = workspace_ids
      @contributor_ids = contributor_ids
      @company_users= company_users
      @is_archive_modal = is_archive_modal
      @delete_users = delete_users
      @is_bulk_delete = is_bulk_delete
      @is_group_delete = is_group_delete
      @company_id = company_id
    end

    def call(is_asset_query = false)
      process_helpdesk_automations
      process_assets_automations
      [@helpdesk_dependent_tasks, @assets_dependent_tasks]
    end

    def process_helpdesk_automations
      @dependent_tasks = []
      AutomatedTasks::AutomatedTask.includes(task_actions: :action_type).where(workspace_id: workspace_ids).each do |automated_task|
        process_tasks(automated_task)
      end
      @helpdesk_dependent_tasks = @dependent_tasks
    end

    def process_assets_automations
      @dependent_tasks = []
      Assets::AutomatedTask.includes(task_actions: :action_type).where(company_id: @company_id).each do |automated_task|
        process_tasks(automated_task)
      end
      @assets_dependent_tasks = @dependent_tasks
    end

    def process_tasks(automated_task)
      automated_task.task_actions.each do |action|
        task_value = JSON.parse(action.value)
        if is_bulk_delete || is_group_delete
          user_contributor_ids = contributor_ids
        else
          user_contributor_ids = is_archive_modal ? company_users.pluck(:contributor_id) : [contributor_ids]
        end
        contributor_values = extract_contributor_values(task_value)
        matching_contributor_ids = contributor_values & user_contributor_ids
        if contributor_values.present? && matching_contributor_ids.any?
          if delete_users
            if contributor_values.count > 1
              if action.action_type.action_class == "SetFormField"
                is_bulk_delete ? contributor_values.reject! { |value| contributor_ids.include?(value) } : contributor_values.delete(contributor_ids)
              elsif ["AddAlert", "SendSms", "SendEmail"].include?(action.action_type.action_class)
                task_value["contributors"].reject! { |contributor| matching_contributor_ids.include?(contributor["id"]) }
              elsif action.action_type.action_class == "AddTicket"
                field_hash = {}
                field_ids = task_value["values"].map { |val| val["custom_form_field_id"] if val["value_int"].present? }.compact.uniq
                fetched_fields = CustomFormField.where(id: field_ids).group_by(&:id)
                user_counts = task_value["values"].pluck('custom_form_field_id').group_by(&:itself)

                task_value["values"].delete_if do |value|
                  field_id = value['custom_form_field_id']
                  next if value["value_int"].nil?

                  field_hash[field_id] = fetched_fields[field_id].first if field_hash[field_id].nil?
                  if !field_hash[field_id].required && matching_contributor_ids.include?(value["value_int"])
                    true
                  elsif field_hash[field_id].required && matching_contributor_ids.include?(value["value_int"])
                    disable_task(automated_task) if user_counts[field_id].count == 1
                    true
                  end
                end
              end
              action.value = task_value.to_json
              action.save!
            else
              disable_task(automated_task)
            end
          elsif is_archive_modal
            @dependent_tasks << {
              user: matching_company_users(matching_contributor_ids).pluck('id'),
              automated_tasks: automated_task
            }
          else
            @dependent_tasks << automated_task
          end
        end
      end
      @dependent_tasks
    end

    def matching_company_users(contributors_ids)
      company_users.select { |company_user| contributors_ids.include?(company_user.contributor_id) }
    end

    def base_query
      if @is_asset_query
        Assets::AutomatedTask.includes(task_actions: :action_type).where(company_id: @company_id)
      else
        AutomatedTasks::AutomatedTask.includes(task_actions: :action_type).where(workspace_id: workspace_ids)
      end
    end

    def disable_task(automated_task)
      automated_task.disabled_at = Time.zone.now
      automated_task.force_disabled = true
      automated_task.save!
    end
  end
end
