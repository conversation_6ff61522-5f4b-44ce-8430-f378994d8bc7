module AutomatedTasks
  module Concerns
    module Eventing
      include ReadReplicaDb

      attr_accessor :detail

      def initialize(detail)
        self.detail = detail
      end

      ##
      # Creating the match and subjects
      def subject(detail)
        type_name =
          if is_ticket_same_state_event(detail) && is_form_field_subject(detail)
            "TicketRemainsInSameState"
          else
            type_name = detail.event_subject_type.subject_class
          end
        "AutomatedTasks::Subjects::#{type_name}".constantize.new(detail)
      end

      def is_ticket_same_state_event(detail)
        detail.task_event.event_type.event_class == 'TicketRemainsInSameState'
      end

      def is_form_field_subject(detail)
        detail.event_subject_type.subject_class == 'TicketFormField'
      end

      def event_match?(object)
        detail.event_details.map { |detail| subject(detail).match?(object) }.reduce(:&)
      end

      def subject_match?(object)
        detail.children.map { |child| subject(child).match?(object) }.reduce(:&)
      end

      def value
        return nil unless detail.value
        JSON.parse(detail.value)
      end

      def pending_tasks(cfv)
        @pending_tasks ||= set_read_replica_db do
          cfv.scheduled_automated_tasks
              .where(status: 'pending', automated_tasks_automated_task_id: task_id)
        end
      end

      def task_id
        @task_id ||= detail.task_event.automated_task_id
      end
    end
  end
end
