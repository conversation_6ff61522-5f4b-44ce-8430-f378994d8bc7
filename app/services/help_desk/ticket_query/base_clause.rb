module HelpDesk
  module TicketQuery
    class BaseClause
      attr_accessor :params

      VALID_ORDERINGS = %w{
        ticket_number
        subject
        assigned_to
        company_name
        comment_count
        status
        priority
        created_by
        location
      }.map{ |col| ["#{col} desc", "#{col} asc"]}.flatten

      def initialize(params)
        self.params = params
      end

      def sanitize(args)
        ActiveRecord::Base.send(:sanitize_sql, args)
      end

      def method_missing(method_name)
        params[method_name]
      end

      def call(base)
        base
      end
    end
  end
end
