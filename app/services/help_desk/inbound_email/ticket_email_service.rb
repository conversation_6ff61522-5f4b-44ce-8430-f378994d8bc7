module HelpDesk
  module InboundEmail
    class TicketEmailService
      include EmailLogging
      include HandleCompanyCacheKeys

      attr_accessor :company, :ticket_emails, :ticket_email, :custom_form_id, :params, :email_from

      def initialize(company, ticket_emails = nil, custom_form_id = nil, params: {})
        self.params = params
        @email_from = params[:from]
        @company = company
        @ticket_emails = ticket_emails
        @custom_form_id = custom_form_id
      end

      def create_tickets_from_emails(origin = nil)
        @ticket_emails.each do |ticket_email|
          @ticket_email = ticket_email
          if ticket_email.ticket_created
            create_log("ticket_already_created", ticket_email.help_ticket_id, source: origin)
          else
            ticket_creation(origin)
            ticket_email.help_ticket = @help_ticket
            ticket_email.mark_ticket_created
            ticket_email.save
            create_log("valid_email", @help_ticket.id, followers: recipients(ticket_email), attachments: attachment_values)
          end
        end
      rescue Exception => e
        create_log("invalid_email", error: e, source: origin)
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
        Bugsnag.notify(e) if Rails.env.staging? || Rails.env.production?
        raise e
      end

    def recipients(ticket_email)
      followers_arr = []
      cc_arr = []

      if recipients_users_exists?(ticket_email)
        ticket_email.recipients.map(&:downcase).each do |recipient|
          followers_arr << { email: recipient }
        end
      end
      cc_arr << {
        recipients: followers_arr.uniq,
        allow_making_recipients_follower: allow_making_recipients_follower?
      }

      cc_arr
    end

      def create_ticket(origin = nil)
        values = {
          subject: ticket_email.subject,
          description: ticket_description(ticket_email),
          created_by: creator ? creator.contributor_id : ticket_email.from_email,
          followers: followers(ticket_email),
          attachments: attachment_values,
        }

        ticket_attr = { message_id: ticket_email.message_id, email: ticket_email.from_email, source: :email , secondary_source: :incoming_tab }
        ticket_attr[:secondary_source] = :direct_email if origin == "ticket_create_from_email"
        HelpDesk::SimpleEntity.new('helpdesk', @company, creator, workspace: form.workspace, form: form, entity_attr: ticket_attr).create_entity(values)
      end

      def attachment_values
        # we can only save attachments if there is an attachment field
        # in the custom form
        attachments = []
        valid_extensions = valid_email_extensions(ticket_email.workspace_id)
        ticket_email.ticket_email_attachments.each do |attachment|
          next unless valid_extension?(attachment, valid_extensions)

          attachment_params = { attachment: attachment.attached_file.blob, content_id: attachment.content_id, company: @company }
          attachment_params[:skip_attachment] = attachment.is_hidden
          if skip_attachments(attachment, ticket_email)
            attachment_params[:auto_add] = true
          end
          attachments << attachment_params
        end
        attachments
      end

      def ticket_id
        @help_ticket.id
      end

      def form
        @form ||= begin
          if custom_form_id.present?
            @company.custom_forms.find_by(id: custom_form_id)
          else
            email_addresses = @ticket_email.to
            custom_email = nil

            HelpdeskCustomEmail.where(email: email_addresses).find_each do |e|
              custom_email ||= e if e&.helpdesk_custom_form&.custom_form&.default
            end

            custom_email ||= HelpdeskCustomEmail.where(email: email_addresses).order(:created_at).first
            
            if custom_email
              custom_email.custom_form
            else
              helpdesk_form = HelpdeskCustomForm.find_by(email: email_addresses, default: true)
              helpdesk_form ||= HelpdeskCustomForm.where(email: email_addresses).order(:created_at).first
              
              if helpdesk_form
                helpdesk_form.custom_form
              else
                @company.custom_forms.find_by(company_module: "helpdesk", default: true) ||
                  @company.custom_forms.where(company_module: "helpdesk").order(:created_at).first
              end
            end
          end
        end
      end

      def created_by
        form.custom_form_fields.find_by(name: "created_by")
      end

      def creator
        @creator ||= company_user_cache(@company, @ticket_email.from_email)
      end

      def status
        form.custom_form_fields.find_by(name: "status")
      end

      def priority
        form.custom_form_fields.find_by(name: "priority")
      end

      def description
        @description ||= form.custom_form_fields.find_by(name: "description")
        @description ||= form.custom_form_fields.order('order_position').where(field_attribute_type: "rich_text").first
      end

      def attachment_field
        form.custom_form_fields.find_by(name: "attachments")
      end

      def subject
        form.custom_form_fields.find_by(name: "subject")
      end

      def assigned_to
        form.custom_form_fields.find_by(name: "assigned_to")
      end

      def followers(ticket_email)
        followers_arr = []
        if followers_field.present?
          if allow_making_recipients_follower? && recipients_users_exists?(ticket_email)
            ticket_email.recipients.map(&:downcase).each do |recipient|
              followers = company_user_cache(@company, recipient)
              if followers.present?
                followers_arr << followers.contributor_id
              elsif followers_field.audience == "guests" && !is_helpdesk_email?(recipient)
                guest = @company.guests.find_or_create_by(email: recipient, workspace: form.workspace)
                followers_arr << guest.contributor_id
              end
            end
          end
          if followers_field.default_value.present?
            parsed_values = JSON.parse(followers_field.default_value)
            followers_arr += parsed_values
          end 
        end
        followers_arr.uniq
      end

      def is_helpdesk_email? recipient
        excluded_emails = company.helpdesk_custom_emails.pluck(:email)
        excluded_emails << form.helpdesk_custom_form.email
        excluded_emails.include? recipient
      end

      def followers_field
        form.custom_form_fields.find_by(name: "followers") || form.custom_form_fields.find_by(is_followers_field: true)
      end

      def allow_making_recipients_follower?
        recipients_follower_setting = DefaultHelpdeskSetting.find_by(setting_type: 'allow_making_email_recipients_ticket_followers')
        @company.helpdesk_settings.where(default_helpdesk_setting_id: recipients_follower_setting.id, enabled: true).present?
      end

      def recipients_users_exists? ticket_email
        ticket_email.recipients.present? && ticket_email.recipients.length > 0
      end

      def skip_attachments(attachment, ticket_email)
        (attachment.content_id.present? && 
          ticket_email.body_html&.include?(attachment.content_id)) || 
        (attachment.name.present? && 
          ticket_email.body_html&.include?(attachment.name))
      end

      def ticket_description(ticket_email)
        if ticket_email.body_text.include?('--Apple-Mail=')
          return 'Sorry, we encountered an issue while decoding your data'
        end
        return ticket_email.body_html || ticket_email.body_text
      end

      def method_missing(method_name)
        params[method_name]
      end

      def respond_to_missing?(method_name, include_private = false)
        super
      end

      def ticket_creation(origin = nil)
        max_retries = 3
        retries = 0
      
        begin
          ActiveRecord::Base.transaction do
            @help_ticket = create_ticket(origin)
          end
          ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
        rescue ActiveRecord::RecordNotUnique => e
          if e.message.include?("index_help_tickets_on_ticket_number_and_company_and_workspace")
            retries += 1
            if retries <= max_retries
              retry
            else
              raise "Ticket number conflict occurred, retries exceeded"
            end
          else
            raise e
          end
        end
      end
    end
  end
end
