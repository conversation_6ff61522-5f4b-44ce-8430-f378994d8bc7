class Integrations::GoogleWorkspace::ConfigsController < ApplicationController
  require 'google/apis/admin_directory_v1'
  
  include ApplicationHelper
  include IntegrationHelper
  include ReadReplicaDb

  def consent
    company_id = current_company.id
    state_data = {company_id: company_id, import_type: params[:import_type]}.to_query
    service = google_workspace_service(company_id)
    auth_url = service.get_credentials(state_data)
    render json: {url: auth_url}
  end

  def callback
    if params[:state].present?
      state_data = Rack::Utils.parse_nested_query(params[:state])
      import_type = state_data["import_type"]
      company_id = state_data["company_id"]
    end

    company = Company.find_by_cache(id: company_id)
    google_workspace_redirect_path = "#{build_company_url(company)}managed_assets/discovery_tools/connectors"

    if params[:code].present?
      service = google_workspace_service(company.id)
      authorizer = service.get_authorizer
      credentials = authorizer.get_credentials_from_code(
        user_id: 'admin_user',
        code: params[:code],
        base_url: oauth2callback_url
      )

      google_workspace = Integrations::GoogleWorkspace::Config.find_or_initialize_by(company_id: company.id)
      google_workspace.assign_attributes(token: credentials.access_token,
                                         refresh_token: credentials.refresh_token,
                                         expires_at: credentials.expires_at,
                                         import_type: import_type,
                                         company_user_id: current_company_user&.id)

      google_workspace.save!
    end

    redirect_to google_workspace_redirect_path
  end

  def destroy
    google_workspace_config = Integrations::GoogleWorkspace::Config.find_by_id(params[:id])

    if google_workspace_config&.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    google_workspace_config = Integrations::GoogleWorkspace::Config.find_by_id(params[:id])
    if google_workspace_config
      google_workspace_config.company_integration.update!(active: false, status: false)
      render json: { message: "Integration deactivated successfully" }, status: :ok
    end
  end

  def update_import_type
    google_workspace_config = set_read_replica_db do
      Integrations::GoogleWorkspace::Config.find_by_id(params[:id])
    end

    if google_workspace_config
      google_workspace_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  private

  def oauth2callback_url
    "#{Rails.application.credentials.domain_with_port}/integrations/google_workspace/oauth2callback"
  end

  def google_workspace_service(company_id)
    Integrations::GoogleWorkspace::FetchData.new(company_id)
  end
end
