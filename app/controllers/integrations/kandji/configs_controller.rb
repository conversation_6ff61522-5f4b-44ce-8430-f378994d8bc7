class Integrations::Kandji::ConfigsController < Assets::BaseController
  include IntegrationHelper
  include ReadReplicaDb

  before_action :set_resource, only: [:show, :destroy, :deactivate]

  def create
    kandji_config = Integrations::Kandji::Config.find_or_initialize_by(company_id: current_company.id)
    kandji_config.assign_attributes(auth_params)
    kandji_config.company_user_id = current_company_user&.id
    if kandji_config.save
      render json: { message: 'Kandji integrated successfully' }, status: :ok
    else
      render json: { message: kandji_config.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def show
    render json: { config: @kandji_config }, status: :ok
  end

  def destroy
    company_integration = @kandji_config.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    if @kandji_config.company_integration.update(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: 'Integration deactivated successfully' }, status: :ok
    else
      render json: { message: "Sorry, there was an error deactivating. Please try again" }, status: :unprocessable_entity
    end
  end

  def update_import_type
    kandji_config = set_read_replica_db do
      Integrations::Kandji::Config.find_by_id(params[:id])
    end

    if kandji_config
      kandji_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  private

  def auth_params
    params.require(:kandji_credentials).permit(:api_url, :api_token, :import_type)
  end

  def set_resource
    @kandji_config = Integrations::Kandji::Config.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { message: 'Config was not found.' }, status: :not_found }
    end
  end
end
