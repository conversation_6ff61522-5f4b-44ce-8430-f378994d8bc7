class Integrations::GoogleAssets::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper

  before_action :set_resource, only: [:show, :destroy, :deactivate, :update_import_type]

  def create
    current_company_config.assign_attributes(project_attributes)
    current_company_config.company_user_id = current_company_user&.id
    if current_company_config.save
      render json: {}, status: :ok
    else
      render json: { message: current_company_config.errors.full_messages.to_sentence,
                     config: current_company_config.as_json }, status: :unprocessable_entity
    end
  rescue ActiveRecord::RecordNotUnique
    render json: { message: "Please exclude the duplicate projects" }, status: :conflict
  end

  def show
    render json: { config: @google_assets_config.as_json }, status: :ok
  end

  def destroy
    company_integration = @google_assets_config.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    @google_assets_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
    render json: { message: "Integration deactivated successfully" }, status: :ok
  end

  def update_import_type
    if @google_assets_config
      @google_assets_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  private

  def current_company_config
    @current_company_config ||= Integrations::GoogleAssets::Config.find_or_initialize_by(company_id: current_company.id)
  end
  
  def project_attributes
    params.require(:google_assets_config).permit(projects_attributes: :project_id, import_type: :import_type)
  end

  def set_resource
    @google_assets_config = Integrations::GoogleAssets::Config.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { message: "Config was not found." }, status: :not_found }
      format.html { render "shared/not_found", status: :not_found }
    end
  end
end
