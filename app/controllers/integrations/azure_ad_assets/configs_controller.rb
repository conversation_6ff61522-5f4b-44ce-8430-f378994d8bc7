class Integrations::AzureAdAssets::ConfigsController < Assets::BaseController
  include ApplicationHelper
  include IntegrationHelper
  include ReadReplicaDb

  require 'signet/oauth_2/client'

  before_action :authorize_write, except: [:consent_callback]
  before_action :set_company, only: :consent_callback
  before_action :set_azure_ad_assets_config, only: [:deactivate, :destroy]
  skip_before_action :ensure_access, only: [:consent_callback]
  skip_before_action :authenticate_user!, only: [:consent, :consent_callback]

  def consent
    import_type = JSON.parse(params[:state])["import_type"] if params[:state].present?
    state_data = {company_id: current_company.id, import_type: import_type}.to_query
    client = azure_ad_assets_service.client(state_data)
    redirect_to client.authorization_uri.to_s
  end

  def consent_callback
    if @current_company.present? && params[:code].present?
      token_detail = azure_ad_assets_service.token(params[:code])
      expiry_time = Time.now + (token_detail['expires_in'] - 600)
      state_data = Rack::Utils.parse_nested_query(params[:state])
      import_type = state_data["import_type"]
      company_id = state_data["company_id"]

      azure_ad_assets_config = Integrations::AzureAdAssets::Config.find_or_initialize_by(company_id: company_id)
      azure_ad_assets_config.assign_attributes(
        token: token_detail['access_token'],
        expires_in: expiry_time,
        refresh_token: token_detail['refresh_token'],
        import_type: import_type,
        company_user_id: current_company_user&.id
      )

      azure_ad_assets_config.save!
    end

    redirect_to "#{protocol}#{@current_company.subdomain}.#{Rails.application.credentials.assets_connectors_path}"
  end

  def destroy
    company_integration = @azure_ad_assets_config&.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: 'Integration deleted successfully' }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    if @azure_ad_assets_config.present?
      @azure_ad_assets_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
      render json: { message: 'Integration deactivated successfully' }, status: :ok
    else
      render json: { message: "Sorry, there was an error deactivating. Please try again" }, status: :not_found
    end
  end

  def update_import_type
    azure_ad_assets_config = set_read_replica_db do
      Integrations::AzureAdAssets::Config.find_by_id(params[:id])
    end

    if azure_ad_assets_config
      azure_ad_assets_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  private
  def azure_ad_assets_service
    company_id ||= @current_company.id
    @azure_ad_assets_service ||= Integrations::AzureAdAssets::FetchData.new(company_id)
  end

  def set_azure_ad_assets_config
    @azure_ad_assets_config ||= Integrations::AzureAdAssets::Config.find_by_id(params[:id])
  end

  def set_company
    if params[:state].present?
      state_data = Rack::Utils.parse_nested_query(params[:state])
      company_id = state_data["company_id"]
      @current_company ||= Company.find_by_cache(id: company_id) 
    end
  end
end
