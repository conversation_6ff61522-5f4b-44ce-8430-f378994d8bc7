class Integrations::GoogleDrive::ConfigsController < AuthenticatedController
  set_privilege_name 'HelpTicket'

  require 'google/apis/drive_v3'
  require 'googleauth'
  require 'googleauth/web_user_authorizer'

  before_action :authorize_write, except: [:consume]
  skip_before_action :ensure_access, only: [:consume]
  skip_before_action :authenticate_user!, only: [:consume]
  before_action :set_company, only: :consume

  SCOPE = ['https://www.googleapis.com/auth/drive.file']

  GSUITE_AUTHORIZATION_URI = 'https://accounts.google.com/o/oauth2/auth?prompt=consent'
  GSUITE_TOKEN_CREDENTIAL_URI = 'https://oauth2.googleapis.com/token'
  GSUITE_CALLBACK = "#{Rails.application.credentials.domain_with_port}/integrations/google_drive/oauth2callback"

  def authorize
    data = {
      company_user_id: scoped_company_user.id,
      company_id: current_company.id
    }.to_json

    client = Signet::OAuth2::Client.new(
      authorization_uri: GSUITE_AUTHORIZATION_URI,
      token_credential_uri:  GSUITE_TOKEN_CREDENTIAL_URI,
      client_id: creds[:client_id],
      client_secret: creds[:client_secret],
      scope: SCOPE,
      redirect_uri: GSUITE_CALLBACK,
      state: data
    )
    redirect_to client.authorization_uri.to_s
  end

  def consume
    return redirect_to root_path unless params[:code]

    client = Signet::OAuth2::Client.new(
      token_credential_uri: GSUITE_TOKEN_CREDENTIAL_URI,
      client_id: creds[:client_id],
      client_secret: creds[:client_secret],
      redirect_uri: GSUITE_CALLBACK,
      code: params[:code]
    )

    response = client.fetch_access_token
    gsuite_redirect_path = "#{build_company_url(current_company)}help_tickets/articles"
    if response['access_token'].present?
      Rails.cache.write(state_data['company_user_id'], response['access_token'], expires_in: 4.minute, skip_nil: true)
      gsuite_redirect_path = "#{gsuite_redirect_path}?showModal=true"
    else
      Bugsnag.notify(response) if Rails.env.staging? || Rails.env.production?
    end
    
    redirect_to gsuite_redirect_path, flash: {}
  end
  
  def get_credentials
    token = Rails.cache.read(scoped_company_user.id)
    return render json: {}, status: :ok if token.blank?

    # Deleting cache here because we want to use access token only one time
    Rails.cache.delete(scoped_company_user.id)

    render json: {
      client_id: creds[:client_id],
      developerKey: creds[:developer_key],
      token: token,
    }, status: :ok
  end

  private

  def set_company
    return redirect_to root_path if state_data.blank?

    @current_company = Company.find_by_cache(id: state_data['company_id'])
  end

  def state_data
    @state_data ||= JSON.parse(params[:state])
  end

  def creds
    @creds ||= {
      client_id: Rails.application.credentials.google_drive[:client_id],
      client_secret: Rails.application.credentials.google_drive[:client_secret],
      developer_key: Rails.application.credentials.google_drive[:developer_key],
    }
  end
end
