class Integrations::AwsAssets::ConfigsController < Assets::BaseController
  include Regions
  include IntegrationHelper

  before_action :set_resource, only: [:show, :destroy, :deactivate, :update_import_type]

  def create
    aws_assets_config = Integrations::AwsAssets::Config.find_or_initialize_by(company_id: current_company.id)
    aws_assets_config.assign_attributes(auth_params)
    aws_assets_config.company_user_id = current_company_user&.id
    if aws_assets_config.save
      render json: { message: "AWS integrated successfully" }, status: :ok
    else
      render json: { message: aws_assets_config.errors.full_messages.to_sentence }, status: :bad_request
    end
  end

  def show
    render json: { config: @aws_assets_config }, status: :ok
  end

  def destroy
    company_integration = @aws_assets_config.company_integration
    if company_integration
      company_integration.company_user_id = current_company_user&.id
      company_integration.destroy
      render json: { message: "Integration deleted successfully" }, status: :ok
    else
      render json: {}, status: :unprocessable_entity
    end
  end

  def deactivate
    return unless @aws_assets_config

    @aws_assets_config.company_integration.update!(active: false, status: false, company_user_id: current_company_user&.id)
    render json: { message: "Integration deactivated successfully" }, status: :ok
  end

  def aws_regions
    render json: { aws_regions: Regions::AWSREGIONS }, status: :ok
  end

  def update_import_type
    if @aws_assets_config
      @aws_assets_config.update_column(:import_type, params[:import_type])
      render json: { message: "Integration import place updated successfully" }, status: :ok
    end
  rescue => e
    render json: { message: "Failed to update import place: #{e.message}" }, status: :unprocessable_entity
  end

  private

  def auth_params
    params.require(:aws_credentials).permit(:access_key, :secret_key, :import_type, regions: [])
  end

  def set_resource
    @aws_assets_config = Integrations::AwsAssets::Config.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { message: "Config was not found." }, status: :not_found }
      format.html { render "shared/not_found", status: :not_found }
    end
  end
end
