require 'base64'

class Oauth2<PERSON><PERSON>roller < ApplicationController
  before_action :set_client
  include Devise::Controllers::SignInOut
  include HandleCompanyCacheKeys

  def consume_code
    # will work only for development environment, because development cognito client redirects without secure domain
    @is_manual_login = params.as_json.with_indifferent_access[:is_manual_login].present?
    if @is_manual_login
      access_token = params.as_json.with_indifferent_access[:access_token]
      refresh_token = params.as_json.with_indifferent_access[:refresh_token]
    else
      redirect_to root_path and return unless params[:code].present?

      redirect_to consume_code_url(code: params[:code], host: secure_host, params: {state: params["state"]}) and return unless is_secure_subdomain?

      token_headers = {'Authorization' => "Basic " + Base64.strict_encode64("#{Rails.application.credentials.aws[:cognito][:client_id]}:#{Rails.application.credentials.aws[:cognito][:client_secret]}")}

      token_endpoint = @client.auth_code.get_token(params[:code],
                                                   redirect_uri: Rails.application.credentials.aws[:cognito][:redirect_uri],
                                                   headers: token_headers)
      access_token = token_endpoint.token
      refresh_token = token_endpoint.refresh_token
    end
    user_info = CognitoService.new().get_user(access_token)
    @email_attribute = email = user_info.user_attributes.find{|key| key.name == "email"}
    @cognito_sub = user_info.user_attributes.find{|key| key.name == "sub"}.value
    if (user_info.user_attributes[1].value.include?("Google") && params["state"]&.include?("invited")) || user_info.user_attributes[1].value.include?("SAML")
      @user = User.joins(company_users: :company).where(company_users: { archived_at: nil }).find_by(email: @email_attribute.value.downcase)
    else
      @user = User.joins(company_users: :company).where(company_users: { archived_at: nil }).where('granted_access_at is NOT NULL').find_by(email: @email_attribute.value.downcase)
    end

    if @user.present?
      @user.update_columns(access_id: access_token, refresh_id: refresh_token)
      sign_in(:user, @user)
      track_sign_in

      update_user_guid(user_info)

      if user_info.user_attributes[1].value.include?("SAML") && !params["state"]&.include?("invited")
        @user.update_columns(has_confirmed_email: true) unless @user.has_confirmed_email
        company_users = @user.company_users.where(archived_at: nil, granted_access_at: nil)
        company_users.each do |cu|
          if cu.company.microsoft_sso_enabled
            cu.update_columns(granted_access_at: DateTime.current)
            ExpandedPrivileges::Populate.new(cu.contributor).call
            AssignAdminsToSampleCompaniesWorker.perform_async(cu.id) if cu.is_admin?
          end
        end
        verify_email_on_cognito(user_info)
      end

      if @user.super_admin?
        create_super_admin_log
        session[:super_admin_guid] = @user.guid
      else
        session[:super_admin_guid] = nil
      end

      company_users = @user.company_users.access_granted
      company_count = company_users.count

      if processible_state_params?
        path = params["state"].include?("invited") ? dashboard_path : params["state"].split(",")[0]
        subdomain =  params["state"].split(",")[1]
        protocol = Rails.env.development? ? "http://" : "https://"
        port = Rails.env.development? ? ":3000" : ""
        redirection_path = "#{protocol}#{subdomain}.#{Rails.application.credentials.root_domain}#{port}#{path}"
      end

      if @user.super_admin?
        if processible_state_params?
          redirect_user_to_secure(redirection_path)
        else
          redirect_to_specific_path(admin_root_path)
        end
      elsif session['redirect_to']
        host = URI(session['redirect_to']).host
        if host =~ /([\w\-]+)#{"." + root_domain}/
          subdomain = $1
          @company = Company.find_by_cache(subdomain: subdomain)
          if @company
            route_with_access
          else
            redirect_user(no_access_url)
          end
        else
          redirect_user(no_access_url)
        end
      elsif company_count > 1

        if processible_state_params?
          redirect_user_to_secure(redirection_path)
        else
          redirect_to_specific_path(select_company_path(redirect_to: params[:redirect_to]))
        end

      elsif company_count == 0

        if user_info.user_attributes[1].value.include?("Google") || user_info.user_attributes[1].value.include?("SAML")
          if params["state"] && params["state"].include?("invited")
            company_id = Company.find_by_cache(subdomain: subdomain).id
            @company_user = CompanyUser.where(user_id: @user.id, company_id: company_id).first

            if !@company_user.invite_token.nil? && @company_user.granted_access_at == nil
              service = CognitoService.new(@user)
              is_cognito_user = service.find_user_by_email(@user.email)
              new_cognito_user = service.sign_up_user if !is_cognito_user  
              @company_user.update_columns(invite_token: nil, granted_access_at: DateTime.current)
              if new_cognito_user
                user_sub = new_cognito_user.user.attributes.select{|item| item[:name] == "sub"}[0].value
                @user.update_columns(guid: user_sub, encrypted_password: nil, has_confirmed_email: true)
              end
              verify_email_on_cognito(user_info)
              access = UserAccess.create!(company_user: @company_user)
              redirect_user(user_access_url(id: access.auth_token, redirect_route: redirection_path))
            elsif is_sign_up_state?
              redirect_user(new_sso_company_path)
            else
              redirect_user(no_sso_company_path)
            end
          elsif is_sign_up_state?
            redirect_user(new_sso_company_path)
          else
            redirect_user(no_sso_company_path)
          end
        else
          if @user.companies.not_sample.where(verified: true).blank?
            return redirect_to review_companies_url
          end
          # if there are no company_user with granted_access_at set in any company
          # redirect them to no access page.
          redirect_user(no_access_url)
        end
      else
        # if the user needs to be routes to the right domain, then we need to
        # redirect them with a user access token
        @company_user = company_users.first
        @company = @company_user.company
        @company_user.update_columns(last_logged_in_at: DateTime.current)
        @company.update_columns(last_logged_in_at: DateTime.current) if !@user.super_admin?
        if session[:redirect_to] == 'store'
          user_access = UserAccess.create!(super_admin_guid: session[:super_admin_guid],
                                           company_user: @company_user,
                                           redirect_to: session['redirect_to'])
          redirect_user("#{Rails.application.credentials.store_url}/store_accesses/#{user_access.auth_token}")
        elsif @company.subdomain != subdomain || is_login_with_google?(user_info, subdomain)
          route_with_access(redirection_path)
        else

          if processible_state_params?
            redirect_user(redirection_path)
          else
            respond_with resource, location: after_sign_in_path_for(resource)
          end

        end
      end
    else
      # This path will destroy session from cognito and redirect user to signin
      # We have made custom login screen as default so currently, commenting out the logout path for cognito login screen
      # logout_url = "#{Rails.application.credentials.aws[:cognito][:base_uri]}/logout?response_type=code&client_id=#{Rails.application.credentials.aws[:cognito][:client_id]}&redirect_uri=#{Rails.application.credentials.aws[:cognito][:redirect_uri]}&state=STATE"

      if user_info.user_attributes[1].value.include?("Google") || user_info.user_attributes[1].value.include?("SAML")
        if @email_attribute.value.include?("gmail")
          redirect_user(no_sso_access_path)
        elsif is_sign_up_state?
          f_name = 4
          l_name = 5

          user_info.user_attributes.each_with_index do |ua, i|
            f_name = i if user_info.user_attributes[i].name == "given_name"
            l_name = i if user_info.user_attributes[i].name == "family_name"
          end
          email = @email_attribute.value.downcase
          user = User.find_by_cache(email: email)
          user ||= User.new(email: email)
          @guid_attribute = user_info.user_attributes.find{ |key| key.name == "sub" }
          first_name = user_info.user_attributes[f_name]&.value
          last_name = user_info.user_attributes[l_name]&.value
          user.assign_attributes(first_name: first_name.present? ? first_name : 'missing first name',
                                 last_name: last_name.present? ? last_name : 'missing last name',
                                 skip_validations: true,
                                 has_confirmed_email: true,
                                 sso_user: true,
                                 sso_user_source: user_info.user_attributes[1].value.include?("Google") ? User.sso_user_sources[:google] : User.sso_user_sources[:microsoft], 
                                 access_id: access_token,
                                 refresh_id: refresh_token,
                                 guid: @guid_attribute.value)

          user.save!

          RegistrationEmails.find_or_create_by(email: user.email, full_name: "#{user.first_name} #{user.last_name}")

          sign_in(User, user)

          verify_email_on_cognito(user_info)

          if Rails.env.production?
            HubspotIntegrationWorker.perform_async(user.as_json, nil, request.remote_ip)
          end

          redirect_user(new_sso_company_path)
        else
          redirect_user(no_sso_company_path)
        end
      else
        redirect_user(no_access_path)
      end
    end
  rescue OAuth2::Error => e
    e.message.start_with?('invalid_grant:') ? (redirect_to root_path) : (raise e)
  end

  private

  def redirect_to_specific_path admin_path
    if params["redirection_path"].present?
      render json: { url: params["redirection_path"] }
    else
      redirect_user(admin_path)
    end
  end

  def redirect_user_to_secure redirection_path
    if is_secure_subdomain?
      redirect_user(redirection_path)
    else
      params_token = params[:access_id_token]
      secure_url = user_session_url(host: secure_host, params: { access_id: params_token })
      render json: { url: redirection_path, secure_url: params_token.present? ? secure_url : nil }, status: :ok
    end
  end

  def set_client
    @client = OAuth2::Client.new(Rails.application.credentials.aws[:cognito][:client_id], Rails.application.credentials.aws[:cognito][:client_secret], site: Rails.application.credentials.aws[:cognito][:base_uri], authorize_url: '/oauth2/authorize', token_url: '/oauth2/token')
  end

  def route_with_access(redirection_path = nil)
    key = "company_user_#{current_user.id}_#{@company.id}"

    if is_cache_enabled?("company_user_auth_controller")
      company_user = Rails.cache.fetch(key, :expires_in => 8.hours) do
        current_user.reload
        CompanyUser.find_by(user_id: current_user.id, company_id: @company.id)
      end
    else
      CompanyUser.find_by(user_id: current_user.id, company_id: @company.id)
    end

    user_access = UserAccess.create!(super_admin_guid: session[:super_admin_guid],
                                     company_user: company_user,
                                     redirect_to: session['redirect_to'])
    session['redirect_to'] = nil
    if processible_state_params?
      redirect_user(redirection_path)
    else
      redirect_user(user_access_url(id: user_access.auth_token,
                                    domain: root_domain,
                                    subdomain: @company.subdomain))
    end
  end

  def basic_authorization
    Base64::encode64("#{Rails.application.credentials.aws[:cognito][:client_id]}:#{Rails.application.credentials.aws[:cognito][:client_secret]}").gsub("\n", "")
  end

  def track_sign_in
    mixpanel_service.track(@user.guid, 'Sign In')
  end

  def update_user_guid(user_info)
    sso_user = user_info.user_attributes[1].value.include?("Google")
    saml_user = user_info.user_attributes[1].value.include?("SAML")
    if sso_user
      @user.update_column(:sso_user_source, User.sso_user_sources[:google])
    elsif saml_user
      @user.update_column(:sso_user_source, User.sso_user_sources[:microsoft])
    end
    return if @user.guid.present? && (sso_user || saml_user)

    if @user.guid != @cognito_sub
      # for new cognito pool, we're updating the external id for appdirect.
      AppDirect::UpdateUserExternalId.new.perform(@user.id, @cognito_sub)
      @user.update_column('guid', @cognito_sub)
    end
  end

  def redirect_user(path)
    if @is_manual_login
      render json: {url: path}, status: :ok
    else
      redirect_to path
    end
  end

  def verify_email_on_cognito(user_info)
    is_email_verified = user_info.user_attributes.find{|key| key.name == "email_verified"}.value
    if user_info.user_attributes[1].value.include?("SAML") && is_email_verified == "false"
      client = Aws::CognitoIdentityProvider::Client.new(
        region: Rails.application.credentials.aws[:region],
        access_key_id: Rails.application.credentials.aws[:cognito][:access_key_id],
        secret_access_key: Rails.application.credentials.aws[:cognito][:secret_access_key]
      )
      client.admin_update_user_attributes({
        user_pool_id: Rails.application.credentials.aws[:cognito][:user_pool_id],
        username: user_info.username,
        user_attributes: [
          {
            name: "email_verified",
            value: "true"
          }
        ]
      }) rescue false
    end
  end

  def create_super_admin_log
    log_params = { email: @user.email, ip_address: request.remote_ip, user_id: @user.id }
    LogCreationWorker.perform_async('Logs::SuperAdminLog', log_params.to_json)
  end

  def is_login_with_google?(user_info, subdomain)
    user_info.user_attributes[1].value.include?("Google") && @company.subdomain == subdomain
  end

  def processible_state_params?
    params["state"] && !params["state"].include?("STATE") && !is_sign_up_state?
  end

  def is_sign_up_state?
    params["state"] == "SIGNUP"
  end
end
