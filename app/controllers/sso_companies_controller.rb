class SsoCompaniesController < ApplicationController
  include AssignAdminsToSampleCompany

  layout "bare"

  def new
    render layout: 'bare'
  end

  def create
    user = current_user
    company = nil
    url = nil

    Company.transaction do
      company = Company.new(company_params)
      company = company.set_up_self(user)
      user_form = company.custom_forms.find_by(form_name: "Teammates")

      company_user = user.company_users.create(company: company,
                                               granted_access_at: DateTime.current,
                                               custom_form_id: user_form.id)

      company_user.create_contributor

      company.admins.group_members.create(skip_callbacks: true, contributor_id: company_user.contributor_id)

      agent_group = company.groups.find_by_name("Help Desk Agents")
      agent_group.group_members.create(skip_callbacks: true, contributor_id: company_user.contributor_id)

      create_expanded_privileges(company_user.contributor)
      add_access_to_sample_company(company) unless cannot_add_sample_company

      if @sample_company && is_sample_company_user(user)
        subdomain = @sample_company.subdomain
      else
        subdomain = company.subdomain
      end

      user_access = UserAccess.create!(super_admin_guid: session[:super_admin_guid],
                                       company_user: company_user,
                                       redirect_to: "/dashboard")

      url = user_access_url(id: user_access.auth_token,
	                           domain: root_domain,
	                           subdomain: subdomain)
    end

    if user && company
      registration_email = RegistrationEmails.find_or_initialize_by(email: user.email)
      registration_email.assign_attributes(
        full_name: "#{user.first_name} #{user.last_name}",
        company_name: company.name,
        company_url: company.url,
        subdomain: company.subdomain
      )

      if registration_email.save
        registration_email.create_company_tracking(cookies)
      end
      # Send the appropriate mailers
      SendInfoEmailJob.set(wait: 5.minutes).perform_later(user)

      # Save any tracking data they came here with
      company.create_company_tracking(cookies)

      # Create sample company and discovered services
      company.run_setup_workers(request)
      if Rails.env.production?
        HubspotIntegrationWorker.perform_async(user.as_json, company.as_json, request.remote_ip)
      end
      render json: { url: url, company: company }, status: :ok
    end
  rescue => e
    render json: { confirmed: false, success: false, message: e.message }, status: :unprocessable_entity
  end

  def no_access
  end

  def company_creation_denied
  end

  private

  def add_access_to_sample_company company
    @sample_company ||= Company.find_by_cache(is_sample_company: true)
    admins_cu_ids = company.admin_company_users.ids
    if @sample_company && admins_cu_ids
      assign_admins(admins_cu_ids)
    end
  end

  def create_expanded_privileges(contributor)
    ExpandedPrivileges::Populate.new(contributor).call
  end

  def company_params
    params.require(:company).permit(
      :name,
      :subdomain,
      :email,
      :logo,
      :logo_url,
      :logo_filename,
      :logo_image_data,
      :timezone,
      :url,
      :default_logo_url,
      :original_logo_url
    )
  end

  def cannot_add_sample_company
    parent_id = Rails.application.credentials.sample_company[:parent_id]
    Rails.env.test? || parent_id.nil? || Company.find_by_cache(id: parent_id).nil?
  end
end
