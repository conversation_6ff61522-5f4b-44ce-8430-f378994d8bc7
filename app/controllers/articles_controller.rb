class ArticlesController < AuthenticatedController
  include MultiCompany::GeneralScoping
  include ArticlesHelper

  PER_PAGE_DEFAULT = 10

  def index
    articles = abbreviated_articles
    sorted_articles = articles[:articles].sort_by { |article| article[:order] }

    render json: {
      total: articles[:total],
      page_count: articles[:page_count],
      articles: sorted_articles.as_json({ current_company_user: scoped_company_user })
    }
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def create
    @article = scoped_company.articles.new(article_params)
    if update_article
      render json: { data: @article }, status: :ok
    else
      errors = article.errors.full_messages.to_sentence
      render json: { errors: errors }, status: :unprocessable_entity
    end
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def show
    if article.present?
      processed_article = article.as_json
      processed_article[:document] = article.library_documents
      render json: processed_article, status: :ok
    else
      render json: { message: "Article not found" }, status: :not_found
    end
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def update
    article.assign_attributes(article_params)
    if update_article
      render json: { data: @article }, status: :ok
    else
      errors = article.errors.full_messages.to_sentence
      render json: { errors: errors }, status: :unprocessable_entity
    end
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def destroy
    deleted_article = Article.find_by(id: params[:id])
    if deleted_article.present?
      if deleted_article.destroy
        render json: {}, status: :ok
      else
        errors = @article.errors.full_messages.to_sentence
        render json: { errors: errors }, status: :unprocessable_entity
      end
    else
      render json: { errors: "Article not present" }, status: :not_found
    end
  end

  def reorder
    id_lookup = params.require(:tasks).each_with_object({}) do |task_params, hash|
      permitted_task = task_params.permit(:id, :order)
      hash[permitted_task[:id].to_i] = permitted_task[:order].to_i
    end
    Article.where(id: id_lookup.keys).find_each do |article|
      article.update_column(:order, id_lookup[article.id])
    end
    render json: { message: 'Articles reordered successfully' }, status: :ok
  end

  def get_article_prvileges
    return render json: {}, status: :ok unless params[:article_ids].present?
  
    article_privileges = ArticlePrivilege.includes(contributor: [:company_user, :group]).where(article_id: params[:article_ids]) 
    contributors = article_privileges.each_with_object({}) do |privilege, hash|
      contributor = privilege.contributor
      next unless contributor
    
      entity = contributor.contributor_type == 'CompanyUser' ? contributor.company_user : contributor.group
      next unless entity
    
      hash[entity.id] ||= {
        contributorId: contributor.id,
        id: entity.id,
        name: entity.name
      }
      hash[entity.id][:email_address] = entity.email if contributor.contributor_type == 'CompanyUser'
      hash[entity.id][:role] = 'Group' if contributor.contributor_type == 'Group'
    end.values
  
    render json: contributors, status: :ok
  end

  private

  def abbreviated_articles
    selected_articles = Article.where(company_id: scoped_company.id, workspace_id: scoped_workspace.id)

    unless scoped_company_user&.user&.super_admin? || scoped_company.is_sample_company?
      selected_articles = selected_articles.includes(:article_expanded_privileges).where(article_expanded_privileges: { contributor_id: scoped_company_user.contributor_id })
    end

    if is_basic_read?
      selected_articles = selected_articles.where(public: true)
    end

    total_articles_count = selected_articles.count
    selected_articles = selected_articles.search_text(params[:search_terms]) if params[:search_terms].present?
    selected_articles = selected_articles.where(category: params[:category]) if params[:category].present?
    selected_articles = selected_articles.where(location_id: params[:location_id]) if params[:location_id].present?
    if params[:visibility].present?
      visibility_params = JSON.parse(params[:visibility], symbolize_names: true)
      selected_articles = selected_articles.where(public: visibility_params[:public]) unless visibility_params[:public].nil?
      if visibility_params[:contributor_ids].present?
        selected_articles = selected_articles.joins(:article_privileges)
                                             .where(article_privileges: { contributor_id: visibility_params[:contributor_ids] })
      end
    end
    selected_articles = selected_articles.where(company_user_id: params[:created_by]) if params[:created_by].present?
    if params[:start_date].present? && params[:end_date].present?
      selected_articles = selected_articles.where(created_at: Date.parse(params[:start_date]).beginning_of_day..Date.parse(params[:end_date]).end_of_day)
    end
    selected_articles = selected_articles.order('title ASC')
    selected_articles_count = selected_articles.count
    selected_articles = selected_articles.offset(offset)
    selected_articles = selected_articles.limit(page_size)
    truncate_articles_body(selected_articles)
    {
      articles: selected_articles,
      page_count: page_count(selected_articles_count),
      total: total_articles_count
    }
  rescue => e
    render json: { message: e.message }, status: :unprocessable_entity
  end

  def offset
    page_number > 0 ? (page_number - 1) * page_size : 0
  end

  def is_basic_read?
    JSON.parse(params[:is_basic_read]) if params[:is_basic_read]
  end

  def page_size
    params[:per_page]&.to_i || 25
  end

  def page_number
    params[:page]&.to_i || 1
  end

  def article_params
    params.require(:article).permit(
      :id,
      :title,
      :category,
      :body,
      :company_id,
      :company_user_id,
      :help_ticket_comment_id,
      :location_id,
      :slug,
      :public,
      :visibility,
      :created_by,
      :start_date,
      :end_date,
      tags: []
    )
  end

  def article
    @article ||= begin
      slug = params[:slug] || params[:article][:slug]
      scoped_workspace.articles.find_by(slug: slug)
    end
  end

  def update_article
    Articles::Update.new(article).call(params, scoped_company, scoped_company_user, scoped_workspace)
  end

  def page_count(articles_count)
    (articles_count / (params[:per_page].try(:to_f) || PER_PAGE_DEFAULT)).ceil
  end
end
