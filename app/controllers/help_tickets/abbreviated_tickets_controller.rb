module HelpTickets
  class AbbreviatedTicketsController < BaseTicketsController
    include HelpdeskRequestEmail
    include UnusedMemoryUtils

    PER_PAGE_DEFAULT = 25

    def index
      return render json: "", status: :not_acceptable unless format_json?

      set_instance_variables
      clear_unused_memory
      current_contributor_ids

      response_data = {
        totalTicketCount: @total_ticket_count,
        tickets: @tickets,
        currentContributorIds: @current_contributor_ids,
        pageCount: @page_count,
        requestEmail: @request_email,
        filterUpdated: @updated_filters,
        unread_comments_count: unread_comments_count
      }

      if params[:kanban_view]
        response_data[:kanbanPage] = params[:page] || 1
        response_data[:kanbanColumnTicketsCount] = kanban_column_tickets_count
      end

      render json: response_data
    end

    def set_instance_variables
      @tickets = tickets
      @total_ticket_count = total_ticket_count
      @page_count = page_count
      @request_email = request_email
      @updated_filters = updated_filters
    end

    def quick_filters_preview_data
      render json: { tickets: tickets }, status: :ok
    end

    def format_json?
      request.format.json?
    end

    private
    def offset
      params[:offset].to_i
    end

    def limit
      params[:limit].to_i
    end
  end
end
