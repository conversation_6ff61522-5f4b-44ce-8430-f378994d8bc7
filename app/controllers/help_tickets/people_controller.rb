module HelpTickets
  class PeopleController < AuthenticatedController
    def index
      search_query = params[:search].to_s.strip.downcase
      sort_by = params[:sort_by]
      sort_order = params[:sort_order]

      base_query = base_contributor_query
      filtered_contributors = search_query.present? ? apply_search_filter(base_query, search_query) : base_query

      paginated = filtered_contributors.offset((page - 1) * per_page).limit(per_page)
      contributor_ids = paginated.pluck(:contributor_id)

      field = params[:is_agent].present? ? 'assigned_user_ids' : 'creator_ids'
      ticket_stats = contributor_ids.blank? ? {} : ticket_stats_for(contributor_ids: contributor_ids, field: field)
      
      results = paginated.map do |record|
        contributor = record.contributor
        stats = ticket_stats[contributor.id] || {}

        {
          contributor_id: contributor.id,
          name: contributor.name,
          ticketCount: stats['total_count'].to_i,
          openTicketCount: stats['open_count'].to_i,
          last30DaysTicketCount: stats['last_30'].to_i,
          last60DaysTicketCount: stats['last_60'].to_i,
          last90DaysTicketCount: stats['last_90'].to_i
        }
      end

      sorted_results = sort_results(results, sort_by, sort_order)

      render json: {
        people: sorted_results,
        total_count: filtered_contributors.count,
        per_page: per_page,
        page: page
      }, status: :ok
    end

    def ticket_stats_for(contributor_ids:, field:)
      HelpTicket.select(
        "unnest(#{field}) AS contributor_id",
        "COUNT(*) AS total_count",
        "COUNT(*) FILTER (WHERE status != 'Closed') AS open_count",
        "COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') AS last_30",
        "COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '60 days') AS last_60",
        "COUNT(*) FILTER (WHERE created_at >= CURRENT_DATE - INTERVAL '90 days') AS last_90"
      )
      .where("#{field} && ARRAY[?]::int[] AND workspace_id = ?", contributor_ids, scoped_workspace.id)
      .group("unnest(#{field})")
      .index_by { |row| row.contributor_id }
    end

    def page
      params['page'].to_i > 0 ? params['page'].to_i : 1
    end

    def per_page
      params['per_page'].to_i > 0 ? params['per_page'].to_i : 25
    end

    def base_contributor_query
      if params[:is_agent].present?
        GroupMember.joins(:group).where(groups: { workspace_id: scoped_workspace.id }).joins(contributor: { company_user: :user })
      else
        CompanyUser.joins(:user).joins(contributor: :privileges).where(company_id: current_company.id).where(privileges: { workspace_id: scoped_workspace.id }).distinct
      end
    end

    def apply_search_filter(query, search_query)
      query.where("LOWER(CONCAT(users.first_name, ' ', users.last_name)) ILIKE ?", "%#{search_query}%")
    end

    def sort_results(results, sort_by_param, sort_order_param)
      sort_key_map = {
        'people' => :name,
        'open_ticket_count' => :openTicketCount,
        'last_30_days_ticket_count' => :last30DaysTicketCount,
        'last_60_days_ticket_count' => :last60DaysTicketCount,
        'last_90_days_ticket_count' => :last90DaysTicketCount,
        'full_history' => :ticketCount
      }

      sort_key = sort_key_map.fetch(sort_by_param)
      sort_desc = sort_order_param == 'desc'

      sorted = results.sort_by { |r| r[sort_key] || 0 }
      sort_desc ? sorted.reverse : sorted
    end
  end
end
