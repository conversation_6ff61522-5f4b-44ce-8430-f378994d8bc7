class HelpTickets::TicketSummariesController < ApplicationController
  def summarize
    ticket = HelpTicket.find_by(id: params[:ticket_id])
    GenerateTicketSummaryWorker.perform_async(ticket.id, current_company_user.id)

    render json: { time_stamp: Time.now.in_time_zone(current_company.timezone)}, status: :ok
  end

  def show
    ai_summary = AiSummary.find_by(help_ticket_id: params[:id])
    help_ticket_last_activity = HelpTicketActivity.where(help_ticket_id: params[:id]).last.created_at

    if (ai_summary && (help_ticket_last_activity > ai_summary.updated_at))
      regenerate_button = true
    else
      regenerate_button = false
    end

    render json: { summary: ai_summary&.summary, updated_at: ai_summary&.updated_at, show_regenerate_button: regenerate_button }, status: :ok
  end
end
