class CustomSurveysController < AuthenticatedController
  layout 'bare'

  skip_before_action :ensure_access, only: [:show]
  skip_before_action :authenticate_user!, only: [:show]
  skip_before_action :verify_mfa, only: [:show]
  before_action :set_resource, only: [:show, :update, :destroy, :toggle_active]

  def index
    surveys = scoped_workspace.custom_surveys
    surveys = surveys.where(visible: true) if params[:visible_only] == 'true'

    render json: { custom_surveys: surveys.as_json }, status: :ok
  end

  def show
    @body_class = "closing-survey"

    respond_to do |format|
      format.json { render json: @custom_survey.as_json }
      format.html {}
    end
  end

  def create
    ActiveRecord::Base.transaction do
      @new_survey = params['custom_survey']
      @survey = scoped_company.custom_surveys.new(survey_params)
      @survey.save!
      
      CustomSurveys::QuestionsUpdate.new(@new_survey['questions'], @survey).create
      CustomSurveys::RulesUpdate.new(@new_survey['rules'], @survey).create
      CustomSurveys::TriggerUpdate.new(@new_survey['trigger'], @survey).create

      render json: @survey, status: :ok if @survey.present?
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      render json: { message: e.message }, status: :unprocessable_entity
      raise ActiveRecord::Rollback
    end
  end

  def update
    ActiveRecord::Base.transaction do
      @new_survey = params['custom_survey']
      if @custom_survey.present?
        @custom_survey.update!(survey_params)

        CustomSurveys::QuestionsUpdate.new(@new_survey['questions'], @custom_survey).update
        CustomSurveys::RulesUpdate.new(@new_survey['rules'], @custom_survey).update
        CustomSurveys::TriggerUpdate.new(@new_survey['trigger'], @custom_survey).update
      end

      render json: @custom_survey, status: :ok if @custom_survey.present?
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      render json: { message: e.message }, status: :unprocessable_entity
      raise ActiveRecord::Rollback
    end
  end

  def toggle_active
    @custom_survey.visible = params['visible']
    if @custom_survey.save
      render json: { domain: @custom_survey.as_json }, status: :ok
    else
      render json: { message: @custom_survey.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def destroy
    if @custom_survey.destroy
      render json: { message: "Custom Survey deleted successfully" }, status: :ok
    else
      render json: { message: @custom_survey.errors.full_messages.to_sentence }, status: :unprocessable_entity  
    end
  end

  private

  def set_resource
    @custom_survey ||= CustomSurvey.find_by(id: params[:id])
  end

  def survey_params
    {
      title: @new_survey["title"],
      description: @new_survey["description"],
      workspace_id: scoped_workspace.id,
      custom_form_ids: @new_survey["custom_forms"]&.pluck(:id),
      background_color: @new_survey['background_color'],
      custom_design: @new_survey['custom_design'],
      visible: @new_survey['visible']
    }
  end
end
