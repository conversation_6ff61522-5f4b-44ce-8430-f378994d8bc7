class ContractsController < Contracts::BaseController
  before_action :authorize_write, only: [:create, :update, :destroy, :new]
  before_action :set_resource,
                only: [:show, :edit, :update, :destroy, :archive, :unarchive],
                if: Proc.new { |w| request.format.json? }
  skip_before_action :verify_authenticity_token, only: [:remove_file, :destroy, :update, :destroy]

  include Operations

  def index
    respond_to do |format|
      format.html {}
      format.json { render json: { contracts: contracts, total_contracts: @total_contracts, rails_env: Rails.env, channel_key: scoped_company_user.guid, company_channel_key: scoped_company.guid } }
    end
  end

  def old_contracts_of_user
    historical_contract_ids = ContractAndAppHistory.where(company_user_id: params[:user_id], record_type: 'contract').pluck(:contract_id).uniq
    current_contract_ids = ContractContact.where(company_user_id: params[:user_id]).pluck(:contract_id).uniq
    old_contract_ids = historical_contract_ids - current_contract_ids
    contracts = Contract.where(id: old_contract_ids).offset((params[:page_no].to_i - 1) * params[:page_size].to_i).limit(params[:page_size].to_i)
    render json: { contracts: contracts, total_old_contracts: old_contract_ids.count }
  end

  def show
    respond_to do |format|
      format.html { render action: 'index' }
      format.json { render json: get_contract_info_json }
    end
  end

  def new
    respond_to do |format|
      format.html { render action: 'index' }
    end
  end

  def edit
    respond_to do |format|
      format.html { render action: 'index' }
    end
  end

  def create
    check_default_vendor
    @contract = scoped_company.contracts.new(contract_params)
    @contract.contract_value_amount = contract_params[:contract_value_amount].delete("_,$").to_f if contract_params[:contract_value_amount]
    #created_by used for reward points
    #checking if we get all valid data here
    @contract.assign_attributes(creator_id: scoped_company_user&.contributor&.id,
                                created_by: scoped_company_user,
                                company_user_id: scoped_company_user.id,
                                request_ip: request.ip,
                                request_uuid: request.uuid)
    if @contract.save_without_auditing
      create_new_vendor
      create_default_vendor_source
      save_related_items if params[:contract][:links_array].present?
      add_tags
      create_contract_locations
      create_contract_departments
      create_contract_company_users
      apply_track_spend
      render json: { contract: get_contract_info_json }, status: :ok
    else
      scoped_company.vendors.find(@vendor_id).destroy if @vendor_create_flag
      render json: { message: @contract.errors.full_messages.join(', ') }, status: :unprocessable_entity
    end
  end

  def update
    prev_vendor_id = @contract.vendor_id
    prev_product_id = @contract.product_id
    check_default_vendor
    unless params[:only_category_change] || params[:only_note_change]
      @contract.contract_value_amount = contract_params[:contract_value_amount].delete("_,$").to_f if contract_params[:contract_value_amount]
      @contract.created_by = scoped_company_user # used for reward points
    end

    @contract.assign_attributes(contract_params)
    save_related_items

    # Check if we get all valid data here
    if @contract.contract_type == "open_ended" && @contract.alert_dates
      @contract.alert_dates.each do |contract_alert_date|
        contract_alert_date.delete if contract_alert_date.date.nil?
      end
      @contract.end_date = nil
    end

    ActiveRecord::Base.transaction do
      create_new_vendor
      if prev_vendor_id != @contract.vendor_id
        @contract.general_transactions.each(&:destroy)
        @contract.product_id = nil
      end
      @contract.assign_attributes(company_user_id: scoped_company_user.id,
                                  request_ip: request.ip,
                                  request_uuid: request.uuid)
      @contract.save!
      create_default_vendor_source
      if @contract.saved_changes['track_spend'].present?
        apply_track_spend 
      elsif product_name.present?
        create_product
        if prev_product_id != @contract.product_id && @contract.track_spend
          create_transactions(@contract.product)
        end
      end

      unless params[:only_category_change] || params[:only_note_change]
        add_tags
        create_contract_locations
        create_contract_departments
        create_contract_company_users
      end
      render json: { contract: get_contract_info_json }, status: :ok
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue Exception => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
      render json: { message: @contract.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def destroy
    @contract.created_by = scoped_company_user # used to delete event and points
    if @contract.destroy
      render json: {}, status: :ok
    else
      render json: { message: @contract.errors.full_messages.join(', ') }, status: :unprocessable_entity
    end
  end

  def remove_file
    attachment = ContractAttachment.find_by(id: params[:file_id])
    if attachment.present?
      if attachment.destroy
        render json: {}, status: :ok
      else
        render json: { message: attachment.errors.full_messages.to_sentence }, status: :unprocessable_entity
      end
    else
      render json: { message: "Attachment not found" }, status: :not_found
    end
  end

  def archive
    if @contract.update(archived: true)
      render json: {}, status: :ok
    else
      render json: { message: @contract.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def unarchive
    if @contract.update(archived: false)
      render json: {}, status: :ok
    else
      render json: { message: @contract.errors.full_messages.to_sentence }, status: :unprocessable_entity
    end
  end

  def monthly_contracts
    year = params[:year].to_i
    current_month = params[:currentMonth].to_i
    contracts = fetch_contracts_for_year(year, current_month)

    render json: {
      contracts: group_contracts_by_month(contracts, year),
      alerts: group_alerts_by_month(year, current_month)
    }
  end

  def contract_contacts
    contract_type = params['contractType'] || 'fixed_term'
    contracts = Contract.where(company_id: scoped_company.id, contract_type: contract_type)
                        .includes(company_users: :user)

    grouped_contacts = contracts.flat_map { |contract| contract.company_users.map { |user| contact_details(user, contract.id) } }
                                .group_by { |contact| contact[:id] }

    all_contacts = grouped_contacts.values.collect { |contacts| build_contact_response(contacts) }

    render json: all_contacts
  end

  def contracts_metrics
    contracts = Contract.where(company_id: scoped_company.id)

    monthly_max = (contracts.maximum(:monthly_cost) || 0).ceil
    avg_monthly = (contracts.average(:monthly_cost) || 0).round 
    max_total = 0.0
    sum_total = 0.0
    count = 0

    contracts.each do |contract|
      amount = contract.contract_value_amount.to_f
      max_total = amount if amount > max_total
      sum_total += amount
      count += 1
    end

    total_max = max_total.ceil
    avg_total = count > 0 ? (sum_total / count).round : 0

    render json: {
      monthly_max: monthly_max,
      avg_monthly: avg_monthly,
      total_max: total_max,
      avg_total: avg_total,
    }
  end

  private

  def product_name
    params[:contract][:product_name]
  end

  def track_spend
    params[:contract][:track_spend]
  end

  def apply_track_spend
    if track_spend == 'true'
      create_product_and_transactions
    elsif track_spend == 'false' && product_name.present?
      create_product
    end
  end

  def contact_details(contact, contract_id)
    {
      id: contact.contributor_id,
      name: contact.user.full_name,
      email: contact.email,
      contract_id: contract_id
    }
  end

  def build_contact_response(contacts)
    first_contact = contacts.first.slice(:id, :name, :email)
    first_contact[:contract_ids] = contacts.filter_map { |c| c[:contract_id] }.uniq
    first_contact
  end

  def save_related_items
    LinkableService.new(@contract, scoped_company_user, request).save_serialized_related_items(params[:contract][:links_array])
  end

  def fetch_contracts_for_year(year, current_month)
    contracts = Contract.includes(:vendor, :alert_dates)
                        .where(company_id: scoped_company.id, contract_type: params['contractType'])

    start_date = Date.new(year, 1, 1).prev_month.beginning_of_month
    current_month == 0 ? end_date = Date.new(year, 12, 31) : end_date = Date.new(year + 1, current_month + 1, 1).end_of_month

    if params['contractType'] == 'fixed_term'
      contracts = contracts.where(end_date: start_date..end_date)
    else
      contracts = contracts.where(start_date: start_date..end_date)
    end
  
    contracts.order(end_date: :asc)
  end

  def group_contracts_by_month(contracts, current_year)
    grouped = contracts.group_by do |c|
      date = params['contractType'] == 'fixed_term' ? c.end_date : c.start_date
      if date.year == current_year
        date.strftime("%B").downcase
      else
        date.strftime("%B%Y").downcase
      end
    end
  
    grouped.transform_values do |contracts|
      contracts.map { |contract| contract_details(contract) }
    end
  end

  def group_alerts_by_month(year, current_month)
    contracts = scoped_company.contracts.where(contract_type: params['contractType']).includes(:vendor, :alert_dates)
  
    contract_map = contracts.index_by(&:id)
  
    end_date = if current_month == 0
                 Date.new(year, 12, 31)
               else
                 Date.new(year + 1, current_month, 1).end_of_month
               end
    start_date = Date.new(year, 1, 1)
    alert_dates = contracts.flat_map(&:alert_dates)
                           .select do |alert|
                             alert_date = alert.date
                             alert_date >= start_date && alert_date <= end_date
                           end
  
    grouped = alert_dates.group_by do |alert|
      alert_date = alert.date
      if alert_date.year == year
        alert_date.strftime("%B").downcase
      else
        alert_date.strftime("%B%Y").downcase
      end
    end
  
    grouped.transform_values do |alerts|
      alerts.map do |alert|
        contract = contract_map[alert.alertable_id]
        {
          id: alert.id,
          date: alert.date,
          is_seen: alert.is_seen,
          notes: alert.notes,
          contract_id: alert.alertable_id,
          contract_name: contract&.name,
          end_date: contract&.end_date,
          annual_cost: contract&.contract_value_amount,
          vendor_name: contract&.vendor&.name
        }
      end
    end
  end

  def contract_details(contract)
    start_date = contract.start_date
    end_date = contract.end_date
    months, days = calculate_duration(start_date, end_date)

    duration_text = format_duration(months, days)

    contract.as_json.merge(
      id: contract.id,
      name: contract.name,
      vendor: contract.vendor.as_json(except: :total_spend),
      end_date: contract.end_date,
      monthly_cost: contract.monthly_cost,
      contract_term: duration_text,
      contacts: contract.company_users.map { |user| contact_info_json(user) },
      department: contract.departments,
      locations: contract.locations,
      tags: contract.contract_tags,
      alert_dates: contract.alert_dates
    )
  end

  def calculate_duration(start_date, end_date)
    months = (end_date.year * 12 + end_date.month) - (start_date.year * 12 + start_date.month)
    days = end_date.day - start_date.day

    if days.negative?
      months -= 1
      previous_month = end_date.prev_month
      days += (Date.new(previous_month.year, previous_month.month, -1).day)
    end

    [months, days]
  end

  def format_duration(months, days)
    if months.zero?
      "#{days} days"
    elsif days.zero?
      "#{months} months"
    else
      "#{months} months #{days} days"
    end
  end

  def resp_path(response)
    if response[:flag]
      if response[:invalid_file]
        flash[:error] = 'Invalid file type.'
        redirect_back(fallback_location: root_path)
      elsif response[:empty_file]
        flash[:error] = 'File is empty.'
        redirect_back(fallback_location: root_path)
      else
        flash[:error] = 'Valid records are uploaded. Please correct invalid records and upload again'
        render 'csv_files/invalid_records_file', locals: { data: response[:data], company: scoped_company, invalid_records: response[:invalid], errors: response[:errors] }
      end
    else
      flash[:success] = 'valid records are added successfully'
      redirect_to contracts_path
    end
  end

  def set_resource
    @contract = scoped_company.contracts.find_by_id(params[:id])
    unless @contract
      respond_to do |format|
        format.json { render json: { message: "Contract was not found." } }
        format.html { render 'shared/not_found' }
      end
    end
  end

  def contract_params
    params.require(:contract).permit(
      :name,
      :company_id,
      :vendor_id,
      :location_id,
      :product,
      :reference,
      :category_id,
      :contract_value_amount,
      :start_date,
      :notice_period,
      :end_date,
      :notes,
      :monthly_cost,
      :all_locations,
      :vendor_notes,
      :default_vendor_id,
      :contract_type,
      :new_vendor_name,
      :is_cloned,
      :track_spend,
      :parent_id,
      location_ids: [],
      alert_dates_attributes: [:id, :date, :_destroy],
      contract_attachments_attributes: [:attached_file],
      company_user_ids: []
    )
  end

  def create_contract_locations
    existing_ids = @contract.contract_locations.pluck(:location_id)
    location_ids = params[:contract][:location_ids]&.split(",").map(&:to_i)
    new_locations_ids = location_ids - existing_ids
    deleted_locations_ids = existing_ids - location_ids

    if deleted_locations_ids.present?
      begin
        @contract.contract_locations.where(location_id: deleted_locations_ids).destroy_all
      rescue Exception => e
        Rails.logger.error("Failed to delete contract locations: #{e.message}")
        raise e
      end
    end

    if new_locations_ids.present?
      ActiveRecord::Base.transaction do
        location_ids.each do |location_id|
          @contract.contract_locations.create(location_id: location_id)
        end
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
        raise e
      end
    end
  end

  def create_contract_company_users
    existing_ids = @contract.contract_contacts.pluck(:company_user_id)
    company_user_ids = params[:contract][:company_user_ids]&.split(",")
    new_company_user_ids = company_user_ids&.map(&:to_i) - existing_ids
    deleted_company_user_ids = existing_ids - company_user_ids&.map(&:to_i)
    if deleted_company_user_ids.present?
      ActiveRecord::Base.transaction do
        @contract.contract_contacts.where(company_user_id: deleted_company_user_ids).destroy_all
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
        raise e
      end
    end

    if new_company_user_ids.present?
      ActiveRecord::Base.transaction do
        company_user_ids.each do |company_user_id|
          @contract.contract_contacts.create(company_user_id: company_user_id)
        end
        ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
      rescue Exception => e
        Rails.logger.error("Transaction failed: #{e.message}")
        ActiveRecord::Base.connection.execute "ROLLBACK"
        raise e
      end
    end
  end

  def create_contract_departments
    return unless params[:contract][:department_ids]

    existing_ids = @contract.contract_departments.pluck(:department_id)
    department_ids = params[:contract][:department_ids].split(",").map(&:to_i)
    new_department_ids = department_ids - existing_ids
    deleted_department_ids = existing_ids - department_ids

    if deleted_department_ids.present?
      begin
        @contract.contract_departments.where(department_id: deleted_department_ids).destroy_all
      rescue Exception => e
        Rails.logger.error("Failed to delete contract departments: #{e.message}")
        raise e
      end
    end

    if new_department_ids.present?
      new_department_ids.each do |department_id|
        begin
          @contract.contract_departments.create(department_id: department_id)
        rescue Exception => e
          Rails.logger.error("Failed to create contract department with ID #{department_id}: #{e.message}")
          raise e
        end
      end
    end
  end

  def contracts
    if is_dashboard_route?
      @contracts = scoped_company.contracts.includes(:category).as_json
    else
      @contracts = scoped_company.contracts.includes(:category, :contract_tags, :alert_dates, :departments, :contract_attachments, :locations, :vendor, :creator, product: :default_product)
      if has_module_access?
        @contracts = @contracts.where(creator_id: scoped_company_user.contributor_id)
      elsif permissions['Contract']['root'].nil? || permissions['Contract']['root'].empty?
        return []
      end
      @contracts = @contracts.joins(:contract_contacts).where("contract_contacts.company_user_id = ?", params["company_user_id"]) if params["company_user_id"].present?
      @contracts = @contracts.where(archived:false) if params['archived'] == "false"

      # sorting
      @contracts = apply_sorting(@contracts)

      # filters
      @contracts = apply_filters(@contracts)

      # pagination
      page = params[:page].present? ? params[:page].to_i : 1
      per_page = params[:per_page].present? ? params[:per_page].to_i : 15
      offset = (page - 1) * per_page
      @contracts = @contracts[offset, per_page]
      @contracts = @contracts.map do |contract|
        contract.as_json.merge(
          attachments: contract.contract_attachments.present?,
          vendor: contract.vendor.as_json(except: :total_spend),
          location_ids: contract.locations.ids,
          tags: contract.contract_tags.sort_by(&:tag),
          alert_dates: contract.alert_dates.sort_by(&:date),
          department_ids: contract.departments.ids
        )
      end
    end
  end

  def get_contract_info_json
    edited_contract = @contract.as_json.merge(
      contract_attachments: (@contract.contract_attachments.present? ? @contract.contract_attachments.map { |att| att.as_json.merge(url: att.attached_file.url, attached_file_file_name: att.attached_file.filename.to_s, attached_file_content_type: att.attached_file.content_type).as_json } : nil),
      category: @contract.category.presence,
      alert_dates: @contract.alert_dates.order('date ASC'),
      locations: @contract.locations,
      departments: @contract.departments,
      universal_links: @contract.linkable.source_linkable_links.map { |ll| { id: ll.id, target: ll.target } },
      contacts: @contract.company_users.map { |user| contact_info_json(user) },
      tags: @contract.contract_tags,
      creator: @contract.creator&.as_json_abbreviated,
      tags_array: @contract.contract_tags.pluck(:tag),
      parent_contract: @contract.parent_contract
    ).as_json

    contract_vendor = @contract.vendor
    edited_contract = edited_contract.merge(
                        rails_env: Rails.env,
                        vendor: contract_vendor,
                        channel_key: scoped_company_user.guid,
                        linkable_id: @contract.linkable.id,
                        company_channel_key: scoped_company.guid,
                        total_monthly_cost: scoped_company.contracts.sum(:monthly_cost)).as_json
    edited_contract
  end

  def date_parse(params, type)
    Date.parse(contract_params[type]);
  rescue => e
    return e.message
  end

  def contact_info_json(contact)
    {
      id: contact.id,
      full_name: contact.user.full_name,
      email: contact.email,
      avatar: contact.avatar.present? ? contact.avatar[:url] : nil,
      avatar_thumb_url: contact.avatar.present? ? contact.avatar_thumb : nil,
      work_phone: contact.work_phone,
      work_phone_country_code: contact.work_phone_country_code,
      work_phone_country_code_number: contact.work_phone_country_code_number
    }
  end

  def add_tags
    tags = params[:contract][:tags_array]
    if tags.present?
      new_tags = tags[0]&.split(",")&.flatten
      existing_tags = @contract.contract_tags.map(&:tag)
      unless new_tags.sort == existing_tags.sort
        to_add = new_tags - existing_tags
        to_remove = existing_tags - to_add - (existing_tags & new_tags)
        @contract.contract_tags.where(tag: to_remove).destroy_all

        to_add.each do |tag|
          @contract.contract_tags << ContractTag.new(tag: tag, company: scoped_company)
        end
      end
    end
  end

  def check_default_vendor
    default_vendor_id = params['contract']['default_vendor_id']
    @vendor_create_flag = false
    if default_vendor_id.present? && default_vendor_id != "undefined"
      default_vendor_id = params['contract']['default_vendor_id'].to_i
      default_vendor = DefaultVendor.find_by(id: default_vendor_id)
      vendor = scoped_company.vendors.find_by_name(default_vendor.name)
      if vendor
        @vendor_id = vendor.id
      else
        @vendor_id = scoped_company.vendors.create!(
          name: default_vendor.name,
          created_by: scoped_company_user,
          creator_id: scoped_company_user&.contributor&.id,
          category_id: add_category(default_vendor)
        ).id
        @vendor_create_flag = true
      end
      params['contract']['vendor_id'] = @vendor_id
    end
  end

  def add_category(default_vendor)
    category_name = default_vendor&.default_category&.name || "Uncategorized"
    scoped_company.categories.find_by(name: category_name)&.id
  end

  def create_new_vendor
    vendor_name = params[:new_vendor_name]
    existing_vendor = scoped_company.vendors.find_by_name(vendor_name)
    if vendor_name.present? && !existing_vendor
      begin
        category_id = scoped_company.categories.find_by(name: "Uncategorized").id
        new_vendor = Vendor.create!(
          name: vendor_name,
          category_id: category_id,
          company_id: scoped_company.id,
          creator_id: scoped_company_user&.contributor&.id,
          vendor_source: @contract
        )
        @contract.update_column(:vendor_id, new_vendor.id)
      rescue Exception => e
        Rails.logger.error("Failed to create vendor: #{e.message}")
        raise e
      end
    elsif vendor_name.present?
      @contract.update_column(:vendor_id, existing_vendor.id)
    end
  end

  def create_product
    if @contract.vendor
      name = product_name || @contract.name
      product = Product.find_or_initialize_by(company_id: @contract.company_id,
                                              name: name,
                                              vendor_id: @contract.vendor_id)
      product.product_type = 'Auto-generated from linked Contract' if product.new_record? && product_name.nil?
      product.save
      @contract.update(product: product)
    end
    product
  end

  def create_product_and_transactions
    if @contract.vendor
      product = create_product
      create_transactions(product)
    end
  end

  def create_transactions(product)
    contract_transaction = @contract.vendor.general_transactions.new({ amount: @contract.monthly_cost,
                                                                        company_id: @contract.company_id,
                                                                        product: product,
                                                                        product_name: product.name,
                                                                        recurring: true,
                                                                        location_id: @contract.locations[0]&.id,
                                                                        status: "recognized",
                                                                        transaction_date: @contract.start_date })
    contract_transaction.recurrance_end_date = @contract.end_date + 1.day if @contract.fixed_term?
    contract_transaction.save
    TransactionRecurrence.new(contract_transaction).create_recurrances
  end

  def apply_filters(contracts)
    contracts = filter_by_category(contracts)
    contracts = filter_by_search(contracts)
    contracts = filter_by_department(contracts)
    contracts = filter_by_vendor(contracts)
    contracts = filter_by_contract_type(contracts)
    contracts = filter_by_location(contracts)
    contracts = filter_by_tag(contracts)
    contracts = filter_by_start_date(contracts)
    contracts = filter_by_end_date(contracts)
    contracts = filter_by_alert_date(contracts)
    contracts = filter_by_status(contracts)
    contracts = filter_by_monthly_cost(contracts)
    contracts = filter_by_total_cost(contracts)
    @total_contracts = contracts.count
    contracts
  end

  def filter_by_category(contracts)
    return contracts unless params['category_id'].present?
    contracts.where(category_id: params['category_id'])
  end

  def filter_by_search(contracts)
    return contracts unless params['search'].present?
    search_term = "%#{params['search'].downcase}%"
    contracts.joins(:vendor).where("LOWER(contracts.name) LIKE ? OR LOWER(vendors.name) LIKE ?", search_term, search_term)
  end

  def filter_by_department(contracts)
    return contracts unless params['department_id'].present?
    contracts.joins(:departments).where(departments: { id: params['department_id'] })
  end

  def filter_by_vendor(contracts)
    return contracts unless params['vendor_id'].present?
    contracts.where(vendor_id: params['vendor_id'])
  end

  def filter_by_contract_type(contracts)
    return contracts unless params['contract_type'].present?
    contracts.where(contract_type: params['contract_type'])
  end

  def filter_by_location(contracts)
    return contracts unless params['location_id'].present?
    contracts.joins(:locations).where(locations: { id: params['location_id'] })
  end

  def filter_by_tag(contracts)
    return contracts unless params['tag'].present?
    contracts.joins(:contract_tags).where(contract_tags: { tag: params['tag'] })
  end

  def filter_by_start_date(contracts)
    return contracts unless params['start_date'].present?
    contracts.where(start_date: params['start_date'])
  end

  def filter_by_end_date(contracts)
    return contracts unless params['end_date'].present?
    contracts.where(end_date: params['end_date'])
  end

  def filter_by_alert_date(contracts)
    return contracts unless params['alert_date'].present?
    contracts.joins(:alert_dates).where(alert_dates: { date: params['alert_date'] })
  end

  def filter_by_status(contracts)
    return contracts unless params['status'].present?

    case params['status']
    when "active"
      contracts.active
    when "expired"
      contracts.expired
    when "archived"
      contracts.archived
    when "thirtyDays"
      contracts.ending_within(0, 30)
    when "sixtyDays"
      contracts.ending_within(31, 60)
    when "ninetyDays"
      contracts.ending_within(61, 90)
    end
  end

  def filter_by_monthly_cost(contracts)
    return contracts unless params[:monthly_min].present? && params[:monthly_max].present?
    contracts.where(monthly_cost: params[:monthly_min]..params[:monthly_max])
  end

  def filter_by_total_cost(contracts)
    return contracts unless params[:total_min].present? && params[:total_max].present?
    min = params[:total_min].to_f
    max = params[:total_max].to_f
    contracts.select { |c| c.contract_value_amount.to_f.between?(min, max) }
  end

  def apply_sorting(contracts)
    sort_item = params[:sort_by]
    sort_direction = params[:sort_order]

    return contracts unless sort_item.present?
    
    sort_direction = 'asc' unless %w[asc desc].include?(sort_direction)

    case sort_item
    when 'category'
      contracts.left_joins(:category)
              .group('contracts.id, categories.name')
              .order(Arel.sql("categories.name #{sort_direction} NULLS LAST"))
    when 'contract_tags'
      contracts.left_joins(:contract_tags)
              .group('contracts.id')
              .order(Arel.sql("MIN(contract_tags.tag) #{sort_direction} NULLS LAST"))
    when 'locations'
      contracts.left_joins(:locations)
              .group('contracts.id')
              .order(Arel.sql("COUNT(locations.id) #{sort_direction}"))
    when 'vendor_name'
      contracts.left_joins(:vendor)
              .order(Arel.sql("vendors.name #{sort_direction} NULLS LAST"))
    when 'alert_dates'
      contracts.left_joins(:alert_dates)
              .group('contracts.id')
              .order(Arel.sql("MIN(alert_dates.date) #{sort_direction} NULLS LAST"))
    when 'contract_value_amount'
      sorted = contracts.to_a.sort_by { |c| c.contract_value_amount.to_f }
      sorted.reverse! if sort_direction == 'desc'
      sorted
    else
      if Contract.column_names.include?(sort_item)
        contracts.order(Arel.sql("contracts.#{sort_item} #{sort_direction} NULLS LAST"))
      else
        contracts.order(created_at: sort_direction)
      end
    end
  end

  def create_default_vendor_source
    if params['contract']['default_vendor_id'].present?
      vendor = Vendor.find(@vendor_id)
      vendor.vendor_source = @contract
      vendor.save!
    end
  end
  
  def has_module_access?
    if params['company_module'] == 'company_user'
      return permissions['CompanyUser']['root'].include?('read') || permissions['CompanyUser']['root'].include?('write')
    else
      return permissions['Contract']['root'].include?("scoped") && !permissions['Contract']['root'].include?("read")
    end
  end

  def is_dashboard_route?
    !!params["is_dashboard"]
  end
end
