import http from 'common/http';
import { fixRounding } from 'mixins/currency';
import GlobalStore from './global_store';
import { contracts } from "./mixins/contracts";
import { companyUsersAndGroupsOptions } from './mixins/company_users_groups_options';
import customForms from "./mixins/custom_forms_mixin";

export default new Vuex.Store({
  modules: {
    contracts: {
      namespaced: true,
      ...contracts,
    },
    companyUsersAndGroupsOptions: {
      ...companyUsersAndGroupsOptions,
    },
    GlobalStore: {
      namespaced: true,
      ...GlobalStore,
    },
    customForms: {
      namespaced: true,
      ...customForms,
    },
  },
  strict: process.env.NODE_ENV === "development",

  state: {
    moduleName: "Contract",
    loading: false,
    currentContract: null,
    totalValue: 0,
    companyChannelKey: '',
    channelKey: '',
    indexPage: 0,
    indexPerPage: 25,
    insightsIndexPerPage: 15,
    active: [],
    thirtyDays: [],
    sixtyDays: [],
    ninetyDays: [],
    openEnded: [],
    expired: [],
    topUsers: [],
    loadingStatus: true,
    totalMonthlyValue: 0,
    archived: [],
    companyLocations: null,
    vendorsArr: [],
    tags: [],
    currentLocation: null,
    currentTag: null,
    currentCategory: null,
    currentDepartment: null,
    currentVendor: null,
    currentStatus: null,
    currentTerm: null,
    contractStartDate: null,
    contractEndDate: null,
    contractAlertDate: null,
    auditHistoryItems: [],
    activeImportFileName: "",
    contractHierarchy: {},
    ancestorContracts: [],
    contractPreferences: [],
    unselectedColumns: [],
    selectedColumns: [],
    successMessage: null,
    costCalculatorType: 'monthly',
    monthlyMinCost: null,
    monthlyMaxCost: null,
    totalMinCost: null,
    totalMaxCost: null,
  },

  mutations: {
    setTopUsers(state, users) {
      state.topUsers = users;
    },
    setLoadingStatus(state, status) {
      state.loadingStatus = status;
    },
    setCurrentContract(state, contract) {
      state.currentContract = contract;
    },
    setIndexPage(state, page) {
      state.indexPage = page;
    },
    setCompanyChannelKey(state, companyChannelKey) {
      state.companyChannelKey = companyChannelKey;
    },
    setChannelKey(state, channelKey) {
      state.channelKey = channelKey;
    },
    setTotalValue(state, value) {
      state.totalValue = value;
    },
    setIndexPerPage(state, perPage) {
      state.indexPerPage = perPage;
    },
    setInsightsIndexPerPage(state, perPage) {
      state.insightsIndexPerPage = perPage;
    },
    setLoading(state, loading) {
      state.loading = loading;
    },
    setActiveArr(state, arr) {
      state.active = arr;
    },
    setThirtyDaysArr(state, arr) {
      state.thirtyDays = arr;
    },
    setSixtyDaysArr(state, arr) {
      state.sixtyDays = arr;
    },
    setNinetyDaysArr(state, arr) {
      state.ninetyDays = arr;
    },
    setOpenEnded(state, arr) {
      state.openEnded = arr;
    },
    setExpiredArr(state, arr) {
      state.expired = arr;
    },
    setArchivedArr(state, arr) {
      state.archived = arr;
    },
    setTotalMonthlyValue(state, totalMonthlyValue) {
      state.totalMonthlyValue = totalMonthlyValue;
    },
    setCompanyLocations(state, locations) {
      state.companyLocations = locations;
    },
    setVendorsData(state, data) {
      state.vendorsArr = data.vendors.results;
    },
    setCurrentLocation(state, location) {
      state.currentLocation = location;
    },
    setCurrentTag(state, tag) {
      state.currentTag = tag;
    },
    setCurrentCategory(state, category) {
      state.currentCategory = category;
    },
    setCurrentDepartment(state, department) {
      state.currentDepartment = department;
    },
    setCurrentVendor(state, vendor) {
      state.currentVendor = vendor;
    },
    setCurrentStatus(state, status) {
      state.currentStatus = status;
    },
    setCurrentTerm(state, term) {
      state.currentTerm = term;
    },
    setContractStartDate(state, payload) {
      state.contractStartDate = payload;
    },
    setContractEndDate(state, payload) {
      state.contractEndDate = payload;
    },
    setContractAlertDate(state, payload) {
      state.contractAlertDate = payload;
    },
    clearContractFilter(state, filter) {
      state[filter.filterName] = null;
    },
    clearCalendarFilter(state, filter) {
      state[filter.filterName] = null;
    },
    setAuditHistoryItems(state, auditHistoryItems) {
      state.auditHistoryItems = auditHistoryItems;
    },
    setActiveImportFileName(state, value) {
      state.activeImportFileName = value;
    },
    setContractHierarchy(state, value) {
      state.contractHierarchy = value;
    },
    setAncestorContracts(state, value) {
      state.ancestorContracts = value;
    }, 
    setSelectedColumns(state) {
      state.selectedColumns = [...state.selectedColumns, ...state.unselectedColumns.filter(d => d.active)];
      state.unselectedColumns = state.unselectedColumns.filter(d => !d.active);
    },
    setSelectedColumn(state, column) {
      state.selectedColumns = column;
    },
    setUnselectedColumn(state, column) {
      state.unselectedColumns = column;
    },
    setUnselectedColumns(state) {
      state.unselectedColumns = [...state.selectedColumns.filter(d => d.active), ...state.unselectedColumns];
      state.selectedColumns = state.selectedColumns.filter(d => !d.active);
    },
    setContractPreferences: (state, preferences) => {
      state.contractPreferences = preferences;
    },
    clearActive(state) {
      state.selectedColumns.forEach(d => {
        d.active = false;
      });
      state.unselectedColumns.forEach(d => {
        d.active = false;
      });
    },
    setSuccessMessage: (state, successMessage) => {
      state.successMessage = successMessage;
    },
    setCostCalculatorType(state, type) {
      state.costCalculatorType = type;
    },
    setMonthlyMinCost(state, value) {
      state.monthlyMinCost = value;
    },
    setMonthlyMaxCost(state, value) {
      state.monthlyMaxCost = value;
    },
    setTotalMinCost(state, value) {
      state.totalMinCost = value;
    },
    setTotalMaxCost(state, value) {
      state.totalMaxCost = value;
    },
  },

  getters: {
    active: state => state.active,
    openEnded: state => state.openEnded,
    thirtyDays: state => state.thirtyDays,
    sixtyDays: state => state.sixtyDays,
    ninetyDays: state => state.ninetyDays,
    expired: state => state.expired,
    archived: state => state.archived,
    loading: state => state.loading,
    totalValue: state => state.totalValue,
    companyChannelKey: state => state.companyChannelKey,
    channelKey: state => state.channelKey,
    currentContract: state => state.currentContract,
    indexPage: state => state.indexPage,
    indexPerPage: state => state.indexPerPage,
    insightsIndexPerPage: state => state.insightsIndexPerPage,
    topUsers: state => state.topUsers,
    loadingStatus: state => state.loadingStatus,
    totalMonthlyValue: state => state.totalMonthlyValue,
    companyLocations: state => state.companyLocations,
    vendorsArr: state => state.vendorsArr,
    tags: state => state.tags,
    currentLocation: state => state.currentLocation,
    currentTag: state => state.currentTag,
    currentCategory: state => state.currentCategory,
    currentDepartment: state => state.currentDepartment,
    currentVendor: state => state.currentVendor,
    currentStatus: state => state.currentStatus,
    currentTerm: state => state.currentTerm,
    contractStartDate: state => state.contractStartDate,
    contractEndDate: state => state.contractEndDate,
    contractAlertDate: state => state.contractAlertDate,
    costCalculatorType: (state) => state.costCalculatorType,
    monthlyMinCost: (state) => state.monthlyMinCost,
    monthlyMaxCost: (state) => state.monthlyMaxCost,
    totalMinCost: (state) => state.totalMinCost,
    totalMaxCost: (state) => state.totalMaxCost,
    auditHistoryItems: state => state.auditHistoryItems,
    contractFiltersArray: (state) => {
      const formatDate = (dateStr) => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const year = date.getFullYear();
        return `${month}-${day}-${year}`;
      };    

      const filters = [
        {filter: state.currentLocation, filterName: 'currentLocation'},
        {filter: state.currentTag, filterName: 'currentTag'},
        {filter: state.currentCategory, filterName: 'currentCategory'},
        {filter: state.currentStatus, filterName: 'currentStatus'},
        {filter: state.currentTerm, filterName: 'currentTerm'},
        {filter: state.currentDepartment, filterName: 'currentDepartment'},
        {filter: state.currentVendor, filterName: 'currentVendor'},
        {
          filter: state.contractStartDate ? { name: `Start Date: ${formatDate(state.contractStartDate)}`, value: state.contractStartDate } : null,
          filterName: 'contractStartDate',
        },
        {
          filter: state.contractEndDate ? { name: `End Date: ${formatDate(state.contractEndDate)}`, value: state.contractEndDate } : null,
          filterName: 'contractEndDate',
        },
        {
          filter: state.contractAlertDate ? { name: `Alert Date: ${formatDate(state.contractAlertDate)}`, value: state.contractAlertDate } : null,
          filterName: 'contractAlertDate',
        },
      ];
      if (state.costCalculatorType === 'monthly') {
        if (state.monthlyMinCost || state.monthlyMaxCost) {
          filters.push({
            filter: {
              name: `Monthly Cost ($): ${state.monthlyMinCost || 0} - ${state.monthlyMaxCost || '∞'}`,
              value: [state.monthlyMinCost, state.monthlyMaxCost],
            },
            filterName: 'monthlyCostRange',
          });
        }
      } else if (state.costCalculatorType === 'total') {
        if (state.totalMinCost || state.totalMaxCost) {
          filters.push({
            filter: {
              name: `Total Cost ($): ${state.totalMinCost || 0} - ${state.totalMaxCost || '∞'}`,
              value: [state.totalMinCost, state.totalMaxCost],
            },
            filterName: 'totalCostRange',
          });
        }
      }
      return filters;
    },
    activeFilters: (state, getters) => getters.contractFiltersArray.filter(f => (f.filter !== null && f.filter !== undefined)),
    activeFiltersCount: (state, getters) => getters.activeFilters.length,
    activeCalendarFilters: (state, getters) => getters.contractFiltersArray.filter(f => (f.filter !== null && f.filter !== undefined)),
    activeCalendarFiltersCount: (state, getters) => getters.activeCalendarFilters.length,
    activeImportFileName: state => state.activeImportFileName,
    contractHierarchy: state => state.contractHierarchy,
    ancestorContracts: state => state.ancestorContracts,
    selectedColumns: (state)  => state.selectedColumns,
    unselectedColumns: (state)  => state.unselectedColumns,
    contractPreferences: (state) => state.contractPreferences,
    successMessage: state => state.successMessage,
  },

  actions: {
    filterAllTimespans({ dispatch }, obj) {
      dispatch('filterActive', obj);
      dispatch('filterOpenEnded', obj);
      dispatch('filterArchive', obj);
      dispatch('filterExpired', obj);
      dispatch('filterThirtyDays', obj);
      dispatch('filterSixtyDays', obj);
      dispatch('filterNinetyDays', obj);
    },

    filterActive({ commit }, obj) {
      const activeArr = [];
      const today = moment();
      Object.keys(obj).forEach(key => {
        const endDate = moment(new Date(obj[key].endDate).toISOString());
        if (endDate && (endDate > today) && obj[key].archived === false) {
          activeArr.push(obj[key]);
        }
      });
      commit('setActiveArr', activeArr);
    },

    filterExpired({ commit }, obj) {
      const expiredArr = [];
      const today = moment();

      Object.keys(obj).forEach(key => {
        const endDate = moment(new Date(obj[key].endDate).toISOString());
        if (obj[key].contractType !== "open_ended" && endDate && (endDate < today) && obj[key].archived === false) {
          expiredArr.push(obj[key]);
        }
      });
      commit('setExpiredArr', expiredArr);
    },

    filterThirtyDays({ commit }, obj) {
      const thirtyDaysFromNow = moment().add(30, 'days');
      const thirtyDaysArr = [];
      const today = moment();

      Object.keys(obj).forEach(key => {
        const endDate = moment(new Date(obj[key].endDate).toISOString());
        if (obj[key].contractType !== "open_ended" && endDate && (endDate > today && endDate <= thirtyDaysFromNow) && obj[key].archived === false) {
          thirtyDaysArr.push(obj[key]);
        }
      });
      commit('setThirtyDaysArr', thirtyDaysArr);
    },

    filterSixtyDays({ commit }, obj) {
      const thirtyDaysFromNow = moment().add(30, 'days');
      const sixtyDaysFromNow = moment().add(60, 'days');
      const sixtyDaysArr = [];

      Object.keys(obj).forEach(key => {
        const endDate = moment(new Date(obj[key].endDate).toISOString());
        if (obj[key].contractType !== "open_ended" && endDate && (endDate > thirtyDaysFromNow && endDate <= sixtyDaysFromNow) && obj[key].archived === false) {
          sixtyDaysArr.push(obj[key]);
        }
      });
      commit('setSixtyDaysArr', sixtyDaysArr);
    },

    filterNinetyDays({ commit }, obj) {
      const sixtyDaysFromNow = moment().add(60, 'days');
      const ninetyDaysFromNow = moment().add(90, 'days');
      const ninetyDaysArr = [];

      Object.keys(obj).forEach(key => {
        const endDate = moment(new Date(obj[key].endDate).toISOString());
        if (obj[key].contractType !== "open_ended" && endDate && (endDate > sixtyDaysFromNow && endDate <= ninetyDaysFromNow) && obj[key].archived === false) {
          ninetyDaysArr.push(obj[key]);
        }
      });
      commit('setNinetyDaysArr', ninetyDaysArr);
    },

    filterOpenEnded({ commit }, obj) {
      const openEndedContracts = [];
      Object.keys(obj).forEach(key => {
        if (obj[key].contractType === "open_ended") {
          openEndedContracts.push(obj[key]);
        }
      });
      commit('setOpenEnded', openEndedContracts);
    },

    filterArchive({ commit }, obj) {
      const archiveArr = [];
      Object.keys(obj).forEach(key => {
        if (obj[key].archived === true) {
          archiveArr.push(obj[key]);
        }
      });
      commit('setArchivedArr', archiveArr);
    },

    calculateTotalValue({ commit, getters }) {
      if (getters['contracts/contractsArr'].length > 0) {
        let sum = 0;
        const activeContracts = getters['contracts/contractsArr'].filter(item => !item.archived);
        for (let i = 0, len = activeContracts.length; i < len; i += 1) {
          sum = fixRounding(sum + parseFloat(activeContracts[i].contractValueAmount));
        }
        commit('setTotalValue', sum);
      } else {
        commit('setTotalValue', 0);
      }
    },

    calculateTotalMonthlyValue({ commit, getters }) {
      if (getters['contracts/contractsArr'].length > 0) {
        let sum = 0;
        const obj = getters['contracts/contractsArr'].filter(item => !item.archived);

        Object.keys(obj).forEach(key => {
          sum = fixRounding(sum + parseFloat(obj[key].monthlyCost));
        });
        commit('setTotalMonthlyValue', sum);
      } else {
        commit('setTotalMonthlyValue', 0);
      }
    },

    fetchContract({ commit }, id) {
      commit('setLoading', true);
      return http
        .get(`/contracts/${id}.json`)
        .then(res => {
          if (!res.data.message) {
            commit('setCurrentContract', res.data);
            commit('setCompanyChannelKey', res.data.companyChannelKey);
            commit('setLoading', false);
          } 
        });
    },

    fetchContractPreferences({ commit }) {
      http
        .get('/contracts_list_columns.json')
        .then((res) => {
          commit('setSelectedColumn', res.data.defaultSelectedCols);
          commit('setUnselectedColumn', res.data.defaultUnselectedCols);
          if (res.data.contractPreference) {
            commit('setContractPreferences', res.data.contractPreference.preference);
          }
          commit('setLoadingStatus', false);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error fetching ticket list columns. ${error.message}`);
        });
    },

    updateContractColumns({ commit }, value) {
      const params = { selected_columns: value };

      return http
        .put('/contracts_list_columns.json', params)
        .then((res) => {
          commit('setContractPreferences', value);
          commit('setSuccessMessage', res.data.message);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error saving data. ${error.response.data.message}`);
        });
    },

    fetchAuditHistory({ state, commit }) {
      if (!state.currentContract) return;

      const params = { 
        id: state.currentContract.id,
      };
      http
        .get('/contract_history', { params })
        .then(res => {
          commit('setAuditHistoryItems', res.data.auditHistoryItems);
        })
        .catch(error => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error while fetching Contract history (${error.response.data.message}).`);
        });
    },

    fetchCompanyLocations({ commit }) {
      commit('setLoading',true);
      http
        .get('/locations.json')
        .then((res) => {
          commit('setCompanyLocations', res.data.locations);
        })
        .catch((error) => {
          commit('GlobalStore/setErrorMessage',`Sorry, there was an error gathering company location data. Please refresh the page and try again: ${error.message}`);
        })
        .finally(() => commit('setLoading', false));
    },

    fetchVendors({ commit }, filterStatus) {
      const params = { };
      if (filterStatus) {
        params.archived = filterStatus;
      }
      commit('setLoadingStatus', true);
      http
        .get('/vendors.json', { params })
        .then(res => {
          commit('setVendorsData', res.data);
          commit('setLoading', false);
          commit('setLoadingStatus', false);
        })
        .catch(() => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error gathering vendor data. Please refresh the page and try again.`);
        });
    },

    fetchContractHierarchy({ commit }, id) {
      commit('setLoading', true);
      http
        .get(`/contracts/${id}/hierarchy.json`)
        .then(res => {
          commit('setContractHierarchy', res.data);
          commit('setLoading', false);
        })
        .catch(error => {
          commit('setLoading', false);
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error fetching contract's hierarchy. (${error.message}).`);
        });
    },

    fetchAncestorContracts({ commit }, id) {
      http
        .get(`/contracts/${id}/ancestor_contracts.json`)
        .then(res => {
          commit('setAncestorContracts', res.data);
        })
        .catch(error => {
          commit('GlobalStore/setErrorMessage', `Sorry, there was an error fetching ancestor contracts. (${error.message}).`, {root: true});
        });
    },

  },
});
