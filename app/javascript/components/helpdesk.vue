<template>
  <div v-if="showHelpdesk">
    <module-header
      v-if="!isOnlyBasicRead"
      ref="moduleHeader"
      module="HelpTickets"
      :show-header="showHeader"
      :container-class="containerClass"
      :dashboard-class="dashboardClass"
      :prop-is-write-any="isWriteAny"
      :prop-is-read-any="isReadAny"
    >
      <template #menu="{type}">
        <header-menu-vertical
          class="helpdesk-menu"
          :import-help-tickets-data="navigateToImportHelpTickets"
          :type="type"
        />
      </template>
      <template #horizontal>
        <header-menu-horizontal
          class="helpdesk-menu"
          :download-help-tickets-data="downloadHelpTicketsData"
          :import-help-tickets-data="navigateToImportHelpTickets"
        />
      </template>
      <template
        v-if="!showTaskScheduler"
        #callout
      >
        <helpdesk-link-callout
          v-if="requestEmail"
          class="mt-n2.5"
          :request-email="requestEmail"
          :setting="setting"
          :class="disableLinks"
        />
        <div
          v-else
          class="ml-sm-0 ml-md-0 ml-lg-2 d-inline-block text-right col-4 pr-0"
          :class="disableLinks"
        >
          <span class="skeleton skeleton__item col-6 ml-1 pt-n1 pb-4 rounded align-middle"/>
        </div>
      </template>
      <template #loader>
        <span
          v-if="ticketLoadingStatus"
          class="ml-3 d-inline-block"
        >
          <pulse-loader
            class="h2 d-inline-block mb-0"
            color="#0d6efd"
            size="0.5rem"
            loading
          />
        </span>
      </template>
      <template #right-nav>
        <div v-if="!isImportPage && (isWriteAny || isReadAny || isBasicAccess)">
          <span
            v-if="!showTaskScheduler "
            v-tooltip.bottom="tooltipMessage"
            class="horizontal-nav-import"
            :class="horizontalNav()"
          >
            <button
              v-if="(isWrite || isReadAny)"
              v-tooltip.top="'Export Help Ticket(s)'"
              class="btn btn-light rounded-circle mr-2 p-0 import-export-btn"
              :class="disableLinks"
              @click="downloadHelpTicketsData"
            >
              <i class="nulodgicon-cloud-download mt-auto text-secondary" />
            </button>
            <button
              v-if="isWrite"
              v-tooltip.top="'Import Help Ticket(s)'"
              class="btn btn-light rounded-circle mr-3 p-0 import-export-btn"
              :class="disableLinks"
              @click="navigateToImportHelpTickets"
            >
              <i class="nulodgicon-cloud-upload mt-auto text-secondary" />
            </button>
          </span>
          <router-link
            v-if="showResponse && !isRead"
            id="addNewResponseBtn"
            to="/responses/new"
            class="btn btn-primary float-right"
            data-tc-add-response
          >
            <i class="nulodgicon-plus-round mr-2" />
            Add Response
          </router-link>
          <router-link
            v-else-if="showTask && !isRead"
            id="addNewTaskBtn"
            to="/tasks/new"
            class="btn btn-primary float-right"
            data-tc-add-task
          >
            <i class="nulodgicon-plus-round mr-2" />
            Add Task
          </router-link>
          <router-link
            v-else-if="showTasksChecklist && !isRead"
            id="addNewTaskChecklistBtn"
            class="btn btn-primary float-right"
            :to="{ path: '/task_checklists?new=true' }"
            data-tc-add-tasks-checklist
          >
            <i class="nulodgicon-plus-round mr-2" />
            Add Task Checklist
          </router-link>
          <a
            v-else-if="showSurveys && !isRead"
            href="#"
            class="btn btn-primary float-right"
            @click.stop.prevent="showModal"
          >
            <i class="nulodgicon-plus-round mr-2" />
            Add Survey
          </a>
          <a
            v-else-if="showDocuments && !isRead"
            href="#"
            class="btn btn-primary float-right"
            data-tc-add-document
            @click.stop.prevent="showModal"
          >
            <i class="nulodgicon-plus-round mr-2" />
            Add Document
          </a>

          <a
            v-else-if="showBlockedMail && isWrite"
            href="#"
            class="btn btn-primary float-right"
            data-tc-add-blocked
            @click.stop.prevent="showModal"
          >
            Add Email/Domain
          </a>

          <a
            v-else-if="showBlockedKeywords && isWrite"
            href="#"
            class="btn btn-primary float-right"
            @click.stop.prevent="showModal"
          >
            Add Word/Phrase
          </a>

          <div
            v-else-if="showCustomForms && isWrite"
            class="float-right"
          >
            <router-link
              :to="{ path: '/settings/custom_forms/new' }"
              class="btn btn-primary float-right"
              data-tc-add-custom-form
            >
              <i class="nulodgicon-plus-round mr-2" />
              Add Form
            </router-link>

            <a
              href="#"
              class="btn btn-outline-primary float-right mr-4"
              data-tc-copy-custom-form
              @click="openCopyFormModal"
            >
              Copy Form
            </a>
          </div>

          <div
            v-else-if="showWorkspaces && isWrite"
            class="float-right"
          >
            <router-link
              :to="{ path: '/workspaces?new=true' }"
              class="btn btn-primary float-right"
              data-tc-add-workspace
            >
              <i class="nulodgicon-plus-round mr-2" />
              Add Workspace
            </router-link>
          </div>

          <span
            v-else-if="showArticles && !isRead"
            class="float-right"
          >
            <new-article-dropdown />
          </span>

          <span v-else-if="showAutomatedTasks && isWrite">
            <router-link
              :to="{ path: '/automated_tasks?new=true' }"
              class="btn btn-primary"
              href="#"
              role="button"
              data-tc-add-new-task
            >
              <i class="nulodgicon-plus-round" />
              New Task
            </router-link>
          </span>

          <router-link
            v-else-if="showFaqs && !isScoped && !isBasicAccess && !isRead"
            to="/faqs/new"
            class="btn btn-primary float-right"
          >
            <i
              class="nulodgicon-plus-round mr-2"
              data-tc-add-faq
            />
            Add FAQ
          </router-link>

          <div
            v-else-if="showTaskScheduler && isWrite"
            class="float-right"
          >
            <router-link
              :to="{ path: '/scheduled_tasks?new=true' }"
              class="btn btn-primary float-right"
              data-tc-btn="add scheduled task"
            >
              <i class="nulodgicon-plus-round mr-2" />
              Create Scheduled Task
            </router-link>
          </div>
          <div
            v-else-if="showPolicy && isWrite"
            class="float-right"
          >
            <router-link
              :to="{ path: '/settings/sla/policies/new' }"
              class="btn btn-primary float-right"
              data-tc-add-policy
            >
              <i class="nulodgicon-plus-round mr-2" />
              Add Policy
            </router-link>
          </div>
          <div
            v-else-if="showReport && isWrite"
            class="float-right"
          >
            <router-link
              :to="{ path: '/reports/saved?new=true' }"
              class="btn btn-primary float-right"
            >
              <i class="nulodgicon-plus-round mr-2" />
              New Report
            </router-link>
          </div>
          <div
            v-else
            v-tooltip.bottom="tooltipMessage"
            class="float-right"
          >
            <span
              id="primary_ctas"
              :class="disableLinks"
            >
              <new-record-dropdown
                v-if="(isWriteAny || isBasicAccess) && (!showFaqs && !showWorkspaces)"
                company-module="helpdesk"
              >
                <template #title>
                  Add Ticket
                </template>
              </new-record-dropdown>
            </span>
          </div>
        </div>
      </template>
      <template #content>
        <router-view ref="component" />
        <copy-custom-form-modal ref="copyCustomFormModal" />
        <ticket-export-modal ref="ticketExportModal" />
      </template>
      <template #additional-content>
        <Teleport to="body">
          <notifications position="bottom right" />
        </Teleport>
        <module-onboarding-template
          v-if="isWriteAny || $isSampleCompany"
          ref="onboarding"
          :tip-card-info="tipCardInfo"
          :step="step"
          @step="moveToStep"
          @close-walkthrough="onCloseWalkthrough"
        />
      </template>
    </module-header>

    <end-user-helpdesk
      v-if="isOnlyBasicRead"
    />
    <downloading-popup 
      v-if="isDownloadAsset"
      @close-widget="closeDownloadingWidget" 
    />
  </div>
</template>

<script>
  import { mapActions, mapMutations, mapGetters } from 'vuex';
  import vClickOutside from 'v-click-outside';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import mspHelper from 'mixins/msp_helper';
  import DownloadingPopup from 'components/assets_downloading_popup.vue';
  import http from '../common/http';
  import e2eTestHelper from '../mixins/e2e_test_helper';
  import permissionsHelper from '../mixins/permissions_helper';
  import ModuleOnboardingTemplate from "./shared/module_onboarding_template.vue";
  import NewRecordDropdown from "./shared/custom_forms/new_record_dropdown.vue";
  import CopyCustomFormModal from "./shared/custom_forms/modals/copy_custom_form_modal.vue";
  import HelpdeskLinkCallout from "./help_tickets/helpdesk_link_callout.vue";
  import TicketExportModal from "./shared/ticket_export_modal.vue";
  import helpdeskOnboardingMixin from '../mixins/module_onboardings/helpdesk';
  import HeaderMenuVertical from './help_tickets/header_menu_vertical.vue';
  import HeaderMenuHorizontal from './help_tickets/header_menu_horizontal.vue';
  import ModuleHeader from "./shared/module_header.vue";
  import EndUserHelpdesk from './help_tickets/end_user_helpdesk.vue';
  import NewArticleDropdown from './knowledge_base/new_article_dropdown.vue';
  import modernViewsHelper from '../mixins/modern_views_helper';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      ModuleHeader,
      HeaderMenuVertical,
      PulseLoader,
      ModuleOnboardingTemplate,
      NewRecordDropdown,
      CopyCustomFormModal,
      HelpdeskLinkCallout,
      TicketExportModal,
      EndUserHelpdesk,
      HeaderMenuHorizontal,
      NewArticleDropdown,
      DownloadingPopup,
    },
    mixins: [
      e2eTestHelper,
      helpdeskOnboardingMixin,
      permissionsHelper,
      mspHelper,
      modernViewsHelper,
    ],
    data() {
      return {
        customFormOptions: [],
        companyId: null,
        workspaceId: null,
      };
    },
    computed: {
      ...mapGetters([
        'loading',
        'pageCount',
        'page',
        'perPage',
        'requestEmail',
        'filterParams',
        'ticketLoadingStatus',
        'setting',
      ]),
      ...mapGetters('GlobalStore', ['errorMessage']),
      ...mapGetters('GlobalStore', ['downloadingAssetReports', 'isDownloadAsset']),
      defaultForm() {
        if (!this.customForms) {
          return [];
        }
        return this.customForms.find(x => x.default);
      },
      showWorkspaces() {
        return this.$route.path.startsWith("/workspaces");
      },
      showResponse() {
        return this.$route.path.startsWith("/responses");
      },
      showTask() {
        return this.$route.path.startsWith("/tasks");
      },
      showTasksChecklist() {
        return this.$route.path.startsWith("/task_checklists");
      },
      showSurveys() {
        return this.$route.path.startsWith("/surveys");
      },
      showBlockedMail() {
        return this.$route.path.startsWith("/settings/blocked_mails");
      },
      showBlockedKeywords() {
        return this.$route.path.startsWith("/settings/blocked_keywords");
      },
      showDocuments() {
        return this.$route.path.startsWith("/documents");
      },
      showArticles() {
        return this.$route.path.startsWith("/articles");
      },
      showAutomatedTasks(){
        return this.$route.path.startsWith("/automated_tasks");
      },
      showFaqs() {
        return this.$route.path.startsWith("/faqs");
      },
      showTaskScheduler() {
        return this.$route.name === 'scheduled-tasks';
      },
      showCustomForms() {
        return this.$route.path.startsWith("/settings/custom_forms");
      },
      showPolicy() {
        return this.$route.path.startsWith("/settings/sla/policies");
      },
      isHelpDeskSettings(){
        return this.$route.name === 'settings-survey';
      },
      showReport() {
        return this.$route.path.startsWith("/reports");
      },
      newTicketUrl() {
        return this.$route.path.startsWith('/new');
      },
      showTickets() {
        return this.$route.name === 'help-tickets';
      },
      isShowPage() {
        return /\/\d+/.test(this.$route.path);
      },
      isResourcePath() {
        return this.$route.path.startsWith("/response") ||
                this.$route.path.startsWith("/faqs") ||
                this.$route.path.startsWith("/documents");
      },
      hasPermissions() {
        if ($permissions) {
          // If there is no permissions at all, then return false
          if (!this.isWriteAny && !this.isRead) {
            return false;
          }
          // These routes are available to anyone
          if (this.$route.path === "/" || this.$route.path === "/new" || this.isShowPage || this.isResourcePath) {
            return true;
          }
          if (this.$route.path === "/dashboard" && (this.isReadAny || this.isWrite)) {
            return true;
          }
          return this.isWrite;
        }
        return true;
      },
      showHeader() {
        return (
          this.$route.name !== 'empty-help-tickets' &&
          !(/^\/\d+/.test(this.$route.fullPath) &&
          this.$route.name === "help-ticket-show") &&
          !/^\/\d+\//.test(this.$route.fullPath) &&
          this.$route.name !== 'new-help-ticket' &&
          this.$route.name !== 'automated-task-new' &&
          this.$route.name !== 'automated-task-edit' &&
          !/^\/settings\/custom_forms\/new/.test(this.$route.fullPath) &&
          !/^\/settings\/custom_forms\/\d+/.test(this.$route.fullPath) &&
          !/denied$/.test(this.$route.fullPath) &&
          this.$route.name !== "show-report" &&
          this.$route.name !== 'new-article' &&
          this.$route.name !== 'show-article' &&
          this.$route.name !== 'update-article'
        );
      },
      dashboardClass() {
        return (
          this.$route.name === "ticket-dashboard" &&
          window.innerWidth < 900
        );
      },
      containerClass() {
        return this.$route.name === "help-ticket-show";
      },
      isImportPage() {
        return this.$route.path === "/import_help_tickets";
      },
    },
    watch: {
      errorMessage() {
        if (this.errorMessage) {
          this.emitError(this.errorMessage);
          this.$store.commit('GlobalStore/setErrorMessage', null);
        }
      },
      companyId() {
        this.fetchCustomNameSetting();
      },
    },
    methods: {
      ...mapMutations([
        'setPage',
        'stopRefresh',
        'setRequestEmail',
      ]),
      ...mapActions([
        'shouldDisplayOldSurveySettings',
        'loadRequestEmail',
        'shouldDisplayAiComments',
      ]),
      ...mapMutations('GlobalStore', ['updateDownloadingAssetReports', 'setIsDownloadAsset']),
      ...mapActions('GlobalStore', ['setupPusherListener', 'updateReportPercentage', 'checkReportProgess']),

      onWorkspaceChange() {
        this.applyDefaultVerticalNav();
        this.shouldDisplayAiComments();
        this.shouldDisplayOldSurveySettings();
        this.checkBasicUserDashboard();
        this.loadRequestEmail();
        // Get workspace title to show in module heading 
        if (this.$refs.moduleHeader) {
          this.$refs.moduleHeader.getTitle();
        }
        if (!this.companyId) {
          this.companyId = parseInt(this.$currentCompanyId, 10);
        }
        const workspace = getWorkspaceFromStorage();
        if (!workspace) {
          return;
        }
        this.workspaceId = workspace.id;
        if (this.$route.path.startsWith("/settings") && !this.hasPermissions) {
          this.$router.push("/");
        }
        if (this.$route.path.startsWith("/import_help_tickets") && !this.isWriteAny) {
          this.$router.push("/");
        }
        if ((this.$route.path === "/dashboard") && !(this.isWrite || this.isReadAny)) {
          this.$router.push("/");
        }
        this.setupPusherListeners();
        this.checkReportProgess(this.$currentCompanyUserId);
        this.setupPusherListener();
      },
      closeDownloadingWidget() {
        this.setIsDownloadAsset(false);
      },
      checkBasicUserDashboard() {
        if (this.isOnlyBasicRead && this.showHelpdesk) {
          const routesToCheck = ['/', '/dashboard'];
          if (routesToCheck.includes(this.$route.path)) {
            this.$router.push('/end_user_tickets');
          }
        } else if (!this.isOnlyBasicRead && this.showHelpdesk) {
          const routesToCheck = ['/end_user_dashboard', '/end_user_tickets'];
          if (routesToCheck.includes(this.$route.path) || this.$route.path.startsWith('/knowledge/workspaces/') || this.$route.path.startsWith('/faqs/workspaces/')) {
            this.$router.push('/');
          }
        }
      },
      horizontalNav() {
        const cookieData = document.cookie.split('; ').find(row => row.startsWith(`nav-theme`));
        if (cookieData && cookieData.split('=')[1] === 'vertical' && window.innerWidth > 1100) {
          return 'd-none';
        }
        return null;
      },
      setupPusherListeners() {
        if (this.$pusher) {
          const channel = this.$pusher.subscribe(`workspace=${this.workspaceId}`);
          channel.bind('permissions-updated', data => {
            if (data.contributor_id === this.$currentContributorId) {
              this.emitWarning("Your workspace permissions have been updated. You will be redirected to the tickets page in 3 seconds...");
              setTimeout(() => {
                window.location.href = '/help_tickets';
              }, 3000);
            }
          }, this);
        }
      },
      openCopyFormModal() {
        this.$refs.copyCustomFormModal.open();
      },
      showModal() {
        this.$refs.component.openModal();
      },
      downloadHelpTicketsData() {
        const params = { company_id: this.companyId, workspace_id: this.workspaceId };
        http
          .get(`/help_tickets/export`, { params })
          .then(() => {
            this.$refs.ticketExportModal.open();
          })
          .catch(() => {
            this.emitError("Something went wrong. Please refresh the page and try again.");
          });
      },

      redirectBack() {
        window.history.length > 2 ? this.$router.go(-1) : this.$router.push('/');
      },

      fetchCustomNameSetting() {
        if (this.companyId) {
          this.$store.dispatch("fetchHelpdeskSettings", this.companyId);
        }
      },
      navigateToImportHelpTickets() {
        this.$router.push("/import_help_tickets");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .import-export-btn {
    box-shadow: none;
    height: 2.375rem;
    width: 2.375rem;
  }

  .nulodgicon-ios-email-outline {
    color: $themed-base;
    font-size: 1.5rem;
    line-height: 1rem;
    vertical-align: middle;
  }
  .btn {
    @media($max: $large) {
      font-size: 0.85rem;
      max-width: 140px;
    }
    @media($max: $small) {
      font-size: 0.8rem;
    }
  }

  .simple-hidden {
    opacity: 0;
  }
</style>
