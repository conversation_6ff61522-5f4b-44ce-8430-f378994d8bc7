<template>
  <div class="readable-length text-secondary mx-auto pb-1">
    <div class="loading-bar">
      <div
        class="loading-bar-fill"
        :style="{ transform: 'scaleX('+ value + ')' }"
      />
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      value: { type: Number },
    },
  };
</script>

<style lang="scss" scoped>
$loader-height: 6px;
.readable-length  {
  top: 0;
}

.loading-bar {
  background-color: $themed-fair;
  background-color: $gray-400;
  border-radius: 0.5rem;
  height: $loader-height;
  max-width: 18.125rem;
  overflow: hidden;
  position: absolute;
  width: 100%;
  top: 0.125rem;
  left: 0.125rem;
}

.loading-bar-fill {
  background-color: $primary;
  height: $loader-height;
  left: 0;
  position: absolute;
  transform-origin: left center;
  top: 0;
  width: 100%;

  &.start-animation {
    animation: TRICKLE-LOAD 25s ease-in forwards;
  }
}

@keyframes TRICKLE-LOAD {
  0%   { transform: scaleX(0);   }
  20%  { transform: scaleX(0.3); }
  25%  { transform: scaleX(0.4); }
  40%  { transform: scaleX(0.45); }
  60%  { transform: scaleX(0.55); }
  65%  { transform: scaleX(0.6); }
  80%  { transform: scaleX(0.8); }
  100% { transform: scaleX(1);   }
}
</style>
