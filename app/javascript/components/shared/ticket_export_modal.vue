<template>
  <Teleport to="body">
    <sweet-modal
      ref="modal"
      v-sweet-esc
      :title="isAssetsModule ? 'Generating asset export...' : 'Generating excel file...'"
    >
      <template slot="default">
        <div class="text-center">
          <p v-if="isAssetsModule">
            We are processing your request, your file will be downloaded in a few minutes and a copy of the file will also be sent to you via email.
          </p>
          <p v-else>
            The file will be delivered to your email address within 10-15 minutes.
          </p>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click="close"
      >
        Done
      </button>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import { SweetModal } from 'sweet-modal-vue';

  export default {
    components: {
      SweetModal,
    },
    props: {
      isAssetsModule: {
        type: Boolean,
        default: false,
      },
    },
    methods: {
      open() {
        this.$refs.modal.open();
      },
      close() {
        this.$refs.modal.close();
      },
    },
  };
</script>
