<template>
  <div>
    <div class="form-group btn btn-link mb-0 text-left d-block white-space-normal mx-n2 border-0 clickable">
      <div class="row align-items-center">
        <div class="col">
          <label
            for="require-time-spent-to-close"
            class="mb-1 clickable"
          >
            Require a time-spent entry before closing.
          </label>
          <div class="true-small text-secondary">
            Only applies to tickets that are closed manually.
          </div>
        </div>
        <div class="col-auto">
          <material-toggle
            id="require-time-spent-to-close"
            :init-active="requireTimeSpentToClose"
            @toggle-sample="setRequireTimeSpentToClose($event)"
          />
        </div>
      </div>
    </div>

    <hr class="my-2.5">

    <div
      v-if="hasVisibleSmartListFields && allowsViewableInPortal"
      class="form-group btn btn-link mb-0 text-left d-block white-space-normal mx-n2 border-0 clickable"
    >
      <div class="row align-items-center">
        <div class="col">
          <label
            for="open-ticket-mode-check"
            class="mb-1 clickable"
          >
            <span class="text-danger required">
              I understand that I have the following fields in the Open Ticket Portal:
            </span>
            <ul class="font-weight-normal">
              <li
                v-for="(field, idx) in smartFieldList"
                :key="idx"
              >
                {{ field.label }}
              </li>
            </ul>
          </label>
        </div>
        <div class="col-auto">
          <material-toggle
            id="open-ticket-mode-check"
            :init-active="verifyVisibleSmartListFields"
            @toggle-sample="setVerifyVisibleSmartListFields($event)"
          />
        </div>
      </div>
      <hr class="my-2.5">
    </div>

    <div class="form-group btn btn-link mb-0 text-left d-block white-space-normal mx-n2 border-0 clickable">
      <div class="row align-items-center">
        <div class="col">
          <label
            for="show-checklist"
            class="mb-1 clickable"
          >
            Show checklist for <span class="text-blue">Assigned To</span> field.
          </label>
          <div class="true-small text-secondary">
            Only applies to internal users.
          </div>
        </div>
        <div class="col-auto">
          <material-toggle
            id="show-checklist"
            :init-active="showChecklist"
            @toggle-sample="setShowChecklist($event)"
          />
        </div>
      </div>
    </div>

    <hr class="my-2.5">

    <automatic-assignment
      class="form-group btn btn-link mb-0 text-left d-block white-space-normal mx-n2 border-0 clickable"
      :current-form="form"
      :contributor-error="contributorError"
      @selectedContributors="setSelectedContributors"
      @assignValue="setAssignValue"
    />

    <hr class="my-2.5">

    <div class="form-group btn btn-link mb-0 text-left d-block white-space-normal mx-n2 border-0 clickable">
      <div class="row align-items-center">
        <div class="col">
          <label
            v-if="displaySurveySettings"
            for="collect-closing-survey"
            class="mb-1 clickable"
          >
            Collect a satisfaction survey after close.
          </label>
          <div
            class="true-small text-secondary"
          >
            Manage surveys in         
            <a 
              href="/help_tickets/surveys"
              class="smallest"
              target="_blank"
            >
              <code class="smallest px-1 py-0">
                Resources &gt; Surveys
              </code>
            </a>
          </div>
        </div>
        <div
          v-if="displaySurveySettings"
          class="col-auto"
        >
          <material-toggle
            id="collect-closing-survey"
            :init-active="collectClosingSurvey"
            @toggle-sample="setCollectClosingSurvey($event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import _filter from "lodash/filter";
import _get from "lodash/get";
import customFormHelper from "mixins/custom_form_helper";
import strings from 'mixins/string';
import { mapGetters } from "vuex";

import permissionsHelper from 'mixins/permissions_helper';
import MaterialToggle from 'components/shared/material_toggle.vue';
import AutomaticAssignment from './automatic_assignment.vue';

export default {
  components: {
    AutomaticAssignment,
    MaterialToggle,
  },
  mixins: [customFormHelper, permissionsHelper, strings],
  props: [ 'form', 'contributorError', 'displaySurveySettings' ],
  data() {
    const helpdeskForm = this.form.moduleForm;
    return {
      verifyVisibleSmartListFields: false,
      defaultEmail: _get(helpdeskForm, 'email'),
      customEmail: _get(helpdeskForm, 'customEmail'),
      collectClosingSurvey: _get(helpdeskForm, 'collectClosingSurvey'),
      showChecklist: _get(helpdeskForm, 'showChecklist'),
      performedVerify: false,
      automatedAssignmentStatus: _get(helpdeskForm, 'automatedAssignmentStatus'),
      selectedContributors: _get(helpdeskForm, 'selectedContributors'),
      requireTimeSpentToClose: _get(helpdeskForm, 'requireTimeSpentToClose'),
      atAssignValue: true,
    };
  },
  computed: {
    ...mapGetters("customForms", ["smartLists"]),
    companyModule() {
      return 'helpdesk';
    },
    helpdeskForm() {
      return _get(this, 'form.moduleForm');
    },
    smartFieldList() {
      if (
        this.form &&
        this.form.formFields.length > 0
      ) {
        return _filter(
          this.form.formFields,
          (field) =>
            this.smartLists.includes(field.fieldAttributeType) && !field.private
        );
      }
      return [];
    },
    hasVisibleSmartListFields() {
      return this.smartFieldList.length > 0;
    },
    allowsViewableInPortal() {
      return this.form.showInOpenPortal;
    },
  },
  methods: {
    setVerifyVisibleSmartListFields(e) {
      this.verifyVisibleSmartListFields = e;
      this.emitInput();
    },
    setCollectClosingSurvey(e) {
      this.collectClosingSurvey = e;
      this.emitInput();
    },
    setShowChecklist(e) {
      this.showChecklist = e;
      this.emitInput();
    },
    setRequireTimeSpentToClose(e) {
      this.requireTimeSpentToClose = e;
      this.emitInput();
    },
    emitInput() {
      this.$emit('input', {
        verifyVisibleSmartListFields: this.verifyVisibleSmartListFields,
        collectClosingSurvey: this.collectClosingSurvey,
        customEmail: this.customEmail,
        email: this.defaultEmail,
        automatedAssignmentStatus: this.automatedAssignmentStatus,
        selectedContributors: this.selectedContributors,
        requireTimeSpentToClose: this.requireTimeSpentToClose,
        showChecklist: this.showChecklist,
        atAssignValue: this.atAssignValue,
      });
    },
    setSelectedContributors(contributors) {
      this.selectedContributors = contributors;
      this.emitInput();
    },
    setAssignValue(value) {
      this.atAssignValue = value;
      this.emitInput();
    },
  },
};
</script>

<style lang="scss" scoped>
  .form-check-input {
    margin-left: 0px !important;
  }
</style>
