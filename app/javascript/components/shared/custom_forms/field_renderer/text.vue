<template>
  <div>
    <div
      class="position-relative"
      style="cursor: pointer"
      @click.stop.prevent="openEdit(false)"
      @mouseenter="shouldShowSubjectHover ? hovering = false : hovering = true"
      @mouseleave="hovering = false"
    >
      <div
        v-if="!isSubjectField" 
        class="d-flex"
      >
        <slot name="label" />
        <edit-field-button 
          v-if="!editing && canEdit"
          :hovering="hovering"
          @openEdit="openEdit"
        />
      </div>
      <div 
        v-if="editing"
        :class="{ 'd-flex' : isHelpTicketShow }"
      >
        <div class="d-inline-block w-100">
          <input
            v-model="intialValue"
            class="form-control"
            maxlength="200"
            :placeholder="`Please enter ${field.label.toLowerCase()}`"
            :data-tc-input-field="field.label.toLowerCase()"
            @input="validate"
          >
        </div>
        <div 
          class="text-right mt-2 form-btn--responsive"
          :class="{'d-flex' : isHelpTicketShow}"
        >
          <button
            class="btn btn-sm btn-link text-secondary mr-2"
            @click.stop.prevent="cancel"
          >
            Cancel
          </button>
          <button
            class="btn btn-sm btn-primary form-btn--responsive"
            :disabled="disable"
            :data-tc-save-btn="field.label.toLowerCase()"
            @click.stop.prevent="updateFieldValue"
          >
            Save
          </button>
        </div>
      </div>
      <div v-else>
        <sync-loader
          :loading="isLoading"
          class="desc-loader"
          color="#0d6efd"
          size="0.5rem"
        />
        <div
          v-if="!isLoading"
          class="d-flex justify-content-between"
          style="cursor: pointer"
          @click.prevent.stop="openEdit(true)"
        >
          <div
            v-if="isSubjectField"
            class="d-flex w-100"
          >
            <h4
              v-if="isHelpTicketShow"
              class="align-middle text-secondary mb-0 h4--responsive"
            >
              {{ truncate(intialValue, 50) }}
            </h4>
            <p
              v-if="showSeeMore"
              class="clickable-text cursor-pointer"
              :class="{ 'ml-3': !(isHelpTicketShow && isSubjectField) }"
              @click="viewTicketDetails"
            >
              see more
            </p>
            <h5
              v-else-if="!isHelpTicketShow"
              class="align-middle text-secondary mb-0 h5--responsive break-word"
              data-tc-ticket-subject
            >
              {{ isQuickView ? truncate(intialValue, 80) : intialValue }}
            </h5>
            <div class="d-flex justify-content-between h-fit-content">
              <slot name="label" />
              <div
                @mouseenter="shouldShowSubjectHover && (hovering = true)"
                @mouseleave="(shouldShowSubjectHover || hovering !== true ) && (hovering = false)" 
              >
                <edit-field-button 
                  v-if="!isQuickView && !editing && isWritableFromCustomFormObject(object)"
                  :hovering="hovering"
                  :align-right="false"
                  @openEdit="openEdit"
                />
              </div>
            </div>
          </div>
          <div
            v-else
            class="w-100"
          >
            <div
              v-if="intialValue && intialValue.length"
              class="phone box__inner"
              :data-text-tc="field.name"
            >
              {{ intialValue }}
            </div>
            <button
              v-else
              class="form-control clickable text-muted text-left btn-sm"
              :disabled="!isWritableFromCustomFormObject(object)"
              @click.prevent.stop="openEdit"
            >
              {{ missingText }}
            </button>
          </div>
        </div>
      </div>
    </div>
    <sweet-modal
      ref="showTicketDetails"
      v-sweet-esc
      class="modal-style modern-modal"
      :title="'Ticket Details'"
      width="60%"
      max-height="90vh"
    >
      <div class="modal-content">
        <h5 class="modal-heading">Subject:</h5>
        <h4 class="modal-subject">{{ intialValue }}</h4>
  
        <h5
          v-if="object.description"
          class="modal-heading"
        >Description:</h5>
  
        <trix-render
          :id="field.id"
          :value="object.description"
          :max-height-px="350"
          view-description
          type="rich_text"
          class="modal-trix-render"
        />
      </div>
    </sweet-modal>  
  </div>
</template>

<script>
  import _get from 'lodash/get';
  import _debounce from "lodash/debounce";
  import { SweetModal } from 'sweet-modal-vue';
  import customForms from 'mixins/custom_forms';
  import SyncLoader from 'vue-spinner/src/SyncLoader.vue';
  import permissions from 'mixins/custom_forms/permissions';
  import fieldPermissions from 'mixins/custom_forms/field_permissions';
  import { mapGetters } from 'vuex';
  import EditFieldButton from 'components/shared/custom_forms/edit_field_button.vue';
  import strings from 'mixins/string';
  import helpTickets from 'mixins/help_ticket';
  import helpTicketDraft from 'mixins/help_ticket_draft';
  import TrixRender from "../../../trix_render.vue";

  export default {
    components: {
      SyncLoader,
      EditFieldButton,
      TrixRender,
      SweetModal,
    },
    mixins: [customForms, permissions, fieldPermissions, strings, helpTicketDraft, helpTickets],
    props: ['field', 'value', 'object', 'showLabel', 'isQuickView', 'isHelpTicketShow'],
    data() {
      return {
        editing: false,
        hovering: false,
        isLoading: false,
        disable: false,
        intialValue: _get(this, "value[0].valueStr", ''),
        originalValue: _get(this, 'value[0].valueStr', ''),
      };
    },
    computed: {
      ...mapGetters([
        'currentHelpTicket',
        'hideHeader',
        'currentHelpTicketDraft',
      ]),

      isSubjectField() {
        return this.field.name === 'subject';
      },
      missingText() {
        return `Enter ${this.field.label}.`;
      },
      textValue: {
        get() {
          return _get(this, "value[0].valueStr", this.field.defaultValue);
        },
        set(val) {
          this.intialValue = val;
        },
      },
      showSeeMore() {
        return this.intialValue.length > 50
           && this.isHelpTicketShow && this.field.name === 'subject';
      },
      shouldShowSubjectHover() {
        return this.isHelpTicketShow && this.isSubjectField;
      },
    },
    watch: {
      value() {
        if (this.currentHelpTicketDraft.fieldsData?.[this.field.id]) {
          this.intialValue = this.currentHelpTicketDraft.fieldsData?.[this.field.id];
        } else {
          this.intialValue = _get(this, "value[0].valueStr", '');
        }
      },
    },
    mounted() {
      if (this.enableEditing && this.currentHelpTicketDraft?.fieldsData[this.field.id]) {
        this.openEdit();
      }
    },
    methods: {
      textUpdate: _debounce(
        function () {
          if (this.intialValue && this.intialValue !== this.originalValue && this.currentHelpTicketDraft) {
            const fields = { ...this.currentHelpTicketDraft?.fieldsData} || {};
            if (this.field.id) {
              fields[this.field.id] = this.intialValue;
            }
            this.updateDraftFieldValue(fields);
          }
        },
        1000
      ),
      cancel() {
        this.intialValue = _get(this, "value[0].valueStr", this.field.defaultValue);
        this.closeEdit();
      },
      closeEdit() {
        this.editing = false;
        this.hovering = false;
        this.$emit('edit', false);
        if (this.draftExists && this.draftValue && this.field.fieldAttributeType === 'text') {
          this.handleDraftResolution('discard');
        }
      },
      openEdit(isSeeMore = false) {
        if ((this.isSubjectField && this.isWritableFromCustomFormObject(this.object)) || (!this.isSubjectField && this.canEdit)) {
          if (isSeeMore && this.isSubjectField && this.isHelpTicketShow) {
            this.editing = false;
          } else {
            this.editing = true;
          }
          this.$emit('edit', true);
        }
      },
      validate() {
        this.textUpdate();
        if (this.field.required && this.intialValue.trim().length < 1) {
          this.disable = true;
        } else {
          this.disable = false;
        }
      },
      updateFieldValue() {
        this.isLoading = true;
        this.addFormValue(this.object.id, {
          custom_form_field_id: this.field.id,
          value: this.intialValue,
          name: this.field.name,
          company_id: this.object.company.id,
        })
        .finally(() => {
          this.isLoading = false;
          this.closeEdit();
          if (this.isHelpTicketShow) {
            this.$store.dispatch("fetchTicket", this.currentHelpTicket?.id);
          }
        });
      },
      viewTicketDetails() {
        this.editing = false;
        this.$refs.showTicketDetails.open();
      },
    },
  };
</script>
<style lang="scss" scoped>
.clickable-text {
  color: blue;
  margin-top: 0.25rem;
  margin-left: 0.30rem;
}
.modal-style {
  margin-top: 3rem;
  overflow: auto;
}
.modern-modal {
  border-radius: 0.5rem;
  padding: 1.25rem;
  box-shadow: 0rem 0.25rem 0.625rem rgba(0, 0, 0, 0.15);
  border: 0.0625rem solid #ddd;
}
.modal-content {
  padding-bottom: 0.9375rem;
}
.modal-heading {
  font-size: 1rem;
  font-weight: 600;
  color: #555;
  margin-bottom: 0.5rem;
}
.modal-subject {
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.9375rem;
  word-break: break-word;
  overflow-wrap: anywhere;
  max-width: 100%;
}
.modal-trix-render {
  border: 0.0625rem solid #ddd;
  border-radius: 0.375rem;
  padding: 0.625rem;
  overflow-y: auto;
}
</style>
