<template>
  <div class="subpage-menu mt-2">
    <router-link
      v-if="isWriteAny || isReadAny || isBasicAccess"
      :class="['subpage-menu__item', isActiveClass('/articles')]"
      to="/articles"
      data-tc-sub-menu="knowledge base"
    >
      Knowledge Base
    </router-link>
    <router-link
      v-if="isWriteAny || isReadAny"
      id="articlesBtn"
      :class="['subpage-menu__item', isActiveClass('/responses')]"
      to="/responses"
      data-tc-sub-menu="canned response"
    >
      Canned Responses
    </router-link>
    <router-link
      v-if="isWriteAny || isReadAny"
      id="tasksChecklistBtn"
      :class="['subpage-menu__item', isActiveClass('/task_checklists')]"
      to="/task_checklists"
      data-tc-sub-menu="task checklists"
    >
      Task Checklists
    </router-link>
    <router-link
      v-if="isWriteAny || isReadAny"
      id="surveysBtn"
      :class="['subpage-menu__item', isActiveClass('/surveys')]"
      to="/surveys"
    >
      Surveys
    </router-link>
    <router-link
      v-if="isWriteAny || isReadAny"
      :class="['subpage-menu__item', isActiveClass('/documents')]"
      to="/documents"
      data-tc-sub-menu="document"
    >
      Documents
    </router-link>
    <router-link
      v-if="isWriteAny || isReadAny || isBasicAccess"
      :class="['subpage-menu__item', isActiveClass('/faqs')]"
      to="/faqs"
      data-tc-sub-menu="FAQ"
    >
      FAQ
    </router-link>
  </div>
</template>

<script>
  import permissionsHelper from "mixins/permissions_helper";

  export default {
    mixins: [ permissionsHelper ],
    methods: {
      onWorkspaceChange() {
        if (this.isScoped) {
          if (this.$route.path.startsWith("/responses")) {
            this.$router.push("/responses").catch(()=>{});
          } else if (this.$route.path.startsWith("/tasks")) {
            this.$router.push("/tasks").catch(()=>{});
          } else if (this.$route.path.startsWith("/task_checklists")) {
            this.$router.push("/task_checklists").catch(()=>{});
          } else if (this.$route.path.startsWith("/documents")) {
            this.$router.push("/documents").catch(()=>{});
          } else if (this.$route.path.startsWith("/faqs")) {
            this.$router.push("/faqs").catch(()=>{});
          }
        }
      },
      isActiveClass(path) {
        return { 'router-link-exact-active': this.isCurrentPath(path) };
      },
      isCurrentPath(path) {
        return this.$route.path.includes(path) || this.$route.path === `${path}/`;
      },
    },
  };
</script>
