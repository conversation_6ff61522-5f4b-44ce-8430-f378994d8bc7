<template>
  <div
    v-if="!timespentComment"
    :class="{'mb-3': !lastComment}"
  >
    <div
      v-if="commentsVisibility"
      class="comments-container border bg-themed-box-bg rounded mt-2"
      data-tc-comment-body
      :data-comment-id="commentObj.id"
    >
      <div
        v-if="!showCommentEdit"
        class="clearfix py-3 position-relative"
        :class="{'private': commentObj.privateFlag}"
      >
        <div
          v-if="!isQuickView"
          class="float-right px-2"
        >
          <a
            v-if="showArticleButton || isScheduledView"
            v-tooltip="isScheduledView ? 'Add Comment' : 'Create Article'"
            href="#"
            class="comment-action px-2"
            @click.stop.prevent="handleCommentAction"
          >
            <i class="nulodgicon-plus-round" />
          </a>
          <a
            v-if="isSubjectExist && isWriteAny && !isScheduledView"
            v-tooltip="'Split Ticket'"
            href="#"
            class="comment-action px-2"
            @click.stop.prevent="openSplitTicketModal"
          >
            <i class="genuicon-ticket" />
          </a>
          <a
            v-if="commentObj.contributorId === currentContributorId && (isWriteAny || isBasicAccess)"
            v-tooltip="'Edit Comment'"
            href="#"
            class="comment-action px-2"
            @click.stop.prevent="toggleEditComment"
          >
            <i class="genuicon-pencil-square-o" />
          </a>
          <a
            v-if="canDelete"
            :id="`delete-comment-icon-${commentObj.id}`"
            v-tooltip="'Delete Comment'"
            href="#"
            class="comment-action px-2"
            data-tc-delete-comment-icon
            @click.stop="deleteComment"
          >
            <i class="nulodgicon-trash-b remove-link clickable h5"/>
          </a>
          <span
            :id="`delete-comment-loader-${commentObj.id}`"
            class="comment-action px-2 d-none"
          >
            <pulse-loader
              :loading="true"
              class="d-inline-block"
              color="#000"
              size="1rem"
            />
          </span>
        </div>
        <div v-else>
          <span
            v-tooltip="'Comment Actions'"
            v-click-outside="closeCommentActions"
            class="clickable mx-2 float-right"
            @click="showCommentActions = !showCommentActions"
          >
            <i class="genuicon-ellipsis-v" />
          </span>
          <div
            class="dropdown-menu comment-actions-dropdown not-as-small"
            :class="{ 'show': showCommentActions }"
          >
            <div
              v-if="isSubjectExist && isWriteAny && !isScheduledView"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="openSplitTicketModal"
            >
              <i class="genuicon-ticket mr-2 small" />Split Ticket
            </div>
            <div
              v-if="commentObj.contributorId === currentContributorId && isWriteAny && !isScheduledView"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop.prevent="toggleEditComment"
            >
              <i class="genuicon-pencil-square-o mr-2 small" />Edit Comment
            </div>
            <div
              v-if="canDelete"
              :id="`delete-comment-icon-${commentObj.id}`"
              class="text-secondary dropdown-text-menu px-3 py-2"
              @click.stop="deleteComment"
            >
              <i class="nulodgicon-trash-b mr-2 small"/>Delete Comment
            </div>
            <span
              :id="`delete-comment-loader-${commentObj.id}`"
              class="commentObj-action px-2 d-none"
            >
              <pulse-loader
                :loading="true"
                class="d-inline-block"
                color="#000"
                size="0.5rem"
              />
            </span>
          </div>
        </div>
        <div class="row mx-1">
          <div class="col-auto">
            <avatar
              v-if="username"
              :size="40"
              :username="username"
              :src="userAvatar"
              class="d-inline-block logo-outline mt-1"
            />
          </div>
          <div
            class="col pl-0"
            :class="{ 'comment-body': !isQuickView }"
          >
            <trix-render
              v-if="commentObj"
              :id="commentObj.id"
              :value="commentObj.commentBody"
              :last-comment="lastComment"
              is-ticket-comment
              view-more
              data-tc-comment
              @display-comments="displayComments"
            />
            <div class="small mt-0 p--responsive">
              <router-link
                v-if="isMergedTicketComment"
                :to="{ path: `${commentObj.mergedTicketId}` }"
                target="_blank"
              >
                <img
                  v-tooltip="getTooltip"
                  src="https://nulodgic-static-assets.s3.amazonaws.com/images/merge-icon.svg"
                  class="source-icon mr-1"
                >
              </router-link>
              <img
                v-if="sourceImgPath"
                height="20"
                :src="sourceImgPath"
                :alt="toTitle(commentObj.source)"
                class="mr-1 mb-1"
              >
              <span v-if="isCommentor">
                <span class="text-muted"><strong>You</strong>, {{ createdAt }}</span>
              </span>

              <span
                v-else
                class="text-muted"
              >
                <strong v-if="username">{{ username }}
                  <sup
                    v-if="commentObj.source === 'auto_generated'"
                    v-tooltip="'This is an auto-generated comment.'"
                    class="text-muted nulodgicon-information-circled small"
                  /> ,
                </strong>
                {{ createdAt }}
              </span>

              <span
                v-if="isCommentUpdated"
                class="text-muted"
              >
                <span class="mx-1">&bull;</span>
                <span v-tooltip="`Last updated at: ${ updatedAt }`">
                  Edited
                </span>
              </span>
              <div v-if="isScheduledView && commentObj.taskStartedAt">
                <span class="text-muted"><strong>Scheduled For:</strong> {{ scheduledAt }}</span>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="commentObj.privateFlag"
          class="private-copy"
        >
          <small class="private-text text-secondary">
            <i class="nulodgicon-locked pr-1 text-alternate" />
            <template
              v-for="(user, index) in privateContributors.slice(0,2)"
            >
              <span
                :key="user.id"
                class="p-1 private-user"
              >
                <a
                  :href="profileLink(user)"
                  target="_blank"
                  class="text-alternate"
                >
                  {{ userName(user) }}
                  <span v-if="privateContributors.length > 1 && index < privateContributors.length-1">,</span>
                </a>
              </span>
            </template>
            <span
              v-if="privateContributors.length > 2"
              class="private-user clickable text-alternate"
              @click="openPrivateContributorsModal"
            >
              show all&hellip;
            </span>
          </small>
        </div>
        <private-contributors-list
          ref="privateContributorsListModal"
          :private-contributors="privateContributors"
        />
      </div>

      <edit-comment
        v-else
        usage="Comment"
        :comment="commentObj"
        :private-contributors="privateContributors"
        :is-quick-view="isQuickView"
        is-editing
        show-private-users-option
        @update-save-status="toggleEditComment"
        @hide-editor="hideEditor"
        @private-contributors="updatePrivateContributors"
      />

      <Teleport to="body">
        <split-ticket-modal
          ref="splitTicketModal"
          :is-splitting="splittingTicket"
          :comment="commentObj"
          @select-user="selectUser"
          @remove-user="removeUser"
          @split-ticket="splitTicket"
        />
      </Teleport>
      <Teleport to="body">
        <article-modal 
          ref="articleModal"
          is-comment-article
          :selected-comment="commentObj"
          @article-created="loadHelpTicket"
        />
      </Teleport>
    </div>
  </div>
</template>
<script>
  import MomentTimezone from 'mixins/moment-timezone';
  import { Avatar } from 'vue-avatar';
  import { mapGetters, mapActions, mapMutations } from 'vuex';
  import _get from 'lodash/get';
  import http from 'common/http';
  import strings from 'mixins/string';
  import permissionsHelper from "mixins/permissions_helper";
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import helpTickets from 'mixins/help_ticket';
  import vClickOutside from 'v-click-outside';
  import _cloneDeep from 'lodash/cloneDeep';
  import ArticleModal from 'components/knowledge_base/article_modal.vue';
  import EditComment from '../add_comment.vue';
  import PrivateContributorsList from './private_contributors_list.vue';
  import TrixRender from '../../../trix_render.vue';
  import SplitTicketModal from '../../split_ticket_modal.vue';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      Avatar,
      TrixRender,
      PrivateContributorsList,
      EditComment,
      SplitTicketModal,
      PulseLoader,
      ArticleModal,
    },
    mixins: [MomentTimezone, permissionsHelper, strings, helpTickets],
    props: ['comment', 'lastComment','commentsVisibility', 'isQuickView', 'isScheduledCommentView'],
    data() {
      return {
        showCommentEdit: false,
        isSplitting: false,
        selectedContributor: null,
        timeSpentCommentId: null,
        editableComment: {...this.comment},
        showCommentActions: false,
        commentObj: _cloneDeep(this.comment),
        isScheduledView: this.isScheduledCommentView ?? false,
      };
    },
    computed: {
      ...mapGetters([
        'companyUserOptions',
        'contractAssetTicketNames',
        'currentHelpTicket',
        'timeSpents',
        'currentHelpTicketDraft',
      ]),
      showArticleButton() {
        return this.isSubjectExist && this.isWriteAny && this.commentObj.isAgentComment && !this.commentObj.isArticlePresent;
      },
      isCommentor() {
        if ((this.commentObj.contributorId || this.$superAdminUser) &&
          this.commentObj.contributorId === this.currentContributorId &&
          this.commentObj.source === 'manually_added') {
            return true;
          }
        return false;
      },
      splittingTicket() {
        return this.isSplitting;
      },
      isSubjectExist() {
        return this.commentObj.commentText;
      },
      currentContributorId() {
        return this.$currentContributorId;
      },
      isCommentUpdated() {
        return this.commentObj.updatedAt > this.commentObj.createdAt;
      },
      privateContributors() {
        return this.commentObj.privateContributors;
      },
      scheduledAt() {
        const { date, startTime, timeZone } = this.commentObj.taskStartedAt || {};
        if (!date || !startTime || !timeZone) return '';
        const utcDatetime = this.$moment.utc(`${date}T${startTime}:00`);
        const localDatetime = utcDatetime.clone().tz(timeZone);
        const formattedTime = this.timezoneDatetime(localDatetime, timeZone);
        const timeZoneName = this.timezoneMapping[timeZone] || '';
        return `${formattedTime} ${timeZoneName}`;
      },
      createdAt() {
        return this.timezoneDatetime(this.commentObj.createdAt, Vue.prototype.$timezone);
      },
      updatedAt() {
        return this.timezoneDatetime(this.commentObj.updatedAt, Vue.prototype.$timezone);
      },
      username() {
        if (this.commentObj.source === "auto_generated") {
          return "Auto Generated ";
        } else if (!this.commentObj.contributor && this.commentObj.source === "manually_added") {
          return "System";
        }
        return _get(this, "comment.contributor.name") || _get(this, "comment.email") || "";
      },
      userAvatar() {
        return _get(this, "comment.contributor.avatar");
      },
      sourceImgPath() {
        if (this.commentObj.source === 'slack') {
          return "https://nulodgic-static-assets.s3.amazonaws.com/images/slack.svg";
        } else if (this.commentObj.source === 'ms_teams') {
          return "https://nulodgic-static-assets.s3.amazonaws.com/images/ms_teams.svg";
        } else if (this.commentObj.source === 'manually_added') {
          return 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/manual.png';
        }
        return null;
      },
      contractAssetTicketNamesPresent() {
        return this.contractAssetTicketNames && Object.keys(this.contractAssetTicketNames).length > 0;
      },
      timeSpent() {
        const timeSpents = this.timeSpents.find(timeSpent => timeSpent.helpTicketCommentId === this.commentObj.id);
        if (timeSpents) {
          this.setTimeSpentCommentId(timeSpents.helpTicketCommentId);
          return true;
        }
        return null;
      },
      timespentComment() {
        this.displayComments();
        return this.timeSpent && this.timeSpentCommentId;
      },
      canDelete() {
        return this.commentObj.contributorId === this.currentContributorId || this.isWrite;
      },
      getTooltip() {
        const data = this.commentObj.mergedTicketData;
        return data ? `#${data.ticketNumber} ${data.subject}` : '';
      },
      isMergedTicketComment() {
        return !!this.commentObj.mergedTicketId;
      },
    },
    watch: {
      comment: {
        handler(newValue, oldValue) {
          if (newValue !== oldValue) {
            this.commentObj = _cloneDeep(this.comment);
          }
        },
        deep: true,
      },
    },
    methods: {
      ...mapMutations([
        'setCurrentHelpTicket',
        'setLoadingTicket',
        'setQuickViewTicketId',
        'setComments',
      ]),
      ...mapActions([
        'fetchSelectedContributor',
      ]),
      onWorkspaceChange() {
        if (this.enableEditing && this.currentHelpTicketDraft?.comments[this.editableComment.id]) {
          this.toggleEditComment();
        }
      },
      openPrivateContributorsModal() {
        this.$refs.privateContributorsListModal.open();
      },
      openArticleModal() {
        this.$refs.articleModal.reset();
        this.$refs.articleModal.open();
      },
      handleCommentAction() {
        if (this.isScheduledView) {
          this.addScheduledComment();
        } else {
          this.openArticleModal();
        }
      },
      addScheduledComment() {
        if (this.commentObj && this.commentObj.id) {
          const contributorId = this.$currentContributorId;
          const helpticketId = this.ticketId;
          const url = `/tickets/${this.currentHelpTicket.id}/ticket_comments/add_scheduled_comment`;
          http
            .post(url, { scheduled_comment_id: this.commentObj.id })
            .then(() => {
              this.emitSuccess('Scheduled comment published successfully');
              this.$store.dispatch('fetchScheduledComments', { helpticketId, contributorId });
            })
            .catch((error) => {
              this.emitError('We encountered an error publishing the comment. Please try again.', error.response.data.message);
            });
        }
      },
      userName(user) {
        if (user.name) {
          return user.name;
        }
          return user.email;
      },
      closeCommentActions() {
        this.showCommentActions = false;
      },
      setTimeSpentCommentId(value) {
        this.timeSpentCommentId = value;
      },
      toggleEditComment() {
        this.showCommentEdit = !this.showCommentEdit;
        this.closeCommentActions();
      },
      hideEditor() {
        this.showCommentEdit = false;
      },
      updatePrivateContributors(updatedContributors) {
        this.commentObj.privateContributors = updatedContributors;
      },
      displayComments() {
        this.$emit('display-comments');
      },
      profileLink(user) {
        if (user.companyGroupId) {
          return `/company/groups/${user.companyGroupId}/edit`;
        }
        return `/company/users/${user.companyUserId}`;
      },
      openSplitTicketModal() {
        if (this.commentObj.contributorId) {
          this.fetchSelectedContributor({
            includes: this.commentObj.contributorId,
            company_id: this.currentHelpTicket.companyId,
          });
        }
        this.$refs.splitTicketModal.open();
      },
      selectUser(contributor) {
        this.selectedContributor = contributor;
      },
      removeUser() {
        this.selectedContributor = null;
      },
      createObject() {
        const ticketValues = [];
        this.currentHelpTicket.customForm.formFields.forEach((field) => {
          if (field.name === 'subject') {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: this.commentObj.commentText,
            });
          } else if (field.name === 'status') {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: field.defaultValue || 'Open',
            });
          } else if (field.name === 'priority') {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: field.defaultValue || 'low',
            });
          } else if (field.name === 'created_by' && this.commentObj.email && !this.selectedContributor) {
            ticketValues.push({
              customFormFieldId: field.id,
              valueStr: this.commentObj.email,
            });
          } else if (field.name === 'created_by' && (this.commentObj.contributorId || this.selectedContributor)) {
            ticketValues.push({
              customFormFieldId: field.id,
              valueInt: this.selectedContributor ? this.selectedContributor.id : this.currentContributorId,
            });
          }
        });
        return ticketValues;
      },
      splitTicket() {
        const object = {
          source: 'splitted',
          commentId: this.commentObj.id,
          values: this.createObject(),
          customFormId: this.currentHelpTicket.customFormId,
        };
        const url = `/custom_forms/${this.currentHelpTicket.customForm.id}/custom_form_tickets.json`;
        this.isSplitting = true;
        http
          .post(url, { json: JSON.stringify(object)} )
          .then(res => {
            this.emitSuccess(`HelpTicket split successfully`);
            this.$refs.splitTicketModal.close();
            this.setComments([]);
            if (!this.isQuickView) {
              this.$router.push(`/${res.data.entity.id}`);
            } else {
              this.setQuickViewTicketId(res.data.entity.id);
            }
            this.loadHelpTicket();
          })
          .catch(() => {
            this.emitError('We encountered an error splitting the ticket. Please refresh the page and try again.');
          })
          .finally(() => {
            this.isSplitting = false;
          });
      },
      loadHelpTicket() {
        this.setCurrentHelpTicket(null);
        this.setLoadingTicket(true);
        this.$store.dispatch("fetchTicket", this.ticketId);
      },
      deleteComment() {
        if (this.editableComment.id) {
          const params = { 
            ticket_id: this.currentHelpTicket.id,
            company_id: this.currentHelpTicket.company.id,
          };

          const commentLoader = document.querySelector(`#delete-comment-loader-${this.editableComment.id}`);
          const commentDeleteIcon = document.querySelector(`#delete-comment-icon-${this.editableComment.id}`);

          commentDeleteIcon.classList.add("d-none");
          commentLoader.classList.remove("d-none");

          const contributorId = this.$currentContributorId;
          const helpticketId = this.ticketId;

          http
            .delete(`/ticket_comments/${this.editableComment.id}`, {params})
            .then(() => {
              if (this.isScheduledView) {
                this.emitSuccess(`Scheduled comment deleted`);
              } else {
                this.emitSuccess(`Ticket comment deleted`);
              }
              this.$store.dispatch('fetchTimeSpents', this.ticketId);
              this.$store.dispatch('fetchComments', this.ticketId);
              this.$store.dispatch('fetchScheduledComments', { helpticketId, contributorId });
              window.onbeforeunload = null;
            })
            .catch((error) => {
              this.emitError(error.response.data.message);
            })
            .finally(() => {
              commentDeleteIcon.classList.remove("d-none");
              commentLoader.classList.add("d-none");
              this.$store.dispatch("fetchTicket", this.ticketId);
          });
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  $border-color: rgb(250, 235, 210);

  .nulodgicon-plus-round::before {
    top: 0
  }
  .private {
    outline-color: $border-color;
    outline-style: auto;
  }

  .private-copy {
    background-color: $border-color;
    border-radius: 0.25rem 0 0 0;
    bottom: 0;
    right: 0;
    padding: 0 8px;
    position: absolute;

    .private-text {
      font-weight: 700;
      letter-spacing: 0.5px;
    }
  }

  .comment-action {
    color: $gray-500;
    font-size: 1.125rem;
  }

  .highlight-comment {
    margin-bottom: 10px;
    transition: box-shadow 400ms;
    box-shadow: 0 4px 8px rgba(0,0,0,0.50);
  }

  .source-icon {
    width: 15px;
    height: 15px;
    cursor: pointer;
  }

  .remove-link {
    color: $themed-fair;
    &:hover {
      color: $danger;
    }
  }

  .comment-body {
    padding-right: 4.75rem;
  }

  .comment-actions-dropdown {
    left: 21.25rem;
    top: 2rem;
  }
</style>
