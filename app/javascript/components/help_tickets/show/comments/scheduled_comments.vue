<template>
  <div>
    <div>
      <div
        v-if="filteredComments.length > 0"
        class="loading-bar"
        :class="{'d-none': commentsVisibility}"
      >
        <span class="float-left text-secondary font-weight-normal">Loading comments</span>
        <span>
          <pulse-loader
            :loading="!commentsVisibility"
            class="ml-3 float-left"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </div>
      <div>
        <div
          v-for="(comment, index) in filteredComments"
          :key="comment.id"
        >
          <comment
            :comment="comment"
            :last-comment="index == filteredComments.length - 1"
            :comments-visibility="commentsVisibility"
            :is-quick-view="isQuickView"
            :is-scheduled-comment-view="true"
            @display-comments="displayComments"
          />
        </div>
      </div>
    </div>
    <div v-if="commentsPageCount > 1">
      <paginate
        ref="paginate"
        class="my-2 px-2 justify-content-center"
        style="right: 10px;"
        :click-handler="pageSelected"
        :container-class="'pagination pagination-sm'"
        :next-class="'next-item'"
        :next-link-class="'page-link'"
        :next-text="'Next'"
        :page-class="'page-item'"
        :page-count="commentsPageCount"
        :page-link-class="'page-link'"
        :prev-class="'prev-item'"
        :prev-link-class="'page-link'"
        :prev-text="'Prev'"
        :selected="commentsPage"
      />
    </div>
  </div>
</template>
  
<script>
  import { mapGetters, mapMutations } from 'vuex';
  import customForms from 'mixins/custom_forms';
  import _get from 'lodash/get';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from "mixins/permissions_helper";
  import suggestions from 'mixins/automated_tasks/suggestions';
  import Paginate from 'vuejs-paginate';
  import helpTickets from 'mixins/help_ticket';
  import Comment from './comment.vue';

export default {
  components: {
    Comment,
    PulseLoader,
    Paginate,
  },
  mixins: [suggestions, customForms, permissionsHelper, helpTickets],
  beforeRouteLeave(to, from, next) {
    if (this.commentSaveStatus) {
      next();
    } else if (window.confirm("Are you sure you want to leave? You have some unsaved changes")) {
      next(true);
    } else {
      next(false);
    }
  },
  props: {
    newCommentId: {
      default: null,
    },
    isQuickView: {
      type: Boolean,
      default: false,
    },
    scheduledCommentId: { 
      type: Number, 
      default: null,
    },
  },
  data() {
    return {
      commentSaveStatus: true,
      showEditor: false,
      commentsVisibility: false,
      draftComment: null,
    };
  },
  computed: {
    ...mapGetters([
      'currentHelpTicket',
      'currentHelpTicketDraft',
      'scheduledComments',
      'commentsPage',
      'commentsPageCount',
      'expandCommentSection',
    ]),

    filteredComments() {
      if (this.scheduledComments?.length > 0) {
        return this.scheduledComments.filter(comment => {
          if (comment.privateFlag) {
            const contributorId = this.$currentContributorId || '';
            const groupMemberIds = comment.privateContributors.map(item => item.groupMembersIds).flat();
            const contributorIds = groupMemberIds.concat(comment.privateContributorIds);
            return contributorIds.map(String).includes(contributorId.toString()) && comment.commentBody;
          }
          return comment.commentBody;
        });
      }
      return [];
    },
    newCommentExists() {
      return this.newCommentId && this.comments.some(com => com.id === this.newCommentId);
    },
  },
  updated() {
    if (this.scheduledCommentId && this.commentsVisibility) {
      this.$nextTick(() => {
        const el = document.querySelector(`[data-comment-id="${this.scheduledCommentId}"]`);
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'center' });
          el.classList.add('highlight-comment');
          setTimeout(() => el.classList.remove('highlight-comment'), 3000); // Remove highlight after 3s
        }
      });
    }

    if (this.newCommentExists && this.commentsVisibility) {
      this.openNewCommentModal();
    }
  },
  created() {
    if (this.enableEditing && ('newComment' in this.currentHelpTicketDraft.comments)) {
      this.toggleShowEditor();
    }
  },
  beforeDestroy() {
    this.$store.commit('setCommentsPage', 0);
    this.$store.commit('setCommentsPageCount', 0);
  },
  methods: {
    ...mapMutations(['setCommentsPage', 'setExpandCommentSection']),

    onWorkspaceChange() {
      this.$store.dispatch('fetchTimeSpents', this.ticketId);
      this.setupPusherListener();
      this.fetchComments();
    },
    async setupPusherListener() {
      const pusher = await Vue.prototype.$pusher;
      const currentCompanyGuid = Vue.prototype.$currentCompanyGuid;
      if (!pusher || !currentCompanyGuid) return;

      const channel = pusher.subscribe(currentCompanyGuid);
      channel.bind('add-scheduled-comment', data => {
        if (data.status) {
          this.fetchComments();
        }
      }, this);
    },
    fetchComments() {
      const helpticketId = this.ticketId;
      const contributorId = this.$currentContributorId;
      this.$store.dispatch('fetchScheduledComments', { helpticketId, contributorId });
    },
    displayComments() {
      this.commentsVisibility = true;
      if (this.newCommentExists) {
        this.checkNewComment();
      }
    },
    updateSaveStatus(status) {
      this.commentSaveStatus = status;
      this.toggleShowEditor();
    },
    toggleShowEditor() {
      this.showEditor = !this.showEditor;
      this.setExpandCommentSection(true);
    },
    openNewCommentModal() {
      this.$emit('open-modal');
    },
    checkNewComment() {
      this.$emit('go-to-comment');
    },
    pageSelected(p) {
      const helpTicketId = _get(this, 'currentHelpTicket.id', null);
      this.setCommentsPage(p - 1);
      this.$store.dispatch('fetchComments', helpTicketId);
    },
  },
};
</script>
<style lang="scss" scoped>
.loading-bar {
  font-size: 1.125rem;
  font-weight: bold;
  padding-top: 0.94rem;
}

.hide-loading-bar {
  display: none;
}

.comments-container {
  visibility: hidden;
}

::v-deep(.highlight-comment) {
  animation: highlight-border-fade 3s ease;
  border: 0.125rem solid #83aff1 !important;
  border-radius: 0.25rem;
  box-shadow: 0 0 0 0.125rem rgba(211, 211, 211, 0.3);
}

@keyframes highlight-border-fade {
  from {
    border-color: #83aff1;
    box-shadow: 0 0 0 0.125rem rgba(211, 211, 211, 0.3);
  }
  to {
    border-color: transparent;
    box-shadow: none;
  }
}
</style>
