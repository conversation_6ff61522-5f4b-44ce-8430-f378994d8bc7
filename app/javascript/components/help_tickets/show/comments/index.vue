<template>
  <div>
    <Teleport
      to=".quick-view-container"
      :disabled="!isQuickView"
    >
      <div
        v-if="currentHelpTicket"
        class="mb-4"
        :class="{'dismissible-container__sticky-footer': isQuickView }"
      >
        <button
          v-if="collapseCommentSection"
          class="form-control clickable mb-3 text-muted text-left btn-sm"
          :disabled="disabled"
          data-tc-add-new-comment
          @click.stop.prevent="toggleShowEditor"
        >
          Add new comment
        </button>

        <add-comment
          v-else
          usage="Comment"
          show-private-users-option
          :comment="getActiveComment"
          :is-quick-view="isQuickView"
          @update-save-status="updateSaveStatus"
          @hide-editor="hideEditor"
          @remove-draft="removeDraft"
          @render-field="$emit('render-field')"
        />
      </div>
    </Teleport>

    <div>
      <div
        v-if="filteredComments.length > 0"
        class="loading-bar"
        :class="{'d-none': commentsVisibility}"
      >
        <span class="float-left text-secondary font-weight-normal">Loading comments</span>
        <span>
          <pulse-loader
            :loading="!commentsVisibility"
            class="ml-3 float-left"
            color="#0d6efd"
            size="0.5rem"
          />
        </span>
      </div>
      <div
        v-if="scheduledComments.length"
        class="d-flex justify-content-start align-items-center p-1 rounded bg-light text-dark comments-text-banner"
      >
        <i class="ml-2 genuicon-clock-o" />
        <span>You have scheduled comments.</span>
        <a
          href="#"
          class="text-primary fw-medium no-underline-on-load"
          @click.prevent="$emit('show-scheduled-comments')"
        >
          See all scheduled comments
        </a>
      </div>
      <div>
        <div
          v-for="(comment, index) in filteredComments"
          :key="comment.id"
        >
          <comment
            :comment="comment"
            :last-comment="index == filteredComments.length - 1"
            :comments-visibility="commentsVisibility"
            :is-quick-view="isQuickView"
            @display-comments="displayComments"
          />
        </div>
      </div>
    </div>
    <div v-if="commentsPageCount > 1">
      <paginate
        ref="paginate"
        class="my-2 px-2 justify-content-center"
        style="right: 10px;"
        :click-handler="pageSelected"
        :container-class="'pagination pagination-sm'"
        :next-class="'next-item'"
        :next-link-class="'page-link'"
        :next-text="'Next'"
        :page-class="'page-item'"
        :page-count="commentsPageCount"
        :page-link-class="'page-link'"
        :prev-class="'prev-item'"
        :prev-link-class="'page-link'"
        :prev-text="'Prev'"
        :selected="commentsPage"
      />
    </div>
    <div
      v-if="isQuickView && currentHelpTicket"
      class="mt-0 ml-1"
      :class="{'mt-4': (filteredComments.length > 0)}"
    >
      <i class="genuicon-info-circled d-inline pt-0.5 mr-1 text-muted align-middle" />
      <span class="text-muted">Add a new comment at the bottom of the screen</span>
    </div>
  </div>
</template>

<script>
  import { mapGetters, mapMutations, mapActions } from 'vuex';
  import customForms from 'mixins/custom_forms';
  import _get from 'lodash/get';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import permissionsHelper from "mixins/permissions_helper";
  import suggestions from 'mixins/automated_tasks/suggestions';
  import Paginate from 'vuejs-paginate';
  import helpTickets from 'mixins/help_ticket';
  import Comment from './comment.vue';
  import AddComment from '../add_comment.vue';
  import string from '../../../../mixins/string';

  export default {
    components: {
      AddComment,
      Comment,
      PulseLoader,
      Paginate,
    },
    mixins: [suggestions, customForms, permissionsHelper, helpTickets, string],
    beforeRouteLeave(to, from, next) {
      if (this.commentSaveStatus) {
        next();
      } else if (window.confirm("Are you sure you want to leave? You have some unsaved changes")) {
          next(true);
        } else {
          next(false);
        }
    },
    props: {
      newCommentId: {
        default: null,
      },
      isQuickView: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        commentSaveStatus: true,
        showEditor: false,
        commentsVisibility: false,
        draftComment: null,
      };
    },
    computed: {
      ...mapGetters([
        'currentHelpTicket',
        'currentHelpTicketDraft',
        'comments',
        'commentsPage',
        'commentsPageCount',
        'expandCommentSection',
        'scheduledComments',
      ]),
      ...mapGetters('GlobalStore', ['isMoveTicketModalOpen', 'currentCompanyUser']),

      disabled() {
        return !this.isWrite && !this.isMyTicket && !((this.isScopedAny || this.isBasicAccess) && (this.isAssignedToMe || this.isFollower));
      },
      collapseCommentSection() {
        return !this.showEditor && !this.draftComment && !this.expandCommentSection;
      },
      isMyTicket() {
        if (!this.currentHelpTicket) {
          return false;
        }
        const values = this.getValuesForName(this.currentHelpTicket, 'created_by');
        if (values.length < 1) {
          return false;
        }
        return !!values.find((value) => value && value.valueInt && value.valueInt === this.$currentContributorId);
      },

      isAssignedToMe() {
        if (!this.currentHelpTicket) {
          return false;
        }
        const values = this.getValuesForName(this.currentHelpTicket, 'assigned_to');
        if (values.length < 1) {
          return false;
        }

        const value = values.find(val => val.valueInt === this.$currentContributorId);
        const groupMembers = this.currentHelpTicket.groupMemberIds;
        if (!value && groupMembers) {
          const parsedIds = JSON.parse(groupMembers);
          if (parsedIds.assigned_to?.includes(this.$currentContributorId)) {
            return true;
          }
        }
        return !!(value);
      },

      isFollower() {
        if (!this.currentHelpTicket) {
          return false;
        }
        else if (this.currentHelpTicket.allowFollower) {
          if (this.currentHelpTicket.followerIds.length < 1) {
            return false;
          }
          return this.currentHelpTicket.followerIds.includes(this.$currentContributorId);
        }
        return false;
      },

      filteredComments() {
        if (this.comments.length > 0) {
          return this.comments.filter(comment => {
            if (comment.privateFlag) {
              const contributorId = this.$currentContributorId || '';
              const groupMemberIds = comment.privateContributors.map(item => item.groupMembersIds).flat();
              const contributorIds = groupMemberIds.concat(comment.privateContributorIds);
              return contributorIds.map(String).includes(contributorId.toString()) && comment.commentBody;
            }
            return comment.commentBody;
          });
        }
        return [];
      },
      newComment() {
        return {
          commentBody: null,
          helpTicketId: _get(this, 'currentHelpTicket.id', null),
          privateFlag: false,
          privateContributorIds: [],
          contributorId: Vue.prototype.$currentContributorId,
          resolutionFlag: false,
        };
      },
      newCommentExists() {
       return this.newCommentId && this.comments.some(com => com.id === this.newCommentId);
      },
      getActiveComment() {
        this.fetchDraftComment();
        return this.draftComment ? this.draftComment : this.newComment;
      },
    },
    updated() {
      if (this.newCommentExists && this.commentsVisibility) {
        this.openNewCommentModal();
      }
    },
    created() {
      if (this.enableEditing && ('newComment' in this.currentHelpTicketDraft.comments)) {
        this.toggleShowEditor();
      }
    },
    beforeDestroy() {
      this.$store.commit('setCommentsPage', 0);
      this.$store.commit('setCommentsPageCount', 0);
    },
    methods: {
      ...mapMutations(['setCommentsPage', 'setExpandCommentSection']),
      ...mapActions('GlobalStore', ['fetchCurrentCompanyUser']),

      onWorkspaceChange() {
        if (this.isMoveTicketModalOpen) {
          return;
        }
        this.fetchScheduledComments();
        this.$store.dispatch('fetchTimeSpents', this.ticketId);
        this.fetchDraftComment();
        this.fetchCurrentCompanyUser({ fetch_permission: false });
        this.fetchComments();
      },
      fetchScheduledComments() {
        const helpticketId = this.ticketId;
        const contributorId = this.$currentContributorId;
        this.$store.dispatch('fetchScheduledComments', { helpticketId, contributorId });
      },
      fetchComments() {
        const id = this.ticketId;
        this.$store.dispatch('fetchComments', id);
      },
      displayComments() {
        this.commentsVisibility = true;
        if (this.newCommentExists) {
          this.checkNewComment();
        }
      },
      updateSaveStatus(status) {
        this.commentSaveStatus = status;
        this.toggleShowEditor();
      },
      toggleShowEditor() {
        this.showEditor = !this.showEditor;
        this.setExpandCommentSection(true);
      },
      hideEditor() {
        this.showEditor = false;
        this.setExpandCommentSection(false);
      },
      openNewCommentModal() {
        this.$emit('open-modal');
      },
      checkNewComment() {
        this.$emit('go-to-comment');
      },
      pageSelected(p) {
        const helpTicketId = _get(this, 'currentHelpTicket.id', null);
        this.setCommentsPage(p - 1);
        this.$store.dispatch('fetchComments', helpTicketId);
      },
      fetchDraftComment() {
        const draft = localStorage.getItem(`user ${this.$currentCompanyUserId}- ticket ${this.currentHelpTicket?.id}`);
        if (draft) {
          this.draftComment = JSON.parse(this.decompressData(draft));
        }
      },
      removeDraft() {
        this.showEditor = true;
        this.draftComment = null;
      },
    },
  };
</script>
<style lang="scss" scoped>

.comments-text-banner {
  gap: 10px; 
  border: 1px solid #ccc;
}

.no-underline-on-load {
  text-decoration: none;
  cursor: pointer;
}

.no-underline-on-load:hover {
  text-decoration: underline;
}

.loading-bar {
  font-size: 18px;
  font-weight: bold;
  padding-top: 15px;
}

.hide-loading-bar {
  display: none;
}

.comments-container {
  visibility: hidden;
}
</style>
