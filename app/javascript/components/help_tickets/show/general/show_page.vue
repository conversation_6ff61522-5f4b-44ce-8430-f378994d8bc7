<template>
  <div>
    <component
      :is="selectedComponent"
      v-if="isLoaded"
    />
  </div>
</template>

<script>
import { mapMutations, mapGetters } from 'vuex';
import ModernHelpTicketShow from './modern_show.vue';
import HelpTicketShow from './show.vue';
import modernViewsHelper from '../../../../mixins/modern_views_helper';

export default {
  components: {
    ModernHelpTicketShow,
    HelpTicketShow,
  },
  mixins: [modernViewsHelper],
  data() {
    return {
      isModernHelpTicket: true,
      isLoaded: false,
    };
  },
  computed: {
    ...mapGetters(['isModernView']),
    selectedComponent() {
      return this.isModernHelpTicket ? 'ModernHelpTicketShow' : 'HelpTicketShow';
    },
  },
  watch: {
    isModernView(newValue) {
      this.isModernHelpTicket = newValue;
      this.setHtModernView(newValue);
    },
  },
  async created() {
    const htStoredValue = localStorage.getItem('is-modern-help-ticket');
    if (htStoredValue === null && this.shouldApplyModernFeatures()) {
      localStorage.setItem('is-modern-help-ticket', String(this.isModernHelpTicket));
    } else {
      this.isModernHelpTicket = htStoredValue === 'true';
    }
    this.setHtModernView(this.isModernHelpTicket);
    this.isLoaded = true;
  },
  methods: {
    ...mapMutations(['setHtModernView']),
  },
};
</script>

<style scoped>
.loading {
  text-align: center;
  padding: 1.25rem;
}
</style>
