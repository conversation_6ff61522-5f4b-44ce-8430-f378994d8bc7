<template>
  <Teleport to="body">
    <sweet-modal
      ref="modal"
      title="Ticket Summary"
      modal-theme="dark-header theme-right theme-sticky-footer"
      width="50%"
      @close="$emit('close')"
    >
      <div class="position-relative">
        <div 
          v-if="summary"
          class="position-absolute mt-n5 right-0"
        >
          <div class="d-flex justify-content-end">
            <span
              v-tooltip="tooltipText"
              class="mx-1 pt-1 mt-1 btn btn-link btn-flat btn-icon-circle btn-icon-circle-sm text-secondary has-tooltip"
              @click.stop="copyResource"
            >
              <i class="nulodgicon-link h5"/>
            </span>
            <div
              v-if="showRegenerateButton" 
              class="btn text-secondary"
              @click="requestSummary(true)"
            >
              <img
                v-tooltip="'Generate Ticket Summary via AI'"
                src="https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/ai_images/ai-tooling.svg"
                :class="{
                  'light-icon-filter': isDarkMode,
                }"
                :width="isQuickView ? 16 : 20"
              >
              Regenerate Summary
            </div>
          </div>
          <p class="text-center">Last Generated: {{ summaryUpdateTime }}</p>
        </div>
        <div
          v-if="summary"
          class="mt-4 prose prose-sm max-w-none"
          v-html="renderedSummary"
        />
        <div
          v-else-if="!summary && isLoading"
          class="d-flex h6 text-muted mt-2"
        >
          {{ loaderText }}
          <pulse-loader
            class="ml-3"
            color="#0d6efd"
            size="0.5rem"
            loading
          />
        </div>
      </div>
    </sweet-modal>
  </Teleport>
</template>

<script>
  import { marked } from "marked";
  import http from 'common/http';
  import { SweetModal } from 'sweet-modal-vue';
  import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
  import consumer from "common/consumer";
  import permissionsHelper from 'mixins/permissions_helper';
  import helpTickets from 'mixins/help_ticket';
  import dates from 'mixins/dates';

  export default {
    components: {
      SweetModal,
      PulseLoader,
    },
    mixins: [ permissionsHelper, helpTickets, dates ],
    props: {
      ticketId: {
        type: Number,
        default: 0,
        require: true,
      },
      isQuickView: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        summary: "",
        isLoading: false,
        renderedSummary: "",
        ticketSummarySubscription: null,
        showRegenerateButton: false,
        summaryUpdateTime: '',
        copiedResourceId: null,
        tooltipText: 'Copy Link',
        loaderText: 'Loading',
      };
    },
    computed: {
      generateSummaryText() {
        return this.summary ? 'Regenerate Summary' : 'Generate Summary';
      },
    },
    mounted() {
      const params = new URLSearchParams(window.location.search);
      if (params.get('summary') === 'true') {
        this.open();
        this.requestSummary();
      }
    },
    methods: {
      open() {
        this.$refs.modal.open();
      },
      close() {
        this.$refs.modal.close();
      },
      requestSummary(triggerAiSummary = false) {
        this.subscribeSummaryChannel();
        this.summary = "";
        this.isLoading = true;
        if (triggerAiSummary) {
          this.generateAiSummary();
        } else {
          this.loaderText = 'Loading';
          http
            .get(`/help_tickets/fetch_ticket_summary/${this.ticketId}.json`)
            .then((response) => {
              const {summary, updatedAt, showRegenerateButton} = response.data;
              if (summary) {
                this.summary = summary;
                this.renderedSummary = marked(summary);
                this.isLoading = false;
                this.showRegenerateButton = showRegenerateButton;
                this.summaryUpdateTime = this.showDateTime(updatedAt);
              } else {
                this.generateAiSummary();
                this.showRegenerateButton = false;
              }
            })
            .catch((error) => {
              this.isLoading = false;
              this.emitError(`Sorry, there was an error while fetching ticket summary. ${error.response.data.message}`);
            });
        }
      },
      generateAiSummary() {
        this.loaderText = 'Generating Summary';
        const params = { ticket_id: this.ticketId };
        http
          .get(`/help_tickets/ticket_summaries.json`, { params })
          .then((response) => {
            this.showRegenerateButton = false;
            this.summaryUpdateTime = this.showDateTime(response.data.timeStamp);
          })
          .catch((e) => {
            this.isLoading = false;
            this.emitError(`Sorry, there was an error while generating ticket summary. ${e.response.data.message}`);
          });
      },
      subscribeSummaryChannel() {
        const vm = this;
        if (!this.ticketSummarySubscription) {
          this.ticketSummarySubscription = consumer.subscriptions.create(
            { channel: "TicketSummaryChannel", ticket_id: vm.ticketId, company_user_id: vm.$currentCompanyUserId },
            {
              received(data) {
                if (data.status === 'done') {
                  vm.isLoading = false;
                } else if (data.status === 'error') {
                  vm.isLoading = false;
                  vm.emitError('Sorry, there was an error while generating ticket summary. Please try again a few moments later.');
                }

                if (data.message) {
                  vm.summary = data.message;
                  vm.renderedSummary = marked(vm.summary);
                }
              },
              disconnected: () => {
                vm.ticketSummarySubscription = null;
              },
            }
          );
        }
      },
      copyResource() {
        const url = new URL(window.location.href);
        url.searchParams.set('summary', 'true');
        const resourceData = url.toString();

        navigator.clipboard.writeText(resourceData)
          .then(() => {
            this.tooltipText = 'Copied!';
            setTimeout(() => {
              this.tooltipText = 'Copy Link';
            }, 2000);
          });
      },
    },
  };
</script>

<style lang="scss">
  .box--with-heading {
    &:hover {
      .header__arrow-wrap {
        background-color: var(--themed-light-hover-bg) !important;
        color: $themed-secondary !important;
      }
    }
  }

  .prose h3 {
    font-size: 1.4rem;
  }
  .prose strong {
    font-weight: bold;
  }

  .prose ul {
    list-style: disc !important;
    padding-left: 1.5rem !important;
  }

  .prose li {
    margin-bottom: 0.25rem;
  }

  .prose ul li::marker {
    font-size: 1.4rem;
  }
</style>
