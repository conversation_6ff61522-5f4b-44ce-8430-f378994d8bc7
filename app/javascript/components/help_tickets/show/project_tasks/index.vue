<template>
  <div>
    <div 
      v-if="isWritableFromCustomFormObject(currentHelpTicket)"
      class="text-right"
    >
      <a
        v-if="!showNewTaskForm && !showEditTaskForm && !showNewChecklistForm"
        class="btn-group ml-2 position-relative"
        @click.prevent.stop="handleAddClick(false)"
      >
        <button
          class="btn btn-sm btn-primary"
          data-tc-add-task
        >
          + Add Task
        </button>
        <button
          id="dropdown_button"
          v-click-outside="closeDropdown"
          class="btn btn-sm btn-primary-dark pl-1 rounded-right"
          type="button"
          @click.prevent.stop="handleAddClick(true)"
        >
          <i class="dropdown-toggle ml-2 arrow-down d-block" />
        </button>
        <div
          v-if="showAddOptions"
          class="dropdown-menu right-0 show"
          :class="{ 
            'task-dropdown-position--left': isQuickView,
            'task-dropdown-menu': !isQuickView && isModernView,
          }"
        >
          <span>
            <div
              v-for="option in addOptions"
              :key="option.id"
              class="dropdown-item small cursor-pointer"
              @click.stop.prevent="toggleForm(option.value)"
            >
              {{ option.name }}
            </div>
            <hr class="horizontal-line">
            <div class="focus-item">
              <div
                class="not-as-small mt-2 ml-2 cursor-pointer"
                @click.stop.prevent="toggleForm('newChecklist')"
              >
                &plus; Create new Checklist
              </div>
            </div>
          </span>
        </div>
      </a>
    </div>

    <div v-if="showNewChecklistModal">
      <new-checklist-modal
        ref="newChecklistModal"
        :render-checklist-form="true"
        @input="newChecklist"
      />
    </div>

    <div v-if="showNewTaskForm">
      <new-task
        :add-new-user="true"
        :is-quick-view="isQuickView"
        @toggle-new-task-form="toggleNewTaskForm"
        @load-tasks="loadTasks"
      />
    </div>

    <div
      v-if="showNewChecklistForm"
      class="mb-2"
    >
      <checklist-select
        ref="checklistSelect"
        :ticket-show-page="true"
        :ticket-checklists="ticketChecklists"
        @close-modal="closeNewChecklistModal"
        @checklists-updated="fetchTasksAndChecklists"
        @toggle-new-checklist-form="toggleNewChecklistForm"
      />
    </div>

    <div v-if="showEditTaskForm">
      <edit-task
        :add-new-user="true"
        :task="taskBeingEdited"
        :is-quick-view="isQuickView"
        @toggle-edit-task-form="toggleEditTaskForm"
        @load-tasks="loadTasks"
      />
    </div>

    <div v-else>
      <p
        v-if="tasksTotalCount >= 2"
        class="clearfix font-weight-normal"
      >
        <span
          v-if="selectedTasks.length == 0"
          class="small text-secondary"
          data-tc-press-selecting-merging
        >
          Press <kbd class="bg-light rounded">{{ selectionCommand }}</kbd> for selecting and merging tasks.
        </span>
        <span
          v-else
          class="mt-1"
        >
          <button
            v-if="selectedTasks.length != tasksTotalCount"
            class="btn btn-xs btn-outline-primary"
            data-tc-btn="add to parent task"
            @click="selectParentTask"
          >
            Add to parent task
          </button>

          <button
            v-if="selectedTasks.length < tasks.length"
            class="btn btn-xs btn-outline-primary"
            data-tc-btn="select all tasks"
            @click="selectAllTasks"
          >
            Select all
          </button>

          <button
            v-if="selectedTasks.length > 0"
            class="btn btn-xs btn-warning text-white"
            data-tc-btn="Unselect all"
            @click="unSelectTasks"
          >
            Unselect all
          </button>
        </span>
      </p>
      <div>
        <div
          v-if="taskList.length > 0"
          class="mt-3 border bg-themed-box-bg p-1 rounded"
        >
          <draggable
            v-model="taskList"
            handle=".handle"
            v-bind="dragOptions"
            :disabled="sortingDisabled"
            animation="150"
            dragover-bubble="true"
            empty-insert-threshold="15"
            @start="drag = true"
            @end="drag = false"
          >
            <transition-group
              type="transition"
              :name="!drag ? 'flip-list' : null"
            >
              <template v-for="(task, index) in taskList">
                <div :key="task.id">
                  <line-item
                    :key="task.id"
                    :value="task"
                    :selected-task-ids-arr="selectedTasks"
                    :object="currentHelpTicket"
                    :is-quick-view="isQuickView"
                    class="py-3 bg-themed-box-bg w-100 m-0"
                    :class="{'border-bottom': !hasSubTasks(task), 'border-bottom': !(index == taskList.length - 1)}"
                    @select-task="selectTask"
                    @toggle-edit-task="toggleEditTaskForm"
                    @delete-task="deleteTask"
                  />
                  <template v-if="hasSubTasks(task)">
                    <line-item
                      v-for="subTask in task.subTasks"
                      :key="subTask.id"
                      :value="subTask"
                      :selected-task-ids-arr="selectedTasks"
                      :is-quick-view="isQuickView"
                      class="sub-task py-3 ml-3 bg-lighter"
                      @remove-sub-task="removeSubTask"
                      @toggle-edit-task="toggleEditTaskForm"
                      @delete-task="deleteTask"
                    />
                  </template>
                </div>
              </template>
            </transition-group>
          </draggable>
        </div>

        <div v-if="tasks && pageCount > 1">
          <div class="float-right">
            <paginate
              :click-handler="pageSelected"
              :container-class="'pagination'"
              :next-class="'next-item'"
              :next-link-class="'page-link'"
              :next-text="'Next'"
              :page-class="'page-item'"
              :page-count="pageCount"
              :page-link-class="'page-link'"
              :prev-class="'prev-item'"
              :prev-link-class="'page-link'"
              :prev-text="'Prev'"
              :selected="pageIndex"
            />
          </div>
        </div>
      </div>
    </div>

    <select-parent-task
      ref="selectParentTask"
      :tasks="allTasks"
      @merge-tasks="mergeTasks"
    />
  </div>
</template>

<script>
  import _ from 'lodash';
  import http from 'common/http';
  import Paginate from 'vuejs-paginate';
  import { mapMutations, mapGetters } from 'vuex';
  import search from 'mixins/search';
  import vClickOutside from 'v-click-outside';
  import permissions from "mixins/custom_forms/permissions";
  import ChecklistSelect from 'components/checklist_select.vue';
  import NewChecklistModal from 'components/new_checklist_modal.vue';
  import helpTickets from 'mixins/help_ticket';
  import SelectParentTask from './select_parent_task.vue';
  import LineItem from './line_item.vue';
  import NewTask from './new.vue';
  import EditTask from './edit.vue';

  export default {
    directives: {
      clickOutside: vClickOutside.directive,
    },
    components: {
      LineItem,
      Paginate,
      SelectParentTask,
      NewTask,
      EditTask,
      ChecklistSelect,
      NewChecklistModal,
    },
    mixins: [search, permissions, helpTickets],
    props: {
      isQuickView: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        viewType: null,
        selectedTasks: [],
        drag: false,
        showNewTaskForm: false,
        showEditTaskForm: false,
        showNewChecklistForm: false,
        taskBeingEdited: null,
        sortingDisabled: false,
        allTasks: [],
        showAddOptions: false,
        ticketChecklists: [],
        totalChecklists: [],
        showNewChecklistModal: false,
        addOptions: [
          { id: 1, name: 'Add Task', value: 'task' },
          { id: 2, name: 'Add Checklist', value: 'checklist' },
        ],
      };
    },
    computed: {
      ...mapGetters([
        'currentHelpTicket',
        'tasks',
        'tasksTotalCount',
        'page',
        'perPage',
        'isModernView',
        'currentHelpTicketDraft',
      ]),
      taskList: {
        get() {
          return _.sortBy(this.tasks, 'position');
        },
        set(value) {
          const val = value;
          this.sortingDisabled = true;
          const len = val.length;
          for (let idx = 0; idx < len; idx += 1) {
            const newVal = [...val];
            newVal[idx].position = idx;
            if (idx + 1 === len) {
              this.updatePosition(newVal);
            }
          }
        },
      },
      selectionCommand() {
        if (navigator.appVersion.indexOf("Mac") !== -1)  {
          return "cmd + click";
        }
        return "ctrl + click";
      },
      pageCount() {
        return Math.ceil(this.tasksTotalCount / this.perPage);
      },
      pageIndex() {
        return this.page;
      },
      filteredCompanyUsers() {
        return this.companyUsers;
      },
      parentTaskOptions() {
        if (this.selectedTasks.length > 0) {
          return this.tasks.filter((task) => this.selectedTasks.find(x => x === task.id) === null);
        }
        return [];
      },
      dragOptions() {
        return {
          animation: 0,
          disabled: false,
          ghostClass: "ghost",
        };
      },
    },
    mounted() {
      if (this.enableEditing && this.currentHelpTicketDraft?.tasksData.newDesc) {
        this.handleAddClick(false);
      }
    },
    methods: {
      ...mapMutations(['setPage']),

      onWorkspaceChange() {
        this.fetchTasksAndChecklists();
      },
      fetchTasksAndChecklists() {
        this.fetchTicketChecklists();
        this.fetchProjectTasks();
      },
      fetchProjectTasks() {
        this.$store.dispatch("fetchTasks", this.ticketId).catch(err => {
          this.emitError(`Sorry, there was an error loading tasks. ${err.response.data.message}`);
        });
      },
      toggleEditTaskForm(task=null) {
        this.taskBeingEdited = task;
        this.showEditTaskForm = !this.showEditTaskForm;
      },
      closeDropdown() {
        this.showAddOptions = false;
      },
      handleAddClick(dropdown = false) {
        if (!dropdown) {
          this.showNewTaskForm = true;
        } else {
          this.toggleDropdown();
        }
      },
      toggleDropdown() {
        this.showAddOptions = !this.showAddOptions;
      },
      toggleForm(option) {
        this.showAddOptions = false;
        if (option === 'task') {
          this.showNewTaskForm = true;
        } else if (option === 'checklist') {
          this.showNewChecklistForm = true;
        } else if (option === 'newChecklist') {
          this.showNewChecklistModal = true;
          this.$nextTick(() => {
            this.$refs.newChecklistModal?.open();
          });
        }
      },
      fetchTicketChecklists() {
        http
          .get('/task_checklists.json', { params: { ticket_id: this.currentHelpTicket.id, company_id: this.currentHelpTicket.company.id } })
          .then(res => {
            this.ticketChecklists = res.data.ticketChecklists;
            this.totalChecklists = res.data.taskChecklists;
          })
          .catch(() => {
            this.emitError('Sorry, there was an error fetching ticket checklists.');
          });
      },
      deleteTask(id) {
        const params = {
          company_id: this.currentHelpTicket.company.id,
          workspace_id: this.currentHelpTicket.workspaceId,
        };
        http
          .delete(`/tickets/${this.currentHelpTicket.id}/project_tasks/${id}.json`, { params })
          .then(() => {
            this.emitSuccess('Task removed successfully');
            this.loadTasks();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error removing a task. Please try again later.`);
          });
        this.$refs.selectParentTask.close();
      },
      toggleNewTaskForm() {
        this.showNewTaskForm = !this.showNewTaskForm;
      },
      toggleNewChecklistForm() {
        this.showNewChecklistForm = !this.showNewChecklistForm;
      },
      hasSubTasks(task) {
        return task.subTasks && task.subTasks.length > 0;
      },
      removeSubTask(id) {
        http
          .delete(`/tickets/${this.currentHelpTicket.id}/parent_tasks/${id}.json`)
          .then(() => {
            this.emitSuccess('Subtask removed successfully');
            this.loadTasks();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error removing a subtask. Please try again later.`);
          });
      },
      mergeTasks(parentTaskId) {
        const params = {parent_task_id: parentTaskId, sub_task_array: this.selectedTasks};
        http
          .post(`/tickets/${this.currentHelpTicket.id}/parent_tasks.json`, params )
          .then(() => {
            this.emitSuccess('Task merged updated successfully');
            this.selectedTasks = [];
            this.$refs.selectParentTask.close();
            this.loadTasks();
          })
          .catch(() => {
            this.emitError(`Sorry, there was an error updating the task.  Please try again later.`);
          });
      },
      selectTask(taskId) {
        const idx = this.selectedTasks.indexOf(taskId);
        if (idx > -1) {
          this.selectedTasks.splice(idx, 1);
        } else {
          this.selectedTasks.push(taskId);
        }
      },
      selectAllTasks() {
        this.selectedTasks = this.tasks.map(a => a.id);
      },
      unSelectTasks() {
        this.selectedTasks = [];
      },
      selectParentTask() {
        this.fetchAllTasks();
        if (this.parentTaskOptions.length > 0) {
          this.$refs.selectParentTask.parentTask = this.parentTaskOptions[0].id;
        }
        this.$refs.selectParentTask.open();
      },
      updatePosition(val) {
        http
          .post(`/project_task_positions/${this.ticketId}.json`, {tasks: val})
          .then(() => {
            this.loadTasks();
            this.sortingDisabled = false;
            this.emitSuccess('Successfully updated tasks');
          })
          .catch((error) => {
            this.sortingDisabled = false;
            this.emitError(error.response.data.message);
          });
      },
      pageSelected(p) {
        this.setPage(p - 1);
        this.loadTasks();
      },
      loadTasks() {
        this.$store.dispatch("fetchTasks", this.ticketId).catch(err => {
          this.emitError(`Sorry, there was an error loading tasks. ${err.response.data.message}`);
        });
      },
      fetchAllTasks() {
        const params = {
          skip_pagination: true,
          selected_tasks: this.selectedTasks,
        };

        return http
          .get(`/tickets/${this.ticketId}/project_tasks.json`, { params })
          .then((res) => {
            this.allTasks = res.data.tasks;
          });
      },
      newChecklist(checklist) {
        this.showNewChecklistForm = true;
        this.$nextTick(() => {
          this.$refs.checklistSelect?.saveNewChecklist(checklist);
        });
      },
      closeNewChecklistModal() {
        this.showNewChecklistModal = false;
        this.$refs.newChecklistModal?.close();
      },
    },
  };
</script>

<style scoped lang="scss">
.flip-list-move {
  transition: transform 0.5s;
}
.no-move {
  transition: transform 0s;
}
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
.horizontal-line {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.focus-item {
  &:hover {
    background-color: $themed-light;
    color: $themed-base;
  }
}
.task-dropdown-position--left {
  left: -6.25rem;
}
.task-dropdown-menu {
  left: -3rem !important;
}
</style>
