<template>
  <form>
    <div
      class="px-4 mt-2 pr-sm-0 pr-md-4 mb-2 mb-lg-0"
      :class="{'box': !popUpModal}"
    >
      <div class="box__inner row">
        <div class="col-12">
          <div
            class="form-group"
            data-tc-new-task-form
          >
            <label for="description">Description</label>
            <input
              id="description"
              v-model="task.description"
              v-validate="'required'"
              type="text"
              class="form-control"
              name="description"
              data-tc-task-description-input
              @input="taskDescriptionUpdate"
            >
            <span
              v-if="errors.has('description')"
              class="form-text small text-danger"
            >{{ errors.first('description') }}</span>
          </div>


          <div class="form-group">
            <label for="priority">Priority</label>
            <priority-select
              id="priority"
              :value="task.priority"
              data-tc-select-task-priority
              @input="priorityChanged"
            />
          </div>
        </div>


        <div
          v-if="!popUpModal"
          class="col-12"
        >
          <div class="row">
            <div class="form-group col-sm-12">
              <label for="due-at">Due Date</label>
              <span 
                class="date-popup"
                :class="{ 'dropdown-menu-top': isModernView }"
              >
                <nice-datepicker
                  id="due-at"
                  v-model="task.dueAt"
                  name="due-at"
                  header-text="Due Date:"
                  class="datepicker"
                  is-clearable
                  data-tc-task-due-at
                />
              </span>
            </div>

            <div class="form-group col-sm-12">
              <label for="completed-at">Completed Date</label>
              <span class="date-popup">
                <nice-datepicker
                  id="completed-at"
                  v-model="task.completedAt"
                  name="completed_at"
                  header-text="Completed Date"
                  class="datepicker"
                  is-clearable
                  data-tc-task-completed-at
                />
              </span>
              <span
                v-show="isCompletionFutureDate"
                class="form-text text-danger small"
                data-tc-error="completion date"
              >
                {{ completionDateErrorMessage }}
              </span>
            </div>
          </div>
        </div>

        <div
          class="form-group col-sm-12"
          :class="{ 'col-md-6': !popUpModal && !isQuickView }"
        >
          <label for="assignee">Assignee</label>
          <mass-assign
            id="assignee"
            v-model="assignees"
            :add-new-user="addNewUser"
            @add="addAssignee"
            @delete="clearMember"
          />
        </div>

        <div class="form-group pt-sm-0 pt-md-3 col-10">
          <submit-button
            :is-validated="!isCompletionFutureDate"
            :disabled="isCompletionFutureDate"
            :is-saving="isSaving"
            :btn-classes="'px-4'"
            btn-content="Save task"
            saving-content="Saving task"
            @submit="saveTask"
          />
          <button
            v-if="showCloseButton"
            type="button"
            class="btn btn-secondary ml-2"
            @click="$emit('close-task-modal')"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </form>
</template>

<script>
import http from 'common/http';
import _cloneDeep from 'lodash/cloneDeep';
import _debounce from "lodash/debounce";
import { mapGetters, mapActions } from 'vuex';
import NiceDatepicker from "components/shared/nice_datepicker.vue";
import PrioritySelect from 'components/shared/priority_select.vue';
import MassAssign from "components/shared/company_user_assign/mass_assign.vue";
import SubmitButton from 'components/shared/submit_button.vue';

export default {
  $_veeValidate: {
    validator: "new",
  },
  components: {
    MassAssign,
    NiceDatepicker,
    PrioritySelect,
    SubmitButton,
  },
  props: ['value', 'addNewUser', 'isSaving', 'popUpModal', 'isQuickView', 'showCloseButton'],
  data() {
    return {
      task: { ...this.value },
      originalTask: { ...this.value },
      completionDateErrorMessage: "Completion date cannot be in the future.",
      isDraft: false,
      isDraftResolved: false,
    };
  },
  computed: {
    ...mapGetters([
      'isModernView',
      'currentHelpTicket',
      'currentHelpTicketDraft',
    ]),
    assignees() {
      return this.task.assignees;
    },
    isCompletionFutureDate() {
      if (this.task.completedAt && moment(this.task.completedAt) > moment()) {
        return true;
      }
      return false;
    },
    draftExists() {
      return (
        this.currentHelpTicketDraft &&
        this.currentHelpTicketDraft.companyUserId === this.$currentCompanyUserId
      );
    },
    draftValue() {
      const draftData = this.currentHelpTicketDraft?.tasksData || {};
      if (this.task.id) {
        return draftData[this.task.id] || null;
      }
      return draftData.newDesc || null;
    },
  },
  watch: {
    value(newVal) {
      this.task = newVal;
    },
  },
  created() {
    this.isDraftResolved = false;
    if (this.draftExists && this.draftValue) {
      if (this.currentHelpTicketDraft.tasksData?.[this.task.id]) {
        this.task.description = _cloneDeep(this.draftValue);
      } else if (!this.task.id && ('newDesc' in this.currentHelpTicketDraft.tasksData)) {
        this.task.description = _cloneDeep(this.draftValue);
      }
      this.isDraft = true;
    }
  },
  methods: {
    ...mapActions([
      'fetchTicketDraft',
    ]),
    taskDescriptionUpdate: _debounce(
      function () {
        if (this.task.description && this.currentHelpTicketDraft) {
          const tasksData = { ...this.currentHelpTicketDraft?.tasksData};
          if (this.task.id) {
            tasksData[this.task.id] = this.task.description;
          } else {
            tasksData.newDesc = this.task.description;
          }
          this.updateDraftTaskValue(tasksData);
        }
      },
      1000
    ),
    handleDraftResolution(actionType) {
      if (actionType === 'discard') {
        this.task = this.originalTask;
        const updatedTasks = { ...this.currentHelpTicketDraft.tasksData };
        if (actionType === 'discard') {
          delete updatedTasks[this.task.id || 'newDesc'];
        }
        this.isDraftResolved = true;
        this.updateDraftTaskValue(updatedTasks);
      }
    },
    updateDraftTaskValue(newTaskValue) {
      const updatedTaskData = {
        tasksData: newTaskValue,
        id: this.currentHelpTicketDraft.id,
        ...(this.currentHelpTicketDraft.helpTicketId || { helpTicketId: this.currentHelpTicket.id }),
      };
      this.$store.commit('updateCurrentHelpTicketDraft', updatedTaskData);
    },
    clearMember(id) {
      const index =  this.task.assignees.findIndex(assignee => assignee.id === id);
      this.task.assignees.splice(index, 1);
    },
    addAssignee(id) {
      this.task.assignees.push({ id });
    },
    changeMember(index, member) {
      this.task.assignees.splice(index, 1, member);
    },
    removeMember(index) {
      this.task.assignees.splice(index, 1);
    },
    saveTask() {
      this.task.contributor_id = this.$currentContributorId;

      this.$validator.validateAll().then((result) => {
        if (result) {
          this.$emit("input", this.task);
          if (this.isDraft && !this.isDraftResolved) {
            this.handleDraftResolution('discard');
          }
        } else {
          this.emitError(`Please correct the highlighted errors before submitting.`);
        }
      });
    },
    loadOptions() {
      http
        .get('/contributor_options.json')
        .then((res) => {
          this.options = res.data;
        })
        .catch((error) => {
          this.emitError(`Sorry, there was an error loading available people ${error.message}`);
        });
    },
    priorityChanged(val) {
      this.task.priority = val;
    },
  },
};
</script>

<style lang="scss" scoped>
.users-select {
  max-width: 18.75rem;
}
.multiselect__content-wrapper {
  background: $themed-box-bg;
  z-index: 200;
}
.date-popup {
  :deep(.mx-datepicker) {
    .mx-datepicker-popup {
      left: -2.125rem !important;
    }
  }
}
.dropdown-menu-top {
  :deep(.mx-datepicker) {
    .mx-datepicker-popup {
      top: 0.5rem !important;
    }
  }
}
</style>
