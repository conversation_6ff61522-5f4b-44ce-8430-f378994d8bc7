<template>
  <div>
    <div
      v-if="ticket.archived"
      class="badge box-badge--archived"
      data-tc-archived-badge
    >
      Archived
    </div>
    <div
      v-else-if="isAgent && ticket.isNew"
      v-tooltip="'Has no agent assigned, status changed, comment added, or time spent added'"
      class="badge box-badge--new"
    >
      New
    </div>
    <!-- TODO: This is never going to be hit, as there is no `isBold` prop. Let's remove it or fix it. -->
    <div
      v-else-if="isBold"
      v-tooltip="'Has not been viewed by any assigned agents'"
      class="badge box-badge--unread"
    >
      Unread
    </div>
    <div
      v-else-if="overdue"
      class="badge box-badge--overdue"
    >
      Overdue
    </div>
    <div
      v-else-if="almostOverdue"
      class="badge box-badge--almost-overdue"
    >
      Almost Due
    </div>
    <div
      v-else-if="draftExists"
      v-tooltip="'Has unsaved changes'"
      class="badge box-badge--ticket-draft"
    >
      Draft
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex';
  import ticketHelper  from 'mixins/ticket_helper';


  export default {
    mixins: [ ticketHelper ],
    props: {
      ticket: {
        type: Object,
        default: () => {},
      },
    },
    mounted() {
      this.$store.dispatch("fetchAllDrafts");
    },
    computed: {
      ...mapGetters('GlobalStore', ['currentCompanyUser']), // currentCompanyUser needed for helper mixin methods
      ...mapGetters(['allTicketDrafts']),
      overdue() {
        if (!this.ticket.dueAt) {
          return false;
        }
        const dueAtUtc = moment.utc(this.ticket.dueAt).toDate();
        const dueAt = moment(moment(dueAtUtc).local().format('YYYY-MM-DD'));
        const today =  moment(moment().format('YYYY-MM-DD'));
        return dueAt.diff(today) < 0;
      },
      almostOverdue() {
        if (!this.ticket.dueAt) {
          return false;
        }
        const dueAtUtc = moment.utc(this.ticket.dueAt).toDate();
        const dueAt = moment(moment(dueAtUtc).local().format('YYYY-MM-DD'));
        const today =  moment(moment().format('YYYY-MM-DD'));
        return dueAt.diff(today) < *********;
      },
      draftExists() {
        return (
          this.allTicketDrafts.some(
            (draft) =>
              draft.company_user_id === this.$currentCompanyUserId &&
              draft.help_ticket_id === this.ticket.id
          )
        );
      },
    },
  };
</script>

<style scoped lang="scss">
  .badge {
    font-size: 0.625rem;
    z-index: 2;
  }

  .badge--top-left {
    .badge {
      left: -0.25rem;
      top: -0.125rem;
      width: 4rem;
    }

    .box-badge--new {
      width: 2rem;
    }
  }

  .badge--left-0 {
    .badge {
      left: 0;
      right: auto;
    }
  }

  .badge--top-0 {
    .badge {
      top: 0;
    }
  }
</style>
