<template>
  <div>
    <resources-menu active="response" />
    <div>
      <h5 class="mt-sm-2 mt-md-4 font-weight-normal h5--responsive">
        Canned Responses
      </h5>
      <span class="p--responsive text-muted">
        Responses are reusable text blocks &mdash; type <kbd class="bg-light rounded">#</kbd> in ticket descriptions and comments to add them quickly.
      </span>
      <div class="row mt-3 mb-3">
        <div class="col-12">
          <input
            ref="searchInput"
            v-model="responseSearch"
            type="text"
            class="form-control search-input readable-length"
            placeholder="Search your Responses..."
            name="responseSearch"
            data-tc-search-field="canned response"
            @keyup="updateSearch"
          >
          <i
            class="mt-2 nulodgicon-ios-search-strong search-input-icon"
            @click.prevent="$refs.searchInput.focus"
          />
        </div>
      </div>
      <div
        v-if="isLoading"
        class="pt-5 d-flex"
      >
        <span class="h5 text-muted">Loading Responses...</span>
        <pulse-loader
          :loading="true"
          class="ml-3 mt-1"
          color="#0d6efd"
          size="0.5rem"
        />
      </div>
      <div v-else>
        <div
          v-if="responses.length > 0"
          class="mt-4 row"
        >
          <div
            v-for="response in responses"
            :key="response.id"
            class="mb-4 col-sm-12 col-md-6 col-lg-4 col-xxl-wide-theme-3 response-item"
          >
            <div class="h-100">
              <router-link
                :to="`/responses/${response.id}`"
                class="box box--with-hover"
                data-tc-response-box
              >
                <div class="box__inner">
                  <div class="pa-2">
                    <h6
                      class="response-title h5--responsive"
                      data-test-response-title
                    >
                      {{ response.title }}
                    </h6>
                    <!-- eslint-disable vue/no-v-html -->
                    <p
                      class="mt-1 text-muted small response-description p--responsive"
                      v-html="sanitizeHTML(response.description)"
                    />
                  </div>
                </div>
              </router-link>
            </div>
          </div>
        </div>
        <div
          v-else
          class="text-center mt-5 pt-4"
        >
          <h4 data-tc-no-response-found>No Responses exist, yet.</h4>
          <h5
            v-if="!isRead"
            class="text-secondary font-weight-normal"
          >
            Be more productive and
            <router-link to="/responses/new">
              add a Response
            </router-link>.
          </h5>
        </div>
      </div>
      <nav v-if="pageCount > 1">
        <paginate
          ref="paginate"
          class="my-3 px-2 justify-content-center"
          :click-handler="pageSelected"
          :container-class="'pagination pagination-sm'"
          :next-class="'next-item'"
          :next-link-class="'page-link'"
          :next-text="'Next'"
          :page-class="'page-item'"
          :page-count="pageCount"
          :page-link-class="'page-link'"
          :prev-class="'prev-item'"
          :prev-link-class="'page-link'"
          :prev-text="'Prev'"
          :selected="indexPage"
        />
      </nav>
    </div>
  </div>
</template>
<script>
import http from 'common/http';
import Paginate from 'vuejs-paginate';
import search from 'mixins/search';
import _debounce from 'lodash/debounce';
import { mapMutations } from 'vuex';
import permissionsHelper from "mixins/permissions_helper";
import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
import strings from 'mixins/string';
import ResourcesMenu from '../resources_menu.vue';

export default {
  components: {
    Paginate,
    ResourcesMenu,
    PulseLoader,
  },
  mixins: [search, permissionsHelper, strings],
  data() {
    return {
      responseSearch: null,
      responses: [],
      responseCount: 0,
      perPage: 10,
      pageCount: 0,
      indexPage: 0,
      isLoading: true,
    };
  },
  methods: {
    ...mapMutations(['setLoadingStatus']),

    onWorkspaceChange() {
      this.handleBasicUserAccess('Canned Responses');
      this.fetchSnippets();
    },

    fetchSnippets() {
      this.isLoading = true;
      const url = '/snippets.json';
      http
        .get(url, { params: {search: this.responseSearch, per_page: this.perPage, page: this.indexPage + 1, index_page: true } })
        .then(res => {
          this.isLoading = false;
          this.responses = res.data.snippets;
          this.pageCount = res.data.pageCount;
          this.setLoadingStatus(false);
        }).catch(error => {
          this.isLoading = false;
          this.emitError(`Sorry, there was an error fetching Responses. ${error.response.data.message}`);
        });
    },
    updateSearch: _debounce(
      function updateSearch() {
        this.fetchSnippets();
      },
      1000
    ),
    pageSelected(p) {
      this.indexPage = p - 1;
      this.fetchSnippets();
    },
  },
};
</script>

<style lang="scss" scoped>
.response-item {
  height: 8rem;
}
.response-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.response-description {
  height: 2.5rem;
  overflow: hidden;
  text-overflow: ellipsis;

  :deep(div) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 40px;
  }
}
.search-input-icon {
  top: 30%;
  left: 1rem;
}
</style>