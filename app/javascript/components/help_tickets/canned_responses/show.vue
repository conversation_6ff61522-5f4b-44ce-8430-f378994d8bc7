<template>
  <div>
    <div class="col-12 d-flex justify-content-between align-items-center mt-2">
      <h5 class="mb-0">Canned Response</h5>
      <div class="d-flex align-items-center">
        <router-link
          to="/responses"
          class="text-secondary back-to-link d-flex align-items-center mr-3"
        >
          <i class="nulodgicon-arrow-left-c white mr-2" />
          <span>Back to <strong>Responses</strong></span>
        </router-link>
        <a
          v-if="isWrite"
          v-tooltip="'Edit'"
          href="#"
          class="edit ml-1 btn btn-light btn-flat btn-icon-circle has-tooltip"
          role="button"
          @click.stop.prevent="goToEditPage"
        >
          <i class="nulodgicon-edit" />
        </a>
        <a
          v-if="isWrite"
          v-tooltip="'Delete'"
          href="#"
          class="delete ml-1 btn btn-light btn-flat btn-icon-circle has-tooltip"
          role="button"
          @click="openDeleteResponseModal(response.id)"
        >
          <i class="nulodgicon-trash-b" />
        </a>
      </div>
    </div>

    <div class="mt-4">
      <div class="box p-4">
        <div
          v-if="response"
          class="mx-auto w-100"
        >
          <h5>{{ response.title }}</h5>
          <div
            class="mt-3"
            v-html="sanitizeHTML(response.description)"
          />
        </div>
        <div
          v-else
          class="text-muted d-flex"
        >
          <span class="mb-0">Loading</span>
          <pulse-loader
            :loading="true"
            class="ml-2 mt-1"
            color="#0d6efd"
            size="0.5rem"
          />
        </div>
      </div>
    </div>
    <sweet-modal
      ref="deleteResponseModal"
      v-sweet-esc
      title="Before you delete this Response..."
    >
      <template slot="default">
        <div class="text-center">
          <h6 class="mb-3">
            Are you sure you want to delete this Response?
          </h6>
        </div>
      </template>
      <button
        slot="button"
        class="btn-sm btn btn-link text-secondary"
        data-tc-response-cancel
        @click.stop="cancelDelete"
      >
        Cancel
      </button>
      <button
        slot="button"
        class="btn btn-sm btn-link text-danger"
        data-tc-response-delete-button
        @click.stop="deleteResponse(responseToDelete)"
      >
        Delete Response
      </button>
    </sweet-modal>
  </div>
</template>

<script>
import { SweetModal } from 'sweet-modal-vue';
import http from 'common/http';
import strings from 'mixins/string';
import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
import permissionsHelper from "mixins/permissions_helper";

export default {
  components: {
    SweetModal,
    PulseLoader,
  },
  mixins: [permissionsHelper, strings],
  data() {
    return {
      response: null,
      responseToDelete: null,
    };
  },
  methods: {
    onWorkspaceChange() {
      this.fetchResponse();
    },
    fetchResponse() {
      const { id } = this.$route.params;
      http
        .get(`/snippets/${id}.json`)
        .then((res) => {
          this.response = res.data;
        })
        .catch(() => {
          this.emitError('Failed to load Response. Please try again.');
        });
    },
    goToEditPage() {
      this.$router.push(`/responses/${this.response.id}/edit`);
    },
    openDeleteResponseModal(id) {
      this.responseToDelete = id;
      this.$refs.deleteResponseModal.open();
    },
    cancelDelete() {
      this.responseToDelete = null;
      this.$refs.deleteResponseModal.close();
    },
    deleteResponse(id) {
      http
        .delete(`/snippets/${id}.json`)
        .then(() => {
          this.$refs.deleteResponseModal.close();
          this.emitSuccess('Response deleted successfully!');
          this.goToIndex();
        }).catch(error => {
          this.emitError(`Sorry, there was an error fetching Responses. ${error.response.data.message}`);
        });
    },
    goToIndex() {
      this.$router.push('/responses');
    },
  },
};
</script>
