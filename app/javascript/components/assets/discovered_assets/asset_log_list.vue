<template>
  <div
    ref="outerContainer"
    class="w-100 outer-container"
  >
    <table 
      class="table mb-0 discovered-assets-table position-relative"
    >
      <thead>
        <tr>
          <th class="pl-4">Asset</th>
          <th class="th-pill-gen">Genuity Status</th>
          <th class="th-pill-intg">{{ name }} Status</th>
          <th>Make & Model</th>
          <th>MAC / Serial #</th>
          <th>Location</th>
          <th>Assigned To</th>
          <th>Last Check-in Time</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody 
        ref="assetsTable"
      >
        <tr 
          v-for="(asset) in discoveredAssetsLogs" 
          :key="asset.id"
          :class="`${getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).class}-row`"
        >
          <td class="d-flex align-items-center display-name">
            <icon-badge
              :tooltip-message="asset.assetType"
              :img-src="assetTypeImageSrc(asset)"
              :img-height-pixels="35"
              :img-padding-pixels="5"
              :background-color-class="`mt-n2 ml-n2 mx-2 pt-2`"
            />
            <div class="pt-2">
              {{ asset.displayName }}
            </div>
          </td>
          <td>
            <div 
              class="status-pill"
              :class="getGenuityStatus(asset.managedAssetId).class"
            >
              {{ getGenuityStatus(asset.managedAssetId).label }}
            </div>
          </td>
          <td>
            <div
              v-tooltip="getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).toolTip"
              class="status-pill"
              :class="getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).class"
            >
              {{ getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).label }}
            </div>
          </td>
          <td>
            {{ asset.manufacturer }} {{ asset.model }}
          </td>
          <td v-if="asset.macAddresses && asset.macAddresses.length > 0 && asset.machineSerialNo">
            <p class="m-0">
              {{ asset.macAddresses[0] }} <span class="text-muted">/</span> {{ asset.machineSerialNo }}
            </p>
          </td>
          <td v-else>
            {{ asset.macAddresses[0] }} {{ asset.machineSerialNo }}
          </td>
          <td>
            {{ asset.locationName }}
          </td>
          <td>
            <div 
              v-if="asset.userInfo"
              class="d-flex align-items-center used-by"
            >
              <avatar 
                :size="30"
                :src="asset.userInfo.avatar"
                :username="asset.userInfo.fullName || asset.userInfo.email"
                class="logo-outline mr-2 avatar-logo"
              />
              <div> {{ asset.userInfo.fullName }} </div>
            </div>
          </td>
          <td class="small">
            {{ formmattedLastSyncedAt(asset.lastCheckInTime) }}
          </td>
          <td>
            <div class="d-flex pl-2">
              <i 
                class="genuicon-ellipsis-v btn btn-round btn-link text-dark my-n1 mr-n2.5 clickable three-dots" 
                @click.stop="toggleDropdown(asset.id)"  
              />
            </div>
            <div v-if="openDropdownRowId === asset.id">
              <basic-dropdown
                class="dropdown-menu not-as-small dropdown-filter d-block"
                :show-dropdown="openDropdownRowId === asset.id"
                :right="-50"
                :max-width = "7"
                @on-blur="handleDropdownBlur(asset.id)"
              >
                <div>
                  <span
                    v-if="getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).label === 'New'"
                    class="text-secondary action-dropdown-box"
                    @click.stop="openImportModal"
                  >
                    <i class="nulodgicon-link" />
                    <span class="pl-1">Import Asset</span>
                  </span>
                  <span
                    v-if="getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).label === 'New'"
                    class="text-secondary action-dropdown-box"
                    @click.stop="openIgnore(asset.id)"
                  >
                    <i class="nulodgicon-trash-b" />
                    <span class="pl-1">Ignore Asset</span>
                  </span>
                  <span
                    v-if="
                      getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).label === 'Check Device'
                        || getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).label === 'Not Reporting' 
                    "
                    class="text-secondary action-dropdown-box"
                    @click.stop="resyncIntegration"
                  >
                    <i class="genuicon-refresh" />
                    <span
                      v-tooltip="`Start syncing with ${name}`"
                      class="pl-1"
                    >
                      Resync
                    </span>
                  </span>
                  <span
                    v-if="getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).label !== 'New'"
                    class="text-secondary action-dropdown-box"
                    @click.stop="applyManagedAssetAction(asset.managedAssetId, 'edit')"
                  >
                    <i class="nulodgicon-edit" />
                    <span class="pl-1">Edit Asset</span>
                  </span>
                  <span
                    v-if="getIntegrationStatus(asset.managedAssetId, asset.lastCheckInTime).label !== 'New'"
                    class="text-secondary action-dropdown-box"
                    @click.stop="applyManagedAssetAction(asset.managedAssetId, 'view')"
                  >
                    <i class="nulodgicon-eye" />
                    <span class="pl-1">View Asset</span>
                  </span>
                </div>
              </basic-dropdown>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <asset-list 
      ref="assetListComponent"
      :assets-to-import="[selectedAsset]"
    />

    <sweet-modal
      ref="ignoreModal"
      v-sweet-esc
      class="ignore-modal"
      title="Before we proceed..."
    >
      <template slot="default">
        <div class="text-center">
          <h6 class="mb-3">
            Are you sure you want to ignore selected discovered asset?
          </h6>
        </div>
      </template>
      <button
        slot="button"
        class="btn btn-link text-secondary"
        @click.stop="closeIgnore"
      >
        No, keep as is
      </button>
      <button
        slot="button"
        class="btn btn-primary ml-2"
        @click.stop="okIgnore"
      >
        Yes, ignore
      </button>
    </sweet-modal>

  </div>
</template>

<script>
  import { SweetModal } from 'sweet-modal-vue';
  import { mapGetters } from 'vuex';
  import { Avatar } from 'vue-avatar';
  import MomentTimezone from 'mixins/moment-timezone';
  import http from 'common/http';
  import assetImages from '../../../mixins/asset_images';
  import IconBadge from '../../shared/icon_badge.vue';
  import BasicDropdown from '../../shared/basic_dropdown.vue';
  import assetList from './asset_list.vue';

  export default({
    components: {
      SweetModal,
      IconBadge,
      Avatar,
      BasicDropdown,
      assetList,
    },
    mixins: [assetImages, MomentTimezone],
    props: {
      name: {
        type: String,
        default: 'Integration',
      },
    },
    data() {
      return {
        selectedAssetID : 0 ,
        openDropdownRowId: null,
        toIgnoreId: null,
      };
    },
    computed: {
      ...mapGetters([
        'discoveredAssetsLogs',
        'assetDiscovery',
      ]),
      selectedAsset() {
        return this.discoveredAssetsLogs.find(asset => asset.id === this.selectedAssetID) || this.discoveredAssetsLogs[0];
      },
      getAssetsTableHeight() {
        return this.$refs.assetsTable.offsetHeight+450;
      },
    },
    mounted() {
      window.addEventListener('scroll', this.handleScroll, true);
    },
    beforeUnmount() {
      window.removeEventListener('scroll', this.handleScroll, true);
    },
    methods: {
      formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        });
      },
      formatAge(days) {
        return days === 1 ? "yesterday." : `for ${days} days`;
      },
      formmattedLastSyncedAt(lastCheckInTime) {
        const timeToFormat = lastCheckInTime;
        const timePassed = this.$moment.tz(timeToFormat, Vue.prototype.$timezone).format('YYYY-MM-DD h:mm A');;

        if (timePassed === "Invalid date") {
          return 'No date present';
        }

        return `${timePassed}`;
      },
      calculateAge(time) {
        const today = new Date();
        const inputDate = new Date(time);

        const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const inputDateOnly = new Date(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate());

        const msPerDay = 1000 * 60 * 60 * 24;
        return Math.floor((todayDateOnly - inputDateOnly) / msPerDay);
      },
      setModalHeight(modal) {
        modal.$el.style.height = `${this.getAssetsTableHeight}px`; 
      },
      getAgeClass(days) {
        switch (true) {
          case days <= 7:
            return '';
          default:
            return 'older';
        }
      },
      isDiscoveryOld() {
        return this.assetDiscovery === "old";
      },
      getClass(label) {
        return label.split(' ').join('-').toLowerCase();
      },
      getStatusObject(statusLabel, statusToolTip = '') {
        return {
          label: statusLabel,
          class: this.getClass(statusLabel),
          toolTip: statusToolTip,
        };
      },
      getGenuityStatus(managedAsset) {
        return this.getStatusObject(managedAsset ? 'Present' : 'Not Present');
      },
      getIntegrationStatus(managedAsset, lastCheckInTime) {
        if (!managedAsset) return this.getStatusObject('New','Newly reported asset, not yet in Genuity.');
        if (!lastCheckInTime) return this.getStatusObject('Missing',`This asset is missing from ${this.name}.`);
        if (this.calculateAge(lastCheckInTime) > 7) return this.getStatusObject('Not Reporting', `This asset is not being reported by ${this.name} for more than 7 days.`);

        return this.getStatusObject('Synced',`This asset is present in ${this.name}.`);
      },
      handleDropdownBlur(rowId) {
        setTimeout(() => {
          if (this.openDropdownRowId === rowId) { this.openDropdownRowId = null; }
        }, 10);
      },
      toggleDropdown(rowId) {
        if (this.openDropdownRowId === rowId ) {
          this.openDropdownRowId = null;
          this.selectedAssetID = 0;
        } else {
          this.openDropdownRowId = rowId;
          this.selectedAssetID = rowId;
        }
      },
      handleScroll() {
        if (this.openDropdownRowId) {
          this.openDropdownRowId = null;
        }
      },
      applyManagedAssetAction(managedAssetId, action) {
        const baseUrl = `${window.location.origin}/managed_assets/${managedAssetId}`;
        const url = action === 'view' ? baseUrl : `${baseUrl}/edit`;
        window.open(url, '_blank');
      },
      resyncIntegration() {
        this.$emit('resync');
      },
      openIgnore(assetId){
        this.toIgnoreId = assetId;
        const { ignoreModal } = this.$refs;

        this.openDropdownRowId = null;
        this.setModalHeight(ignoreModal);
        ignoreModal.open();

        this.$nextTick(() => {
          ignoreModal.$el.scrollIntoView({ behavior: 'smooth', block: 'start' });
        });
      },
      closeIgnore() {
        this.toIgnoreId = null;
        this.$refs.ignoreModal.close();
      },
      okIgnore() {
        this.$refs.ignoreModal.close();
        const params = {};
        params.discovered_asset_ids = [this.toIgnoreId];
        
        http
          .post("/discovered_assets/bulk_archive.json", params )
          .then(() => {
            this.emitSuccess(`Successfully ignored discovered assets`);
            this.closeIgnore();
            this.$store.dispatch('fetchDiscoveredAssetsLogs');
          }).catch(() => {
            this.emitError(`Sorry, there was an error archiving assets. Please try again.`);
          });
      },
      openImportModal() {
        this.openDropdownRowId = null;
        const moreOffSet = this.discoveredAssetsLogs.length > 20 ? 25 : 150;
        this.$refs.assetListComponent.setHeight(this.getAssetsTableHeight + moreOffSet);
        this.$refs.assetListComponent.addToAssets();
      },
    },
  });
</script>

<style lang="scss" scoped>
  .table thead th {
    font-size: 0.875rem;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: left !important;
  }

  th {
    background-color: var(--themed-main-bg);
  }

  td {
    padding: 0.55rem !important;
    font-size: 0.875rem;
    text-align: left !important;
  }

  tr {
    cursor: pointer;
  }

  .th-pill-gen {
    width: 9rem;
  }

  .th-pill-intg {
    width: 10rem;
  }

  .discovered-assets-table th {
    border-top: 0rem;
    border-bottom: 0.063rem solid $themed-fair;
  }

  :deep(.ignore-modal .proceed-modal .sweet-modal .sweet-box-actions .sweet-action-close:hover) {
      color: var(--themed-light) !important;
    }

  :deep(.ignore-modal .proceed-modal .sweet-modal .sweet-box-actions .sweet-action-close) {
    color: var(--themed-base) !important;
  }

  .ignore-modal {
    left: 50% !important;
    transform: translateX(-50%) !important;
    margin-left: 0 !important;
    min-height: 100svh;
  }

  .outer-container {
    padding: 0% !important;
    overflow-x: auto;
    overflow-y: auto;
    max-height: calc(100vh - 18rem);
    border: 0.1rem solid lightgray;
    border-radius: 0.75rem !important;
  }

  .outer-container thead th {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .older {
    color: #cf3232
  }

  .empty-space {
    width: 10rem;
  }

  .table thead th {
    vertical-align: bottom;
    border-bottom: none !important;
    border-bottom: 0.125rem solid var(--themed-very-fair);
  }

  .table {
    min-width: 96rem;
    margin-bottom: 1rem;
    background-color: transparent;
    border-collapse: separate;
    border-spacing: 0;
  }

  .status-pill {
    width: fit-content;
    border-radius: 0.7rem;
    align-self: center;
    padding: 0rem 0.75rem;
  }

  .not-present {
    color: var(--themed-secondary);
    background: var(--themed-moderate-light);
  }

  .present, .synced {
    background-color: var(--themed-green-subtle);
    color: var(--themed-telecom-dark);
  }

  .new-row {
    background-color: var(--themed-new-row);
  }

  .missing-row {
    background-color: var(--themed-check-row);
  }

  .not-reporting-row {
    background-color: var(--themed-not-reporting-row);
  }

  .new {
    background-color: var(--themed-link);
    color: #ffffff;
  }

  .missing {
    background-color: $yellow-dark;
    color: #ffffff;
  }

  .not-reporting {
    background-color: $orange-dark;
    color: #ffffff;
  }

  .three-dots {
    width: 2.5rem !important;
  }

  .action-dropdown-box {
    display: block;
    padding: 0.25rem 0rem;
    cursor: pointer;
    color: #000;
    padding-left: 0.5rem;
    padding-right: 0.9rem;
  }

  .action-dropdown-box:hover {
    background-color: var(--themed-light);
    color: var(--themed-base);
  }

  .used-by {
    width: 7rem;
  }

  .avatar-logo {
    min-width: 2rem;
  }

  .display-name {
    min-width: 8rem;
  }

</style>
