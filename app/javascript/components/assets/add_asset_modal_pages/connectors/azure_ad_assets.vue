<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">

    <div class="col-md-12">
      <h4 class="mt-4 mb-3">
        How to sync:
      </h4>
      <step-instruction-box
        step-number="1"
        step-title="Please read these instructions carefully before proceeding."
      >
        <ul
          class="pl-4 mt-3 not-as-small"
          v-html="integrationInstructions"
        />
      </step-instruction-box>
      <div class="mt-4">
        <import-option-selector
          :integration-name="'Azure AD'"
          @back-to-acc-details="close"
          @submit="setImporttype"
        />
      </div>
      <div class="form-group mt-3 mb-1 clearfix text-right">
        <button
          class="btn btn-sm btn-link text-secondary mr-2"
          @click.stop="close"
        >
          Cancel
        </button>
        <button
          class="btn btn-sm btn-primary font-weight-bold"
          @click.stop="proceedIntegrationRequest"
        >
          Yes, proceed
        </button>
      </div>
    </div>
  </div>
</template>

<script>
  import OnboardingTitle from  'components/shared/module_onboarding/onboarding_title.vue';
  import StepInstructionBox from 'components/shared/module_onboarding/step_instruction_box.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import ImportOptionSelector from 'components/shared/module_onboarding/import_option_selector_modal.vue';

  export default {
    components: {
      OnboardingTitle,
      StepInstructionBox,
      ImportOptionSelector,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        header: 'Integrate Azure AD Devices',
        subHeader: 'Sync your existing asset services with AzureAD Devices',
        imageSrc: 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/azure_ad.png',
        integrationInstructions: `<li class='mt-3'> You must login with an account that have access to Azure AD devices.</li>
                                  <li class='mt-1'>Syncing might take up to 15 minutes.</li>`,
        importType: "managed_asset",
      };
    },
    methods: {
      onWorkspaceChange() {
        this.$parent.$parent.setCondenseModal(true);
      },
      setImporttype(importType) {
        this.importType = importType;
      },
      proceedIntegrationRequest() {
        const state = encodeURIComponent(JSON.stringify({ import_type: this.importType }));
        window.location.href = `/integrations/azure_ad_assets/adminconsent?state=${state}`;
      },
      close() {
        this.$emit('close');
      },
    },
  };
</script>
