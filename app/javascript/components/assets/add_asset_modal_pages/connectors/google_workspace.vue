<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">

    <div class="row">
      <div class="col-md-12">
        <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <step-instruction-box
          step-number="1"
          step-title="Please read these instructions carefully before proceeding."
        >
          <ul
            class="pl-4 mt-3 not-as-small"
            v-html="integrationInstructions"
          />
        </step-instruction-box>
        <div class="mt-4">
          <import-option-selector
            :integration-name="'Google Workspace'"
            @submit="setImporttype"
          />
        </div>
        <div class="form-group mt-3 mb-1 clearfix text-right">
          <button
            class="btn btn-sm btn-link text-secondary mr-2"
            @click.stop="close"
          >
            Cancel
          </button>
          <button
            class="btn btn-sm btn-primary font-weight-bold"
            @click.stop="startGoogleAuth"
          >
            Yes, proceed
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'common/http';
  import OnboardingTitle from  'components/shared/module_onboarding/onboarding_title.vue';
  import StepInstructionBox from 'components/shared/module_onboarding/step_instruction_box.vue';
  import permissionsHelper from 'mixins/permissions_helper';
  import ImportOptionSelector from 'components/shared/module_onboarding/import_option_selector_modal.vue';

  export default {
    components: {
      OnboardingTitle,
      StepInstructionBox,
      ImportOptionSelector,
    },
    mixins: [permissionsHelper],
    data() {
      return {
        header: 'Integrate Google Workspace',
        subHeader: 'Sync your existing asset services with Google Workspace',
        imageSrc: 'https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/google-workspace-devices.png',
        integrationInstructions: `<li class='mt-3'> You must log in using an account that has admin privileges.</li>
                                  <li class='mt-1'> Please be sure that you do not have this account already synced with another connector. 
                                    If this account is already synced, it may result in duplicate assets.
                                  </li>
                                  <li class='mt-1'>Syncing might take up to 15 minutes.</li>`,
        loading: false,
        importType: "managed_asset",
      };
    },
    mounted() {
      this.$parent.$parent.setCondenseModal(true);
    },
    methods: {
      close() {
        this.$emit('close');
      },
      setImporttype(importType) {
        this.importType = importType;
      },
      startGoogleAuth() {
        http
        .get('/integrations/google_workspace/google_auth', { params: { import_type: this.importType } })
        .then((response) => {
          this.loading = false;
          const authUrl = response.data.url;
          window.location.href = authUrl; // Redirect to Google OAuth screen
        })
        .catch((error) => {
          this.loading = false;
          this.errors.push(error.response?.data?.message || 'Something went wrong.');
        });
      },
    },
  };
</script>

<style scoped>
  .instance-input {
    border: 1px solid #ced4da;
    height: calc(2.25rem + 2px);
  }
  .instance-input input {
    height: auto;
  }
  .instance-input input:focus {
    box-shadow: none;
  }

  .instance-input label {
    color: #808080;
  }
</style>
