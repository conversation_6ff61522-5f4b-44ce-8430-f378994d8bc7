<template>
  <div>
    <onboarding-title
      :image-src="imageSrc"
      :header="header"
      :sub-header="subHeader"
    />
    <hr class="mt-4">
    <div 
      v-if="!showImportOption"
      class="row"
    >
      <div class="col-md-12">
        <h4 class="mt-4 mb-3">
          How to sync:
        </h4>
        <div class="rounded bg-lighter p-3 border border-light">
          <div>
            <div class="onboarding-rounded-circle align-middle bg-info text-center text-white d-inline-block font-weight-bold">
              1
            </div>
            <h5 class="d-inline pl-2 text-secondary align-middle">
              In order to integrate Kandji
            </h5>
            <ul class="pl-4 mt-3 not-as-small">
              <li class="mt-3">
                <a
                  href="https://support.kandji.io/support/solutions/articles/72000560412-kandji-api"
                  target="_blank"
                >
                  Click here
                </a>
                and follow the given instructions to get your API token and API URL.
              </li>
            </ul>
          </div>
        </div>

        <div class="rounded bg-lighter p-3 mt-4 border border-light">
          <div>
            <div>
              <div class="onboarding-rounded-circle align-middle bg-info text-center text-white d-inline-block font-weight-bold">
                2
              </div>
              <h5 class="d-inline pl-2 text-secondary align-middle">
                Fill out the following information
              </h5>
              <div class="mt-3">
                <p
                  v-if="errors.length"
                  class="text-danger not-as-small mb-0 mt-3"
                >
                  <strong>Please correct the following error(s):</strong>
                  <ul class="list-unstyled">
                    <li
                      v-for="(error, index) in errors"
                      :key="`error-${index}`"
                    >
                      {{ error }}
                    </li>
                  </ul>
                </p>
                <form class="w-100 mt-3">
                  <div class="form-group">
                    <div class="pb-3">
                      <label for="api_url">
                        API URL
                      </label>
                      <input
                        id="access_key"
                        v-model="kandjiConfig.api_url"
                        class="form-control"
                        required="true"
                        type="text"
                      >
                      <p class="small text-muted mt-1">
                        Note: API URL should be starting with https://
                      </p>
                    </div>
                    <div class="pb-3">
                      <label for="api_token">
                        API Token
                      </label>
                      <input
                        id="secret_key"
                        v-model="kandjiConfig.api_token"
                        class="form-control"
                        required="true"
                        type="text"
                      >
                    </div>
                  </div>
                  <div class="form-group mt-3 mb-1 text-right">
                    <button
                      v-if="!isLoading"
                      class="btn btn-sm btn-link text-secondary mr-2"
                      @click.prevent="close"
                    >
                      <span>Cancel</span>
                    </button>
                    <submit-button
                      :is-saving="isLoading"
                      :is-validated="!hasInvalidData"
                      :disabled="hasInvalidData"
                      :btn-classes="'btn-sm px-3'"
                      btn-content="Continue"
                      saving-content="Syncing Kandji"
                      @submit="toggleKandjiScreen"
                    />
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showImportOption">
      <import-option-selector
        :integration-name="'Kandji'"
        @back-to-acc-details="backToAccDetail"
        @submit="submitKandjiForm"
      />
    </div>
  </div>
</template>

<script>
  import OnboardingTitle from  'components/shared/module_onboarding/onboarding_title.vue';
  import common from 'components/shared/module_onboarding/common';
  import SubmitButton from 'components/shared/submit_button.vue';
  import http from 'common/http';
  import ImportOptionSelector from 'components/shared/module_onboarding/import_option_selector_modal.vue';

  export default {
    components: {
      OnboardingTitle,
      SubmitButton,
      ImportOptionSelector,
    },
    mixins: [common],
    data() {
      return {
        header: 'Integrate Kandji',
        subHeader: 'Sync your existing asset services with Kandji',
        imageSrc: 'https://nulodgic-static-assets.s3.amazonaws.com/images/logos/integrations-logos/kandji.png',
        kandjiConfig: {
          api_url: '',
          api_token: '',
          import_type: 'discovered_asset',
        },
        isLoading: false,
        showImportOption: false,
      };
    },
    computed: {
      hasInvalidData() {
        return !this.kandjiConfig.api_url ||
               !this.kandjiConfig.api_token ||
               !this.kandjiConfig.api_url.startsWith('https://');
      },
    },
    methods: {
      onWorkspaceChange() {
        this.$parent.$parent.setCondenseModal(true);
      },
      backToAccDetail() {
        this.showImportOption = false;
        this.loading = false;
      },
      toggleKandjiScreen() {
        this.showImportOption = true;
      },
      submitKandjiForm(importType) {
        this.isLoading = true;
        this.errors = [];
        this.kandjiConfig.import_type = importType;
        if (!this.hasInvalidData) {
          http
            .post('/integrations/kandji/configs.json', { kandji_credentials: this.kandjiConfig })
            .then(() => {
              this.isLoading = false;
              this.$store.dispatch('fetchCompanyIntegrations');
              this.emitSuccess('Kandji integration is in progress.');
              this.close();
            })
            .catch((error) => {
              this.emitError('Please fix the highlighted errors');
              this.errors.push(error.response.data.message);
              this.isLoading = false;
            });
        } else {
          if (!this.kandjiConfig.api_url) {
            this.errors.push('API URL is required.');
          }
          if (!this.kandjiConfig.api_token) {
            this.errors.push('API Token is required.');
          }
          this.isLoading = false;
        }
      },
      close() {
        this.$emit('close');
      },
    },
  };
</script>
