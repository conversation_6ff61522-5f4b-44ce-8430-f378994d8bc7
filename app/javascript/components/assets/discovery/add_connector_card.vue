<template>
  <div 
    class="d-flex align-items-center flex-column justify-content-center add-button pointer box box--with-hover" 
    @click.stop="goToAddConnectors"
  >
    <div class="pb-3 d-flex align-items-center flex-column justify-content-center">
      <h1 v-tooltip="'Add Connector'" >
        +
      </h1>
      <h5 class="m-0">
        Add Asset Connector
      </h5>
    </div>
  </div>
</template>

<script>
  export default {
    methods: {
      goToAddConnectors() {
        this.$router.push('/discovery_tools/connectors');
      },
    },
  };
</script>

<style lang="scss" scoped>
  h5 {
    font-weight: 100;
  }

  .add-button {
    cursor: pointer;
    min-height: 11.5rem;
  }
</style>
