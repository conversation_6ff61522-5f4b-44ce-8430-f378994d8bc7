<template>
  <sweet-modal
    ref="tasksInfoModalContent"
    v-sweet-esc
    class="modal-content-padding mt-5"
    width="60%"
  >
    <div class="box py-2 template-banner">
      <div class="box__inner text-left">
        <div class="row">
          <div class="col-7">
            <h6 class="mb-0 my-3">
              Automatically handle your routine tasks
            </h6>

            <ul class="mb-0 text-themed-fair mb-3">
              <li class="not-as-small mt-2 mb-1">
                <span class="nulodgicon-checkmark small" /> {{ moduleActions }}
              </li>
              <li class="not-as-small mb-1">
                <span class="nulodgicon-checkmark small" /> {{ isAssetsModule ? 'Create a new task' : 'Create a new task or clone existing ones below.' }}
              </li>
              <li class="not-as-small">
                <span class="nulodgicon-checkmark small" /> Drag and drop tasks to customize execution order.
              </li>
            </ul>
          </div>
          <div class="col-5">
            <img
              :src="imageSrc"
              alt="Image"
              class="centered-image w-100"
            >
          </div>
        </div>
      </div>
    </div>
    <h5 class="d-flex font-weight-bold mt-6">
      What are Automated Tasks?
    </h5>
    <div class="text-left mt-3">
      <p class="text-secondary not-as-small">
        <strong>Automated tasks</strong> handle routine tasks automatically. 
        {{ moduleInfoDetails }}
      </p>
      <p class="text-secondary not-as-small">
        Each automated task is evaluted as they appear in the ordered task list.
      </p>
    </div>
  </sweet-modal>
</template>

<script>
  import { mapGetters } from 'vuex';
  import { SweetModal } from 'sweet-modal-vue';

  export default {
    components: { SweetModal },
    computed: {
      ...mapGetters(['currentModule']),
      isHelpDesk() {
        return this.currentModule === 'helpdesk';
      },
      moduleActions() {
        if (this.isHelpDesk) {
          return 'Flexibly assign users, prioritize tickets, and more.';
        }
        return `Flexibly assign users, alert on hardware status, and more.`;
      },
      moduleInfoDetails() {
        if (this.isHelpDesk) {
          return "Assign agents to tickets or set a ticket's priority with automated workflows.";
        }
        return "Notify on hardware state or send an email when disk space is low with automated workflows.";
      },
      storageKey() {
        return this.isHelpDesk ? `${this.currentModule}_${$workspace.id}_show_banner` : `managed_assets_show_banner`;
      },
      imageSrc() {
        return this.isHelpDesk
          ? "https://nulodgic-static-assets.s3.amazonaws.com/images/automated_task_visualization.gif"
          : "https://nulodgic-static-assets.s3.us-east-1.amazonaws.com/images/assets_automated_tasks_visualization.gif";
      },
      isAssetsModule() {
        return window.location.href.includes('managed_assets');
      },
    },
    methods: {
      open() {
        this.$refs.tasksInfoModalContent.open();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .template-banner {
    color: white;
    background: rgb(27,55,94);
    background: linear-gradient(124deg, rgba(4,30,66,1) 51%, rgba(4,30,66,1) 51%, rgba(255,255,255,1) 51%);
  }
</style>
