<template>
  <div class="col-12">
    <div class="form-group">
      <div>
        <add-recipient
          v-if="targetOptions"
          :id="`send-email--target-${id}`"
          ref="sendEmail"
          class="row"
          :target-options="targetOptions"
          :value="targets"
          @change="handleTarget"
        />
        <span
          v-if="targetError"
          class="form-text small text-danger"
        >
          {{ targetError }}
        </span>
      </div>
      <div 
        v-if="optionIncluded('specified')"  
        class="pb-2 mt-4"
      >
        <textarea
          :id="`send-email--recipients-${id}`"
          v-model="recipients"
          class="form-control"
          required
          data-tc-recipients
          placeholder="<EMAIL>, <EMAIL>"
          @input="handleRecipients"
          @blur="showRecipientsError"
        />
      </div>
      <div
        v-if="optionIncluded('form_field')"  
        class="pb-2 mt-4"
      >
        <form-field
          :custom-form-selected="initialCustomForms"
          :custom-field-selected="initialCustomField"
          @selectedField="handleSelectedField"
        />
        <div class="clearfix">
          <div
            v-if="customFieldError && !customField"
            class="float-left small text-danger"
          >
            {{ customFieldError }}
          </div>
        </div>
      </div>
      <span
        v-if="recipientsError"
        class="form-text small text-danger"
      >
        {{ recipientsError }}
      </span>
      <div 
        v-if="optionIncluded('contributors')"
        class="mt-3"
      >
        <label>
          Select the recipient users or groups
        </label>
        <mass-assign
          :value="selectedContributors"
          class="mass-assign"
          @input="contributorIdChanged"
        />
      </div>
      <span
        v-if="contributorError"
        class="form-text small text-danger"
      >
        {{ contributorError }}
      </span>
    </div>

    <div class="d-flex">
      <div 
        v-if="showOptions"
        class="col-6"
      >
        <label class="row">Select Email Customization</label>
        <div>
          <multi-select
            :id="`send-email--option-${id}`"
            v-model="selectedOption"
            class="row mb-2"
            required
            placeholder="Select Option"
            label="name"
            track-by="value"
            :multiple="false"
            :options="options"
            @input="handleOption"
          />
          <span
            v-if="optionSelectError"
            class="form-text row small text-danger"
          >
            {{ optionSelectError }}
          </span>
        </div>
      </div>

      <div
        v-if="showTypeSelect && showOptions"
        class="col-6"
      >
        <div class="d-flex align-items-center mb-1">
          <label class="row mb-0">Email Template</label>
          <div class="ml-auto mr-4">
            <a
              href="/help_tickets/settings/email_templates/end_users"
              target="_blank"
            >
              Create a new template
            </a>
            <i
              v-tooltip="{ content: 'Add a new template or edit existing ones' }"
              class="nulodgicon-help-circled text-muted font-weight-semi-bold clickable"
            />
          </div>
        </div>
        <div>
          <multi-select
            :id="`send-email--option-template-${id}`"
            v-model="selectedTemplate"
            class="row mb-2"
            required
            placeholder="Select Option"
            label="templateTitle"
            track-by="templateTitle"
            :close-on-select="true"
            :multiple="false"
            :show-labels="false"
            :group-select="false"
            :group-values="true ? 'groupValues' : null"
            :group-label="true ? 'groupLabel' : null"
            :options="filteredOptions"
            @input="handleTemplates"
            @open="fetchTemplates"
          >
            <template
              slot="option"
              slot-scope="props"
            >
              <div class="row align-items-center mr-n2">
                <div class="col item-name px-2 ml-2">
                  <span class="font-weight-bold border-t-300 pt-2 my-1 pl-2 heading">
                    {{ props.option.$groupLabel }}
                  </span>
                  <span class="avatar-name not-as-small truncate">
                    {{ props.option.templateTitle }}
                  </span>
                </div>
              </div>
            </template>
          </multi-select>
          <span
            v-if="templateSelectError"
            class="form-text row small text-danger"
          >
            {{ templateSelectError }}
          </span>
        </div>
      </div>
    </div>

    <div 
      class="form-group mb-3"
      :style="{ display: displayContent }"
    >
      <label>
        Subject
        <i
          v-tooltip.right="`In order to create a comment from email replies, ensure to add the 'ticket number' replacement in the email's subject with this format: [#{ticket_number}]`"
          class="nulodgicon-information-circled h6 ml-1"
        />
      </label>
      <input
        :id="`send-email--subject-${id}`"
        v-model="subject"
        type="text"
        class="form-control"
        required
        data-tc-subject
        :disabled="disabledField"
        @input="handleSubject"
        @blur="showSubjectError"
        @focus="checkLastInputTag('subject')"
      >
      <span
        v-if="showSubjectWarning"
        class="form-text small text-warning"
      >
        Ticket number replacement should be formatted as [#{ticket_number}].
      </span>
      <span
        v-if="subjectError"
        class="form-text small text-danger"
      >
        {{ subjectError }}
      </span>
    </div>
    <div 
      class="form-group mb-0"
      :style="{ display: displayContent }"
      @click="checkLastInputTag('body')"
    >
      <label>Body</label>
      <input
        :id="`send-email--body-${id}`"
        :value="body"
        required
        class="hidden"
      >
      <trix-vue
        ref="trixEditor"
        :key="compKey"
        :input-id="`send-email--body-${id}`"
        :value="body"
        data-tc-body
        :disabled="disabledField"
        :allow-attachments="false"
        @input="handleBody"
        @blur="showBodyError"
      />
      <div class="clearfix">
        <div
          v-if="bodyError"
          class="float-left small text-danger"
        >
          {{ bodyError }}
        </div>
      </div>
    </div>
    <div 
      v-if="model !== 'ExecutionDate'"
      :style="{ display: displayContent }"
    >
      <interpolated-note 
        class="mb-3"
        :text="focusedInputField"
        :current-focused="currentFocused"
        @select="setInterpolatedNote"
      />
    </div>
    <div 
      v-if="isAssetsModule && hasNonFailedIntegrationEvent" 
      class="asset-info-settings"
    >
      <div class="info-label">Include additional asset information in the email or alert.</div>
      <div class="checkbox-grid">
        <label class="checkbox-option">
          <input 
            v-model="checkboxValues.machineSerialNumber" 
            type="checkbox" 
            @input="handleInput"
          >
          Machine serial number
        </label>
        <label class="checkbox-option">
          <input 
            v-model="checkboxValues.macAddress" 
            type="checkbox" 
            @input="handleInput"
          >
          Mac address
        </label>
        <label class="checkbox-option">
          <input 
            v-model="checkboxValues.assignedTo" 
            type="checkbox" 
            @input="handleInput"
          >
          Assigned to
        </label>
        <label class="checkbox-option">
          <input 
            v-model="checkboxValues.managedBy" 
            type="checkbox" 
            @input="handleInput"
          >
          Managed by
        </label>
        <label class="checkbox-option">
          <input 
            v-model="checkboxValues.location" 
            type="checkbox" 
            @input="handleInput"
          >
          Location
        </label>
      </div>        
    </div>
  </div>
</template>

<script>
import http from 'common/http';
import _get from 'lodash/get';
import { mapGetters, mapActions } from 'vuex';
import TrixVue from 'components/trix_vue';
import MassAssign from 'components/shared/company_user_assign/mass_assign.vue';
import permissionsHelper from 'mixins/permissions_helper';
import MultiSelect from 'vue-multiselect';
import FormField from './form_field_for_email.vue';
import AddRecipient from './add_recipients.vue';
import InterpolatedNote from '../interpolated_note.vue';
import common from "../common";

const REGEX_PATTERN = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

const TARGET_OPTIONS = [
  {
    value: "assigned",
    name: "Agent Assigned",
  },
  {
    value: "admins",
    name: "All Admins",
  },
  {
    value: "agents",
    name: "All Agents",
  },
  {
    value: "ticket_creator",
    name: "Ticket Creator",
  },
  {
    value: 'related',
    name: "All People added to the ticket",
  },
  {
    value: 'impacted',
    name: 'Ticket Followers and include any form field that is designated/Included as Followers',
  },
  {
    value: 'specified',
    name: 'Specific People Emails',
  },
  {
    value: 'contributors',
    name: 'Specific People or Groups',
  },
  {
    value: 'form_field',
    name: 'Specific People Form Field',
  },
];

const OPTIONS = [
  {
    value: 1,
    name: 'Customize an email',
  },
  {
    value: 2,
    name: 'Select Template',
  },
];

export default {
  components: {
    InterpolatedNote,
    TrixVue,
    MassAssign,
    AddRecipient,
    MultiSelect,
    FormField,
  },
  mixins: [common, permissionsHelper],
  data() {
    return {
      body: _get(this, `value.actions[${this.id}].value.body`),
      subject: _get(this, `value.actions[${this.id}].value.subject`),
      recipients: _get(this, `value.actions[${this.id}].value.recipients`),
      target: _get(this, `value.actions[${this.id}].value.target`),
      selectedOption: this.isMsp() ? OPTIONS[0] : _get(this, `value.actions[${this.id}].value.option`),
      selectedTemplate: _get(this, `value.actions[${this.id}].value.template`),
      contributors: _get(this, `value.actions[${this.id}].value.contributors`, []),
      option: null,
      template: null,
      targetError: null,
      recipientsError: null,
      subjectError: null,
      bodyError: null,
      customField: null,
      selectedCustomForm: null,
      customFieldError: null,
      optionSelectError: null,
      templateSelectError: null,
      contributorError: null,
      customHelpdeskEmails: null,
      compKey: true,
      currentFocused: '',
      disabledField: false,
      templateList: [],
      checkboxValues: {
        machineSerialNumber: false,
        macAddress: false,
        assignedTo: false,
        managedBy: false,
        location: false,
      },
    };
  },
  computed: {
    ...mapGetters(['displaySurveySettings']),
    model() {
      return _get(this, `value.actions[${this.id}].nodeType.model`);
    },
    filteredOptions() {
      if (this.selectedOption && this.templateList.length) {
        return [
          {
            groupLabel: "Templates for End Users",
            groupValues: this.templateList.filter(x => x.templateType === "end_users"),
          },
          {
            groupLabel: "Templates for Agents",
            groupValues: this.templateList.filter(x => x.templateType === "agents"),
          },
          {
            groupLabel: "Templates for Surveys",
            groupValues: this.templateList.filter(x => x.templateType === "surveys" && this.displaySurveySettings ),
          },
        ];
      }
      return [];
    },
    initialCustomForms() {
      if (this.optionIncluded('form_field')) {
        return _get(this, `value.actions[${this.id}].value.selectedCustomForms`);
      }
      return null;
    },
    initialCustomField() {
      if (this.optionIncluded('form_field')) {
        return _get(this, `value.actions[${this.id}].value.customField`);
      }
      return null;
    },
    taskName() {
      return _get(this, `value.events[0].nodeType.name`);
    },
    taskNodeName() {
      return _get(this, `value.events[0].nodes[0].nodeType.name`);
    },
    isClosedField() {
      return _get(this, `value.events[0].nodes[0].value.status`);
    },
    isAsignedToField() {
      return _get(this, `value.events[0].nodes[0].value.formField.name`);
    },
    showTemplatesOption() {
      return this.value.events.some(event =>
        event.nodeType && (event.nodeType.name === '{a ticket} is created' || event.nodeType.name === '{a comment} is added to a ticket')
      );
    },
    showClosedSurvey() {
      return this.value.events.some(event =>
        event.nodeType && event.nodeType.name === '{a ticket} is updated' &&
        event.nodes && event.nodes.some(node =>
          node.nodeType && node.nodeType.name === '/a ticket/ with {a form field}' &&
          node.value && node.value.status === 'Closed'
        )
      );
    },
    showAssignedToField() {
      return this.value.events.some(event =>
        event.nodeType && event.nodeType.name === '{a ticket} is updated' &&
        event.nodes && event.nodes.some(node =>
          node.value && node.value.formField && node.value.formField.name === 'assigned_to'
        )
      );
    },
    showOptions() {
      return this.showTemplatesOption || this.showClosedSurvey || this.showAssignedToField;
    },
    options() {
      if (this.isMsp()) return [OPTIONS[0]];
      return OPTIONS;
    },
    showTypeSelect() {
      return this.selectedOption?.name === 'Select Template';
    },
    displayContent() {
      return this.showTypeSelect && this.showOptions ? 'none' : 'block';
    },
    targetOptions() {
      if (this.event.findIndex((ev) => ev.nodeType && ev.nodeType.eventClass === "CommentAdded") >= 0) {
        const privateValue = { value: 'private', name: 'Private comment users' };
        const myOptions = TARGET_OPTIONS.slice(0);
        if (this.hasCommentUserRef) {
          const mentionedValue = { value: 'mentioned', name: 'Users or Groups mentioned in the comment' };
          myOptions.push(mentionedValue);
        }
        myOptions.push(privateValue);
        return myOptions;
      }
      if (this.event.findIndex((ev) => ev.nodeType && ev.nodeType.eventClass === "DateOccurred") >= 0) {
        const dateEventTargets = ['admins', 'agents', 'specified', 'contributors'];
        return TARGET_OPTIONS.filter((opt) => dateEventTargets.includes(opt.value));
      }
      if (this.isAssetsModule) {
        const assetTargets = ['admins', 'contributors'];
        return TARGET_OPTIONS.filter((opt) => assetTargets.includes(opt.value));
      }
      return TARGET_OPTIONS;
    },
    hasCommentUserRef() {
      return this.event.some((ev) => ev.nodes.some((node) => node.nodeType?.subjectClass === 'CommentUserRef'));
    },
    targets() {
      return _get(this, 'target', '');
    },
    selectedContributors() {
      return Array.isArray(this.contributors) ? this.contributors : [this.contributors];
    },
   focusedInputField() {
      if (this.currentFocused === 'body' && !(this.selectedOption?.value === 2)) {
        return this.body;
      } else if (this.currentFocused === 'subject' && !(this.selectedOption?.value === 2)) {
        return this.subject;
      } 
      return '';
    },
    showSubjectWarning() {
      return this.subject?.includes("{ticket_number}");
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
    hasNonFailedIntegrationEvent() {
      return this.value.events.some(
        event => event.nodeType.name !== '{an integration} failed continuously'
      );
    },
  },
  watch: {
    value() {
      if (this.value.companyMailer) {
        const newSubject = this.value.actions[this.id].value.subject;
        const newBody = this.value.actions[this.id].value.body;
        const newTarget = this.value.actions[this.id].value.target;
        if (newSubject !== this.subject) {
          this.handleSubject(this.value.actions[this.id].value.subject);
        }
        if (newBody !== this.body) {
          this.handleBody(this.value.actions[this.id].value.body);
          this.compKey = !this.compKey;
        }
        if (newTarget !== this.target) {
          this.handleTarget(this.value.actions[this.id].value.target);
        }
      }
    },
    selectedOption() {
      if(this.selectedOption?.name === 'Select Template') {
        this.disabledField = true;
        this.compKey = !this.compKey;
      } else {
        this.replaceBody();
        this.disabledField = false;
      }
      this.showOptionError();
    },
    taskName() {
      this.replaceBody();
    },
  },
  created() {
    const savedCheckboxes = _get(this, `value.actions[${this.id}].value.selectedCheckboxes`, {});
    this.checkboxValues = {
      ...this.checkboxValues,
      ...savedCheckboxes,
    };
  },
  methods: {
    ...mapActions('GlobalStore', ['fetchStringInterpolationKeys']),

    checkLastInputTag(focusedField) {
      this.currentFocused = focusedField;
    },
    onWorkspaceChange() {
      // I will admit this isn't the greatest solution, but it's the best
      this.loadFormsAndField();
      this.fetchStringInterpolationKeys(this.model);
      this.fetchCustomEmails();
      if (this.$refs.sendEmail) {
        this.$refs.sendEmail.populateTargets();
      }
      if (this.selectedOption?.value === 2) {
        this.disabledField = true;
        this.compKey = !this.compKey;
      }
    },
    loadFormsAndField() {
      if (this.optionIncluded('form_field')) {
        if (this.value.actions[this.id].value?.customField) {
          this.customField = this.value.actions[this.id].value.customField ;
        }
        if (this.value.actions[this.id].value?.selectedCustomForms) {
          this.selectedCustomForm = this.value.actions[this.id].value.selectedCustomForms ;
        }
      }
      return null;
    },
    setInterpolatedNote(text) {
      this.currentFocused === "subject" ? this.handleSubject(text) : this.handleBody(text);
      this.compKey = !this.compKey;
    },
    handleSelectedField({ customForm, customField }) {
      this.customField = customField || null;
      this.selectedCustomForm = customForm || null;
      this.handleInput();
    },
    valid() {
      if (!this.target) {
        this.targetError = "Must select a recipient for the email.";
        return false;
      }
      if (this.showOptions && !this.selectedOption) {
        this.optionSelectError = "Must select an option.";
        return false;
      }
      if (this.showOptions && this.showTypeSelect && !this.selectedTemplate) {
        this.templateSelectError = "Must select a template.";
        return false;
      }
      let error = document.querySelector(`#send-email--subject-${this.id}`).validationMessage;
      if (error || this.showReplacementError()) {
        return false;
      }

      error = document.querySelector(`#send-email--body-${this.id}`).validationMessage;
      if (!this.body || this.body.length === 0) {
        return false;
      }
      if (this.optionIncluded('specified')) {
        error = document.querySelector(`#send-email--recipients-${this.id}`).validationMessage;
        if (error) {
          return false;
        }
      }
      if (this.optionIncluded('form_field')) {
        if (!this.customField) {
          return false;
        }
      }
      if (this.optionIncluded('form_field')) {
        if (!this.selectedCustomForm) {
          return false;
        }
      }
      if (this.targets.includes("contributors") && 
          (this.selectedContributors.length === 0 || 
          (this.selectedContributors.length >= 1 && !(this.contributors[0] && this.contributors[0].id)))) {
        this.contributorError = "Please select a user or group";
        return false;
      }
      return true;
    },
    optionIncluded(option) {
      return this.targets && this.targets.includes(option);
    },
    targetIconCss(option) {
      let css = 'genuicon-square-o';
      if (this.optionIncluded(option.value)) {
        css = 'genuicon-check-square-o';
      }
      return css;
    },
    targetCss(option) {
      if (this.optionIncluded(option.value)) {
        return 'font-weight-bold';
      }
      return null;
    },
    contributorIdChanged(contributor) {
      this.contributors = contributor;
      this.contributorError = null;
      this.handleInput();
    },
    showTargetError() {
      this.targetError = document.querySelector(`#send-email--target-${this.id}`).validationMessage;
    },
    showSubjectError() {
      this.subjectError = document.querySelector(`#send-email--subject-${this.id}`).validationMessage;
    },
    showReplacementError() {
      if (this.showSubjectWarning && !this.subject?.includes("[#{ticket_number}]")) {
        this.subjectError = "Please correct the text replacement format.";
        return true;
      }
      return false;
    },
    showBodyError() {
      this.bodyError = document.querySelector(`#send-email--body-${this.id}`).validationMessage;
      if (!this.body || this.body.length === 0) {
        this.bodyError = "Please fill out this field";
      }
    },
    showFieldError() {
      if (this.optionIncluded('form_field') && !this.customField) {
        this.customFieldError = "Please select a People list field";
      }
    },
    showOptionError() {
      if (this.showOptions) {
        this.optionSelectError = document.querySelector(`#send-email--option-${this.id}`).validationMessage;
        if (!this.selectedOption) {
          this.optionSelectError = "Must select an option";
        }
      }
    },
    showTemplateError() {
      if (this.showOptions && this.selectedOption?.value === 2) {
        this.templateSelectError = document.querySelector(`#send-email--option-template-${this.id}`).validationMessage;
        if (!this.selectedTemplate) {
          this.templateSelectError = "Must select a template";
        }
      }
    },
    showRecipientsError() {
      if (this.optionIncluded('specified')) {
        this.recipientsError = document.querySelector(`#send-email--recipients-${this.id}`).validationMessage;
      }
    },
    showErrors() {
      this.showTargetError();
      this.showSubjectError();
      this.showReplacementError();
      this.showOptionError();
      this.showTemplateError();
      this.showFieldError();
      this.showBodyError();
      this.showRecipientsError();
      return !(this.targetError && this.targetError.length > 0) &&
             !(this.recipientsError && this.recipientsError.length > 0) &&
             !(this.subjectError && this.subjectError.length > 0);
    },
    handleRecipients(event) {
      this.recipients = event.target.value.replace(/[\r\n\v]+/g, '');
      let error = "";
      if (this.recipients) {
        const emails = this.recipients.split(',');
        let email;
        for (let idx = 0; idx < emails.length; idx += 1) {
          email = emails[idx].trim();
          if (email.length > 0 && !REGEX_PATTERN.exec(email)) {
            if (!error) {
              error = '';
            }
            error = `${error} Email ${email} is invalid.  `;
          } else if (this.customHelpdeskEmails && this.customHelpdeskEmails.includes(email)) {
            error = `Email ${email} is already used in custom forms. Please select a different email address.`;
          }
        }
        const recipients = document.querySelector(`#send-email--recipients-${this.id}`);
        if (recipients) {
          recipients.setCustomValidity(error);
        }
        this.recipientsError = error;
      }
      this.handleInput();
    },
    handleTarget(target) {
      this.target = target;
      if (this.disabled) {
        return;
      }
      if (!this.target) {
        this.targetError = "Must select a recipient for the email.";
      } else {
        this.targetError = null;
      }
      if (!this.optionIncluded('specified')) {
        this.recipientsError = null;
        this.recipients = null;
      }
      this.handleInput();
    },
    handleSubject(subjectValue) {
      this.subject = subjectValue.target ? subjectValue.target.value : subjectValue;
      const subject = document.querySelector(`#send-email--subject-${this.id}`);
      this.subjectError = !this.template ? subject.validationMessage : '';
      this.handleInput();
    },
    handleOption(value) {
      this.selectedOption = value;
      this.selectedTemplate = null;
      this.showOptionError();
      this.handleInput();
    },
    handleTemplates(value) {
      this.template = value;
      this.showTemplateError();
      this.replaceBody();
      this.handleInput();
    },
    handleBody(body) {
      this.body = body;
      const bodyInput = document.querySelector(`#send-email--body-${this.id}`);
      this.bodyError = !this.template ? bodyInput.validationMessage : '';
      this.handleInput();
    },
    replaceBody() {
      if (this.template && this.showOptions) {
        this.handleSubject(this.template.subjectTitle);
        this.handleBody(this.template.emailBody);
        this.disabledField = true;
        this.compKey = !this.compKey;
      } else {
        this.handleSubject("");
        this.handleBody("");
        this.template = null;
        this.disabledField = false;
        this.selectedTemplate = null;
        this.compKey = !this.compKey;
      }
    },
    fetchTemplates() {
      const url = '/email_templates.json';
      const templateTypes = ['end_users', 'agents', 'surveys'];
      this.templateList = [];

      Promise.all(
        templateTypes.map(templateType =>
          http.get(url, { params: { template_type: templateType } })
        )
      )
      .then(responses => {
        responses.forEach((res) => {
          this.templateList.push(...res.data.emailTemplates);
        });
      })
      .catch(error => {
        this.emitError(`Sorry, there was an error fetching templates. ${error.response.data.message}`);
      });
    },
    handleInput() {
      let {recipients} = this;
      let value = null;
      if (recipients) {
        recipients = this.optionIncluded('specified') ? recipients.replace(/[\s]+/g, '') : null;
      }
      if (!this.optionIncluded('contributors')) {
        this.contributors = [];
        this.contributorError = null;
      }
      value = {
        target: this.target,
        customField: this.customField,
        selectedCustomForms: this.selectedCustomForm,
        recipients,
        subject: this.subject,
        body: this.body,
        option: this.selectedOption,
        template: this.template,
        contributors: this.selectedContributors.length > 0 ? this.contributors : [],
        selectedCheckboxes: this.checkboxValues,
      };
      this.$emit('input', { value, index: this.id });
    },
    fetchCustomEmails() {
      http
        .get('/help_desk_custom_emails.json')
        .then(res => {
          if (res.data.emails) {
            this.customHelpdeskEmails = res.data.emails;
          }
        })
        .catch(() => {
          this.emitError('There was an issue fetching your helpdesk emails. Please refresh the page and try again.');
        });
    },
    isMsp() {
      return document.location.pathname.startsWith("/related_companies");
    },
  },
};
</script>

<style lang="scss" scoped>

  .heading{
    color: $primary;
  }

  .item-name {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 17.125rem;
    display: inline-block;
    width: 100%;
  }

  .avatar-name {
    color: $themed-base;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-word;

    .multiselect__option--highlight & {
      color: white;
    }
  }

  .multiselect {
    :deep(.multiselect__single) {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 26rem;
    }
  }

  .asset-info-settings {
    font-size: 0.9375rem;
    margin-bottom: 1rem;
  }

  .info-label {
    font-weight: 500;
    margin-bottom: 0.75rem;
  }

  .checkbox-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem 1.5rem;
  }

  .checkbox-option {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    gap: 0.5rem;
  }

  .checkbox-option input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    cursor: pointer;
  }
</style>
