<template>
  <tr
    class="my-0 position-relative task-item"
    :class="isWrite ? 'box--with-hover' : 'disable-cursor'"
  >
    <td
      v-if="isWrite"
      class="col-sm-1 text-left pl-0"
      @click.stop.prevent="editTask"
    >
      <span
        v-tooltip="{
          content: 'Drag task to change evaluation order',
          placement: 'top-start',
          delay: { show: 300 }
        }"
        class="handle genuicon-draggable pl-0 pr-1 py-3"
      />
      <span class="text-muted small align-text-bottom mr-3">
        <span class="not-as-small font-weight-light">#</span>{{ taskNumber }}
      </span>
    </td>
    <td
      class="col-2 text-left font-weight-bold"
      @click.stop.prevent="editTask"
    >
      <span
        v-if="taskName"
        class="ml-1 break-word"
      >
        {{ taskName }}
      </span>
    </td>
    <td
      class="col position-relative"
      @click.stop.prevent="editTask"
    >
      <div class="ml-1">
        <div class="row col-12 align-items-end">
          <event-clause
            v-if="task.events"
            :node="task.events[0]"
            :link="false"
          />
          <span
            v-if="task.events.length > 1"
            v-tooltip="`${task.events.length - 1} more trigger(s)`"
            class="small text-muted ml-1"
          >
            +{{ task.events.length - 1 }}
          </span>
        </div>
        <span
          v-if="task.companyMailer"
          v-tooltip="`This is a default task and can be partially customized`"
          class="p-1 text-muted"
        >
          <i class="nulodgicon-locked" />
        </span>
      </div>
    </td>
    <td
      class="col-sm-3 col-md-2"
      @click.stop.prevent="editTask"
    >
      <span
        class="text-align-middle"
      >
        {{ actionDescription }}.
      </span>
      <span
        v-if="task.actions.length > 1"
        v-tooltip="`${task.actions.length - 1} more action(s)`"
        class="small text-muted"
      >
        +{{ task.actions.length - 1 }}
      </span>
    </td>
    <td
      class="col-sm-1 col-md-1"
      @click.stop.prevent="editTask"
    >
      <span class="pl-5"> {{ taskOrder }} </span>
    </td>
    <td
      class="col-sm-1 col-md-1"
      @click.stop.prevent="editTask"
    >
      <span class="pl-5"> {{ taskUsed || 0 }} </span>
    </td>
    <td
      v-if="isWrite"
      class="col-sm-2 text-right pr-3 disable-cursor"
    >
      <material-toggle
        ref="disableToggle"
        class="ml-3 toggle-switch float-right mt-1"
        :init-active="!disabled"
        @toggle-sample="updateDisable"
      />
      <a
        v-if="!isDefaultTask"
        href="#"
        class="ml-3 float-right edit-icon"
        @click.stop.prevent="editTask"
      >
        <i class="nulodgicon-edit" />
      </a>
      <a
        v-if="!isDefaultTask"
        href="#"
        class="ml-3 float-right trash-icon"
        @click.stop.prevent="openModal"
      >
        <i class="nulodgicon-trash-b" />
      </a>
      <a
        v-else
        v-tooltip="`View task`"
        href="#"
        class="ml-3 float-right edit-icon"
        @click.stop.prevent="editTask"
      >
        <i class="nulodgicon-eye" />
      </a>
    </td>
    <delete-modal
      ref="deleteModal"
      :value="task"
    />
    <edit-task-modal
      v-if="isModalOpen"
      ref="editTaskModal"
      :value="task"
      :task-groups="taskGroups"
      @fetch-task="fetchTask"
      @close-model="closeEditModal"
    />
  </tr>
</template>

<script>
import _get from 'lodash/get';
import common from 'mixins/automated_tasks/common';
import permissionsHelper from 'mixins/permissions_helper';
import DeleteModal from './delete_modal.vue';
import EditTaskModal from './edit.vue';
import eventClause from './event_clause.vue';
import MaterialToggle from '../shared/material_toggle.vue';

export default {
  components: {
    DeleteModal,
    eventClause,
    MaterialToggle,
    EditTaskModal,
  },
  mixins: [
    common,
    permissionsHelper,
  ],
  props: ['task', 'selected', 'taskGroups'],
  data() {
    return {
      isModalOpen: false,
    };
  },
  computed: {
    isDefaultTask() {
      return !!this.task.companyMailer;
    },
    disabled() {
      return !!this.task.disabledAt;
    },
    actionName() {
      return _get(this, 'actionType.name');
    },
    actionDescription() {
      return `${this.removeBraces(this.actionName)}`;
    },
    actionType() {
      return _get(this, 'task.actions[0].nodeType');
    },
    eventClass() {
      return _get(this, 'task.events[0].nodeType.eventClass');
    },
    taskNumber() {
      return _get(this, 'task.serialNumber');
    },
    taskOrder() {
      return _get(this, 'task.order');
    },
    taskName() {
      return _get(this, 'task.name');
    },
    taskUsed() {
      return _get(this, 'task.triggerCount');
    },
  },
  methods: {
    editTask() {
      if (this.isWrite) {
        this.isModalOpen = true;
        this.$nextTick(() => {
          this.$refs.editTaskModal.open();
        });
      }
    },

    updateDisable() {
      this.$store.dispatch("updateDisable", this.task)
        .then(res => {
          this.$emit('update', res.data);
        });
    },
    openModal() {
      this.$refs.deleteModal.open();
    },
    fetchTask() {
      this.$emit('fetch-tasks');
    },
    closeEditModal(){
      this.isModalOpen = false;
    },
  },
};
</script>

<style lang="scss" scoped>
a.edit-icon {
  color: $themed-fair;
  &:hover i {
    color: $themed-link;
  }
  i {
    font-size: 1.15rem;
  }
}

a.trash-icon {
  color: $themed-fair;
  &:hover i {
    color: $danger;
  }
  i {
    font-size: 1.15rem;
  }
}

.task-item {
  &:hover {
    background-color: $themed-lighter;

    .handle {
      opacity: 1;
      visibility: visible;
    }
  }
}

.selected {
  border: 1px solid $themed-link;
}

.handle {
  cursor: move;
  font-size: 1.125rem;
  opacity: 0;
  visibility: hidden;
}

.disable-cursor {
  cursor: auto;
}
</style>
