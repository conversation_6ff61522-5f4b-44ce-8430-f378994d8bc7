<template>
  <div>
    <task-templates-modal
      v-if="isHelpdesk && !openLinkedTask"
      ref="newTaskSetupModal"
      :task-templates="taskTemplates"
      @selected-template="handleTemplateSelection"
      @submit-template-selection="createOrCloseTask"
      @reset-data="resetData"
    />

    <Teleport to="body">
      <sweet-modal
        ref="newTaskFormModal"
        v-sweet-esc
        modal-theme="dark-header theme-right theme-sticky-footer"
        width="85%"
        :blocking="true"
        @close="handleClose"
      >
        <template slot="title">
          <div class="d-flex align-items-center h-100">
            <h4
              v-if="!editingTaskName"
              class="mb-0"
            >
              {{ truncate(templateModalTitle, 50) }}
            </h4>
            <div
              v-else
              class="input-group w-50"
            >
              <input
                v-if="task"
                id="name"
                v-model="task.name"
                v-validate="'required|max:50'"
                type="text"
                class="form-control"
                name="name"
                placeholder="e.g., Notify on custom status"
                @keydown.enter="handleTaskNameAction('save')"
                @keydown.esc="handleTaskNameAction('discard')"
              >
            </div>
            <span class="sweet-box-actions position-relative mt-1 t-0 right-0 ml-4 not-as-small">
              <div
                v-if="!editingTaskName"
                v-tooltip="'Edit Task Name'"
                class="sweet-action-close"
                @click="handleTaskNameAction('start')"
              >
                <i class="nulodgicon-edit" />
              </div>
              <div
                v-else
                v-tooltip="'Save Task Name'"
                class="sweet-action-close"
                @click="handleTaskNameAction('save')"
              >
                <i class="genuicon-checkmark" />
              </div>
              <div
                v-if="editingTaskName"
                v-tooltip="'Discard Changes'"
                class="sweet-action-close ml-2"
                @click="handleTaskNameAction('discard')"
              >
                <i class="genuicon-ios-close" />
              </div>
            </span>
          </div>
        </template>

        <template slot="box-action">
          <div
            v-if="isEditTask && !isAssetsModule"
            v-tooltip="'Edit as a copy'"
            class="sweet-action-close mx-2 clone-task-icon"
            @click.stop.prevent="handleClone"
          >
            <i
              class="genuicon-duplicate-contract base-font-size"
            />
          </div>

          <div
            v-if="isEditTask && !task.companyMailer"
            v-tooltip="'Delete task'"
            class="sweet-action-close mx-2 task-icon"
            @click.stop.prevent="openDeleteModal"
          >
            <i
              class="nulodgicon-trash-b not-as-big"
            />
          </div>
          <material-toggle
            v-tooltip="toggleTooltip"
            :init-active="!disabled"
            class="mx-2 d-inline-flex"
            style="position: relative; bottom: 1px;"
            :disabled="!isEditTask"
            @toggle-sample="updateDisable"
          />
        </template>

        <template
          slot="default"
        >
          <task-form
            v-if="task && queryStringLoadComplete"
            :key="key"
            ref="taskForm"
            :value="task"
            :task-group="selectedGroup"
            :task-groups="taskGroups"
            :is-new-task="isNewTask"
            class="w-100"
            @input="saveTask"
            @cancel="handleCancel"
          />
        </template>
      </sweet-modal>
    </Teleport>

    <Teleport to="body">
      <delete-modal
        ref="deleteModal"
        :value="task"
      />
    </Teleport>
  </div>
</template>

<script>
import http from 'common/http';
import strings from 'mixins/string';
import { mapGetters, mapMutations, mapActions } from 'vuex';
import inflections from "mixins/inflections";
import { SweetModal } from 'sweet-modal-vue';
import MaterialToggle from 'components/shared/material_toggle.vue';
import common from 'mixins/automated_tasks/common';
import taskNameMixin from 'mixins/automated_tasks/task_name';
import suggestionObjects from 'mixins/automated_tasks/suggestion_objects';
import permissionsHelper from 'mixins/permissions_helper';
import TaskTemplatesModal from './task_templates_modal.vue';
import TaskForm from './form_modal.vue';
import DeleteModal from './delete_modal.vue';

export default {
  components: {
    TaskForm,
    SweetModal,
    MaterialToggle,
    TaskTemplatesModal,
    DeleteModal,
  },
  mixins: [
    suggestionObjects,
    inflections,
    permissionsHelper,
    strings,
    common,
    taskNameMixin,
  ],
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    selectedTemplate: {
      type: Number,
      required: false,
      default: 0,
    },
    selectedGroup: {
      type: Object,
      default: () => {},
      required: false,
    },
    taskGroups: {
      type: Array,
      required: false,
      default: () => [],
    },
    isEditTask: {
      type: Boolean,
      default: false,
    },
    openLinkedTask: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      module: null,
      task: {
        name: '',
        events: [{ nodes: [] }],
        actions: [{}],
      },
      idx: 0,
      queryStringLoadComplete: false,
      selectedTaskGroup: this.selectedGroup,
      taskTemplates: [
        {
          name: 'Blank Task',
          iconClass: "nulodgicon-plus-round bg-fair text-secondary",
          description: 'Create your own task without a starting template.',
          events: [{ "nodeType": { }, "nodes": [] }],
          actions: [{ "nodeType": {  }, "value": { } }],
        },
      ],
      isCanceled: true,
      compKey: false,
      isNewTask: false,
      isModalOpen: false,
      oldTaskName: this.value?.name,
      key : 0,
    };
  },
  computed: {
    ...mapGetters(['moduleFilter', 'currentModule']),
    templateModalTitle() {
      return this.task?.name ? this.task.name : "Add the Task Name";
    },
    isExistingTask() {
      return this.task?.id;
    },
    disabled() {
      return !!this.task.disabledAt;
    },
    isHelpdesk() {
      return this.$router.options.base === '/help_tickets';
    },
    forceDisabled() {
      return this.task.forceDisabled;
    },
    toggleTooltip() {
      if (this.isEditTask) {
        return this.disabled ? "Activate this task" : "Deactivate this task";
      }
      return 'This task will be automatically activated';
    },
    isAssetsModule() {
      return window.location.href.includes('managed_assets');
    },
  },
  watch: {
    selectedTemplate(newVal) {
      this.selectedTaskTemplate = newVal;
    },
    selectedGroup(newVal) {
      this.selectedTaskGroup = newVal;
    },
    value(newVal) {
      this.task = newVal;
    },
    openLinkedTask(newVal) {
      if (newVal && !this.task.id) {
        this.$nextTick(() => {
          if (!this.taskTemplates.length) {
            this.fetchTaskTemplates();
          }
          const task = this.taskTemplates.find(t => t.id === this.selectedTemplate);
          this.task = task || this.taskTemplates[0];
          this.isNewTask = true;
          this.isModalOpen = true;
          this.$refs.newTaskFormModal.open();
          this.$nextTick(() => {
            this.$refs.taskForm.initializeData();
          });
        });
      }
    },
  },
  methods: {
    ...mapMutations(['setAutomatedTasks']),
    ...mapActions(['fetchAutomatedTasks']),
    onWorkspaceChange() {
      if (this.value) {
        this.task = this.value;
      }
      this.fetchTaskTemplates();
      if (this.$route.query.moduleName && this.$route.query.suggestionName) {
        const {suggestionName} = this.$route.query;
        this.suggestionObject(this.convertSnakeAndHyphentoCamelCase(suggestionName)).then((res) => {
          this.task = res.data.suggestion;
          this.queryStringLoadComplete = true;
        });
      } else {
        this.queryStringLoadComplete = true;
      }
    },
    fetchTaskTemplates() {
      http
        .get('/task_templates')
        .then((response) => {
          const sortedTemplates = response.data.sort((a, b) => a.order - b.order);

          this.taskTemplates.push(
            ...sortedTemplates
          );
        })
        .catch(() => {
          this.emitError('Sorry, there was an error fetching templates.');
        });
    },
    handleTemplateSelection(task) {
      this.task = task;
    },
    generateUniqueNewNameFromTemplate() {
      // TODO: make sure this is names to ensure unique.  Probably this will be done properly after clicking 'Create Task' from the backend actually making the task
      return this.task.template?.name || 'New Task Name';
    },
    saveTask(task) {
      this.isEditTask ? this.updateTask(task) : this.createTask(task);
    },
    createTask(task) {
      const url = this.isAssetsModule ? '/asset_automation_tasks.json' : '/automated_tasks.json';
      http
        .post(url, { task })
        .then(() => {
          this.emitSuccess("Task saved successfully");
          this.closeModal();
          this.resetData();
          this.$emit('update');
          this.key += 1;
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error saving task. ${error.response.data.message}`);
        });
    },
    updateTask(task) {
      const url = this.isAssetsModule ? `/asset_automation_tasks/${task.id}.json` : `/automated_tasks/${task.id}.json`;
      http
        .put(url, { task } )
        .then(() => {
          this.emitSuccess("Task saved successfully");
          this.$emit('fetch-task');
          this.$emit('close-model');
          this.closeModal();
          this.resetData();
          this.key += 1;
        })
        .catch(error => {
          this.emitError(`Sorry, there was an error saving task. ${error.response.data.message}`);
        });
    },
    createOrCloseTask() {
      this.isCanceled = false;
      this.key += 1;
      this.closeNewTaskSetupModal();
      this.$nextTick(() => {
        this.task.taskGroup = this.selectedTaskGroup?.name || "Ungrouped";
        this.isNewTask = true;
        this.isModalOpen = true;
        this.$refs.newTaskFormModal.open();
      });
      this.isCanceled = true;
    },
    closeNewTaskSetupModal() {
      this.$refs.newTaskSetupModal.close();
    },
    handleCancel() {
      this.$emit('cancel');
      if (!(this.task?.id && this.$route.query.new)) {
        this.resetData();
      }
      this.close();
      this.queryStringLoadComplete = false;
      this.$nextTick(() => {
        this.resetTaskState();
        this.queryStringLoadComplete = true;
      });
    },
    closeTaskSetupModal() {
      if (this.isCanceled) {
        this.resetData();
        if (this.$route.query.new) {
          this.$router.push('/automated_tasks');
        }
      }
    },
    handleClose() {
      this.$emit('cancel');
      if (!(this.task?.id && this.$route.query.new)) {
        this.resetData();
      }
      if (this.$route.path !== "/automated_tasks" || this.$route.query.new === 'true' || this.$route.query.edit === 'true') {
        this.$router.push(`/automated_tasks`);
      }
      this.closeModal();
      this.resetTaskState();
    },
    resetData() {
      this.task = {
        name: null,
        template: null,
        events: [{ nodes: [] }],
        actions: [{ }],
      };
      this.selectedTaskTemplate = 0;
    },
    open(isEditingTask = false) {
      if (!this.isHelpdesk || !isEditingTask || this.openLinkedTask) {
        this.$nextTick(() => {
          this.isNewTask = false;
          this.isModalOpen = true;
          if (!isEditingTask && this.$route.query.new) {
            this.task = { name: '', template: null, events: [{ nodes: [] }], actions: [{ }] };
          }
          this.$refs.newTaskFormModal.open();
        });
      } else {
        this.selectedTaskTemplate = 0;
        this.$refs.newTaskSetupModal.open();
      }
    },
    closeModal() {
      if (this.isModalOpen && this.$refs.newTaskFormModal.visible) {
        this.isModalOpen = false;
        this.$refs.newTaskFormModal.close();
      }
    },
    close() {
      this.$refs.newTaskFormModal.close();
    },
    handleDelete() {
      this.$refs.taskForm.handleDelete();
    },
    handleClone() {
      http
        .post(`/cloned_tasks.json`, { id: this.task.id } )
        .then(() => {
          this.emitSuccess("Task successfully cloned");
          this.loadTasks();
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error cloning that task.`);
        });
    },
    openDeleteModal() {
      this.$nextTick(() => {
        this.$refs.deleteModal.open();
      });
    },
    loadTasks() {
      this.fetchAutomatedTasks()
        .then(res => {
          this.setAutomatedTasks(res.data);
        })
        .catch(() => {
          this.emitError("Sorry, but there was an error fetching the automated tasks.");
        });
    },
    updateDisable() {
      if (this.forceDisabled) {
        this.emitError("Please update task action(s) to enable this task");
      } else {
        this.$store.dispatch("updateDisable", this.task)
          .then(() => {
            this.loadTasks();
          });
      }
    },
    resetTaskState() {
      this.task.name = this.oldTaskName;
      this.editingTaskName = false;
      this.key += 1;
    },
  },
};
</script>

<style lang="scss" scoped>
  .nulodgicon-plus-round:before {
    color: $themed-fair;
  }
  .selected_temp {
    body[data-theme="dark"] & .box__inner h6 {
      color: #fff !important;
    }

    body[data-theme="dark"] & .box__inner p {
      color: #242323 !important;
    }

    .box__inner h6 {
      color: #333 !important;
    }

    .box__inner p {
      color: #242323 !important;
    }
  }
  :deep(.sweet-modal) {
    .sweet-box-actions {
      display: flex;
    }
  }
</style>
