<template>
  <div
    class="w-100"
    :class="{'force-disabled': forceDisabled || isMSTeamsRecipient}"
    :data-tc-task-modal="name"
    @mouseenter="hover = true"
    @mouseleave="hover = false"
  >
    <a
      v-if="task"
      class="box p-3"
      :class="{'box--with-hover': isWrite, 'disable-cursor': !isWrite}"
      @click.stop.prevent="openEditTaskModal"
    >
      <div
        v-if="forceDisabled || isMSTeamsRecipient"
        v-tooltip="tooltipMessage"
        class="disabled-badge text-secondary"
      >
        Outdated
      </div>
      <div
        v-tooltip="{
          content: 'Drag task to change evaluation order',
          placement: 'top-start',
          delay: { show: 300 }
        }"
        class="handle drag-overlay"
      >
        <span class="handle-dots genuicon-draggable"/>
      </div>
      <div class="box__inner align-self-start d-flex flex-column h-100">
        <div class="text-secondary text-wrap pb-2 mb-5">
          <div class="not-so-small mb-2">
            <strong
              v-tooltip="hover ? hoverName : toolTipText"
              :data-tc-automated="name"
            >
              <template v-if="hover">{{ isHover }}</template>
              <template v-else>{{ taskName }}</template>
            </strong>
          </div>
          <p class="smallest text-muted mb-0">
            TRIGGER
            <span
              v-if="task.events.length > 1"
              v-tooltip="`${task.events.length - 1} more trigger(s)`"
              class="text-muted ml-1"
            >
              +{{ task.events.length - 1 }}
            </span>
          </p>
          <div
            v-if="task.events"
            class="text-dark"
          >
            <event-clause
              :node="task.events[0]"
              :link="false"
            />
          </div>
          <p class="smallest text-muted mb-0 mt-3">
            ACTION
            <span
              v-if="task.actions.length > 1"
              v-tooltip="`${task.actions.length - 1} more action(s)`"
              class="text-muted ml-1"
            >
              +{{ task.actions.length - 1 }}
            </span>
          </p>
          <span
            class="small action-description text-dark"
            data-tc-event-action
          >
            {{ actionDescription }}
            <span v-if="isChannelNameRequired(0)">
              to channel <b>"{{ channelName(0) }}"</b>
            </span>
          </span>
        </div>
      </div>

      <div class="bottom-section py-2 px-3">
        <div class="mt-auto">
          <div class="d-inline-block">
            <span
              class="text-muted small align-self-center mr-2"
            >
              <span class="not-as-small font-weight-light">Task Id # </span>
              {{ taskNumber }}
            </span>
          </div>
          <div class="d-inline-block">
            <span class="text-muted small align-self-center mr-2">
              <span class="not-as-small font-weight-light">Order: </span>
              {{ taskOrder }}
            </span>
          </div>
          <div
            v-if="isHelpdesk"
            class="d-inline-block"
          >
            <span
              class="text-muted small align-self-center mr-2"
            >
              <span class="not-as-small font-weight-light">Used: </span>
              {{ taskUsed || 0 }}
            </span>
          </div>
          <div
            v-if="isWrite"
            class="d-flex float-right"
            :data-tc-icons="name"
          >
            <a
              v-if="!isDefaultTask"
              v-tooltip="`Remove task`"
              href="#"
              class="mr-1 action-icon d-flex align-items-center justify-content-center bg-lighter"
              data-tc-delete
              :data-tc-delete-icon="taskName"
              @click.stop.prevent="openModal"
            >
              <i class="nulodgicon-trash-b text-muted mt-1" />
            </a>
            <a
              v-if="!isDefaultTask"
              v-tooltip="`Edit task`"
              href="#"
              class="mr-2 action-icon d-flex align-items-center justify-content-center bg-lighter"
              @click.stop.prevent="editTaskHandler"
            >
              <i
                class="nulodgicon-edit text-muted mt-1"
              />
            </a>

            <i
              v-if="isDefaultTask"
              v-tooltip="`This is a default task and can be partially customized`"
              class="nulodgicon-locked text-muted small mr-2"
              :data-tc-default-icon="name"
            />
            <i
              v-if="isCustomFormTask"
              v-tooltip="`This can only be modified from it's custom form`"
              class="nulodgicon-locked text-muted small mr-2"
            />

            <material-toggle
              v-tooltip="'Activate this task'"
              :init-active="!disabled"
              :data-tc-toggle="!disabled"
              @toggle-sample="updateDisable"
            />

          </div>
        </div>

      </div>
    </a>
    <delete-modal
      ref="deleteModal"
      :value="task"
    />
    <edit-task-modal
      v-if="isModalOpen"
      ref="editTaskModal"
      :value="task"
      :task-groups="taskGroups"
      @close-model="closeEditModal"
      @fetch-task="fetchTask"
    />
  </div>
</template>

<script>
import http from 'common/http';
import common from 'mixins/automated_tasks/common';
import recipients from 'mixins/automated_tasks/recipients';
import _get from 'lodash/get';
import permissionsHelper from 'mixins/permissions_helper';
import eventClause from './event_clause.vue';
import DeleteModal from './delete_modal.vue';
import EditTaskModal from './edit.vue';
import MaterialToggle from '../shared/material_toggle.vue';
import string from '../../mixins/string';

export default {
  components: {
    DeleteModal,
    eventClause,
    MaterialToggle,
    EditTaskModal,
  },
  mixins: [
    common,
    recipients,
    permissionsHelper,
    string,
  ],
  props: ['task', 'selected', 'module', 'taskGroups'],
  data() {
    return {
      isModalOpen: false,
      hover: false,
      forceDisabled: _get(this, 'task.forceDisabled', false),
    };
  },
  computed: {
    actionDescription() {
      return `${this.removeBraces(this.actionName)}`;
    },
    tooltipMessage() {
      let tooltip = '';
      if (this.forceDisabled) {
        tooltip += 'A user/group linked to one or more task actions no longer exists. Please update these actions to enable this automated task.';
      }
      if (this.isMSTeamsRecipient) {
        tooltip += ' Due to the Microsoft Teams limitations, email updates to Teams channels may not work properly.';
      }
      return tooltip;
    },
    isDefaultTask() {
      return !!this.task.companyMailer;
    },
    isCustomFormTask() {
      return !!this.task.customFormId;
    },
    name() {
      return this.task.name;
    },
    taskCss() {
      if (this.disabled) {
        return "text-muted";
      } 
        return null;
      
    },
    disabled() {
      return !!this.task.disabledAt;
    },
    actionType() {
      return _get(this, 'task.actions[0].nodeType');
    },
    actionName() {
      return _get(this, 'actionType.name');
    },
    taskNumber() {
      return _get(this, 'task.serialNumber');
    },
    taskOrder() {
      return _get(this, 'task.order');
    },
    taskUsed() {
      return _get(this, 'task.triggerCount');
    },
    isNameLengthGood() {
      return this.name && this.name.length < 35;
    },
    isLengthGoodForHover() {
      return this.name && this.name.length < 30;
    },
    taskName() {
      if (this.isNameLengthGood) {
        return this.name;
      }
      return this.truncate(this.name, 36);
    },
    toolTipText() {
      if (this.isNameLengthGood) {
        return '';
      }
      return this.name;
    },
    isHover() {
      if (this.isLengthGoodForHover) {
        this.turnOffGoodHover();
        return this.name;
      }
      return this.truncate(this.name, 31);
    },
    hoverName() {
      if (this.isLengthGoodForHover) {
        return '';
      }
      return this.name;
    },
    isHelpdesk() {
      return this.$router.options.base === '/help_tickets';
    },
  },
  methods: {
    cloneTask() {
      http
        .post(`/cloned_tasks.json`, { id: this.task.id } )
        .then(res => {
          this.emitSuccess("Task successfully cloned");
          this.$emit('fetch-tasks', res.data.id);
        })
        .catch(() => {
          this.emitError(`Sorry, there was an error cloning this task.`);
        });
    },
    openEditTaskModal() {
      this.fetchTask();
      if (this.isWrite) {
        this.isModalOpen = true;
        this.$nextTick(() => {
          this.$refs.editTaskModal.open();
        });
      }
    },
    editTaskHandler() {
      if (this.isWrite) {
        if (this.task.customFormId) {
          const routeData = this.$router.resolve({path: `/help_tickets/settings/custom_forms/${this.task.customFormId}/edit`});
          return window.open(routeData.href, '_blank');
        }
        this.openEditTaskModal();
      }
      return null;
    },
    updateDisable() {
      if (this.forceDisabled) {
        this.emitError("Please update task action(s) to enable this task");
      } else {
        this.$store.dispatch("updateDisable", this.task).then(res => {
          this.$emit('update', res.data);
        });
      }
    },
    openModal() {
      this.$refs.deleteModal.open();
    },
    turnOffGoodHover() {
      this.isGoodHover = false;
    },
    fetchTask() {
      this.$emit('fetch-tasks');
    },
    closeEditModal(){
      this.isModalOpen = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.box--with-hover {
  padding: 1.25rem;

  .bottom-section {
    padding-left: inherit;
    transition: padding-left 0.18s linear; // have to manually set 0.18s transition since .bottom-section is absolute positioned
  }

  &:hover {
    padding-left: 35px !important;

    .bottom-section {
      padding-left: 35px !important;
    }

    .drag-overlay {
      opacity: 1;
      transition: 0.5s;
      visibility: visible;
    }
  }
}

a.action-icon {
  i {
    font-size: 0.925rem;
    color: $themed-fair;
  }
  &:hover {
    i.nulodgicon-edit {
      color: $themed-link;
    }
    i.nulodgicon-trash-b {
      color: $danger;
    }
    i.nulodgicon-eye {
      color: $themed-link;
    }
  }
}
.action-icon {
  height: 24px;
  width: 24px;
}

.handle-dots {
  font-size: 1.125rem;
  position: absolute;
  top: 36%;
}

.drag-overlay {
  background-color: rgba(33, 37, 41, 0.1);
  border-radius: 0.25rem 0 0 0.25rem;
  color: $themed-base;
  content: "";
  cursor: move;
  height: 100%;
  left: 0;
  opacity: 0;
  position: absolute;
  top: 0;
  visibility: hidden;
  width: 1.25rem;
  z-index: 2;

  &:hover {
    background-color: rgba(33, 37, 41, 0.3);
  }
}

@media only screen and (max-width: 900px) {
  .task-col {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.disable-cursor {
  cursor: auto;
}

.bottom-section {
  background: $themed-light;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 0;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}

.blinker {
  animation: background-color 1.4s ease-out;
}

@keyframes background-color {
  from {  background-color: #F5F5DC; }
  to { background-color: white; }
}

.action-description {
  background: $themed-task-action;
  padding: 2px 5px;
  border-radius: 4px;
}

.force-disabled {
  border: 0.32rem solid $color-caution;
  border-width: 0.19rem;
  border-radius: 0.7rem;
}

.disabled-badge {
  background-color: $color-caution;
  border-radius: 0 0 0 0.5rem;
  top: 0;
  right: 0;
  padding: 0 0.5rem;
  position: absolute;
  font-size: 0.75rem;
}
</style>
