import { mspRoutingMiddleware } from 'mixins/msp_helper';
import HelpTicketList from '../components/help_tickets/help_ticket_list.vue';
import ActivityList from '../components/help_tickets/activity_list.vue';
import HelpTicketForm from '../components/shared/custom_forms/form.vue';
import EmptyState from '../components/help_tickets/empty_state.vue';
import Report from '../components/help_tickets/report/report.vue';
import WorkspaceList from '../components/shared/workspaces/index.vue';
import Incoming from '../components/help_tickets/incoming/index.vue';
import Responses from '../components/help_tickets/canned_responses/responses.vue';
import Documents from '../components/library_documents/documents.vue';
import NewResponse from '../components/help_tickets/canned_responses/new.vue';
import EditResponse from '../components/help_tickets/canned_responses/edit.vue';
import ShowResponse from '../components/help_tickets/canned_responses/show.vue';
import Tasks from '../components/help_tickets/canned_tasks/tasks.vue';
import NewTaskModal from '../components/help_tickets/canned_tasks/new_task_modal.vue';
import EditTaskModal from '../components/help_tickets/canned_tasks/edit_task_modal.vue';
import Checklists from '../components/help_tickets/task_checklists/checklists.vue';
import ScheduledTasks from '../components/help_tickets/scheduled_tasks/scheduled_tasks.vue';
import NewAndEditScheduledTasks from '../components/help_tickets/scheduled_tasks/scheduled_task_form.vue';
import NewTaskChecklist from '../components/help_tickets/task_checklists/new.vue';
import EditTaskChecklist from '../components/help_tickets/task_checklists/edit.vue';
import Blocked from '../components/help_tickets/settings/blocked.vue';
import BlockedKeywords from '../components/help_tickets/settings/blocked_keywords.vue';
import ClosingSurvey from '../components/help_tickets/closing_surveys/show.vue';
import EmailNotifications from '../components/help_tickets/settings/email_notifications.vue';
import EmailFormatPreview from '../components/help_tickets/settings/email_format.vue';
import GeneralEmail from '../components/help_tickets/settings/email_setting.vue';
import EmailTemplates from '../components/help_tickets/settings/email_templates.vue';
import Surveys from '../components/help_tickets/surveys/index.vue';
import ValidEmailExtensions from '../components/help_tickets/settings/valid_email_extensions.vue';
import Connectors from '../components/help_tickets/settings/connectors.vue';
import MobileAppStore from '../components/help_tickets/settings/mobile_app_store.vue';
import DesktopAppDownload from '../components/help_tickets/settings/desktop_app_download.vue';
import SlackConnectors from '../components/help_tickets/settings/slack_connectors.vue';
import MsTeamsConnectors from '../components/help_tickets/settings/ms_teams_connectors.vue';
import HelpCenterCustomDomain from '../components/help_tickets/settings/help_center/help_center_custom_domain.vue';
import General from '../components/help_tickets/settings/general.vue';
import HelpCenter from '../components/help_tickets/settings/help_center/help_center.vue';
import HelpCenterDisplay from '../components/help_tickets/settings/help_center/help_center_display.vue';
import Faqs from '../components/help_tickets/faqs/index.vue';
import FaqShow from '../components/help_tickets/faqs/show.vue';
import FaqEdit from '../components/help_tickets/faqs/edit.vue';
import FaqNew from '../components/help_tickets/faqs/new.vue';
import HelpTicketShow from '../components/help_tickets/show/general/show_page.vue';
import CustomFormsBuilder from '../components/shared/custom_forms/custom_form_builder.vue';
import CustomFormsIndex from '../components/shared/custom_forms/index.vue';
import AccessDenied from '../components/help_tickets/access_denied.vue';
import { savePreviousScroll, scrollBehavior } from './mixins/scroll_behavior_mixin';
import { trackRouteChange } from './mixins/route_tracking_mixin';
import sanitizeRoute from './mixins/sanitize_route_mixin';
import ImportHelpTickets from '../components/help_tickets/import_help_tickets.vue';
import ListViewColumns from '../components/help_tickets/list_view_columns.vue';
import EventLogs from '../components/help_tickets/settings/event_logs.vue';
import Reports from '../components/help_tickets/reports/index.vue';
import ArticleIndex from '../components/knowledge_base/index.vue';
import ArticleShow from '../components/knowledge_base/show.vue';
import NewArticle from '../components/knowledge_base/new.vue';
import SlaPolicyList from '../components/help_tickets/settings/sla/sla_policy_list.vue';
import BusinessHours from '../components/company/business_hours.vue';
import HelpdeskCategories from '../components/shared/customize_categories.vue';
import SlaPolicyNewAndEdit from '../components/help_tickets/settings/sla/new.vue';
import AutomatedTasks from '../components/automated_tasks/index.vue';
import AutomatedTaskNew from '../components/automated_tasks/new.vue';
import WorkspaceModal from '../components/shared/workspaces/workspace_modal.vue';
import Form from '../components/help_center/form.vue';
import EndUserDashboard from '../components/help_tickets/end_user_dashboard.vue';
import EndUserFaqs from '../components/help_center/faqs.vue';
import EndUserArticles from '../components/help_center/knowledge_base.vue';
import EndUserTicketsView from '../components/help_tickets/end_user_tickets_view.vue';
import People from '../components/help_tickets/people/index.vue';

const router = new VueRouter({
  routes: [
    {
      name: 'help-tickets-all',
      path: '/all',
      component: HelpTicketList,
    },
    {
      name: 'access-denied',
      path: '/denied',
      component: AccessDenied,
    },
    {
      name: 'ticket-dashboard',
      path: '/dashboard',
      component: Report,
    },
    {
      name: 'form',
      path: '/workspaces/:workspace_id/forms/:id',
      component: Form,
    },
    {
      name: 'end-user-tickets',
      path: '/end_user_tickets',
      component: EndUserTicketsView,
    },
    {
      name: 'end-user-dashboard',
      path: '/workspaces/:workspace_id',
      component: EndUserDashboard,
    },
    {
      name: 'end-user-dashboard',
      path: '/end_user_dashboard',
      component: EndUserDashboard,
    },
    {
      path: '/faqs/workspaces/:workspace_id',
      component: EndUserFaqs,
      name: 'end_user_faqs',
    },
    {
      path: '/knowledge/workspaces/:workspace_id',
      component: EndUserArticles,
      name: 'end_user_articles',
    },
    {
      path: '/knowledge/:slug/:workspace_id',
      component: ArticleShow,
      name: 'knowledge_show',
    },
    {
      path: '/knowledge_base/:slug/:workspace_id',
      component: ArticleShow,
      name: 'end_user_knowledge_show',
    },
    {
      name: 'articles',
      path: '/articles',
      component: ArticleIndex,
    },
    {
      name: 'new-article',
      path: '/articles/actions/new',
      component: NewArticle,
    },
    {
      name: 'show-article',
      path: '/articles/:slug',
      component: ArticleShow,
    },
    {
      name: 'update-article',
      path: '/articles/:slug/edit',
      component: NewArticle,
    },
    {
      name: 'all-people',
      path: '/people',
      component: People,
    },
    {
      name: 'all-agents',
      path: '/agents',
      component: People,
    },
    {
      name: 'automated-task-new',
      path: '/automated_tasks/new',
      component: AutomatedTaskNew,
    },
    {
      name: 'automated-tasks',
      path: '/automated_tasks/',
      component: AutomatedTasks,
    },
    {
      name: 'closing-survey',
      path: '/closing_surveys/:id',
      component: ClosingSurvey,
    },
    {
      name: 'new-help-ticket',
      path: '/new',
      component: HelpTicketForm,
    },
    {
      name: 'empty-help-tickets',
      path: '/initial',
      component: EmptyState,
    },
    {
      name: 'help-tickets-incoming',
      path: '/incoming',
      component: Incoming,
    },
    {
      name: 'custom-forms',
      path: '/settings/custom_forms',
      component: CustomFormsIndex,
      props: {
        companyModule: "helpdesk",
      },
    },
    {
      name: 'edit-custom-forms',
      path: '/settings/custom_forms/:id/edit',
      component: CustomFormsBuilder,
      props: {
        pathName: "edit-custom-forms",
      },
    },
    {
      name: 'edit-custom-forms',
      path: '/settings/custom_forms/:id/*/edit',
      component: CustomFormsBuilder,
      props: {
        pathName: "edit-custom-forms",
      },
    },
    {
      name: 'list_view_columns',
      path: '/list_view_columns',
      component: ListViewColumns,
    },
    {
      name: 'new-custom-forms',
      path: '/settings/custom_forms/new',
      component: CustomFormsBuilder,
      props: {
        pathName: "new-custom-forms",
      },
    },
    {
      name: 'email-notifications',
      path: '/settings/email_notifications',
      component: EmailNotifications,
    },
    {
      name: 'email-format',
      path: '/settings/helpdesk_email_format',
      component: EmailFormatPreview,
    },
    {
      name: 'email-setting',
      path: '/settings/email_setting',
      component: GeneralEmail,
    },
    {
      name: 'email-templates',
      path: '/settings/email_templates/end_users',
      component: EmailTemplates,
    },
    {
      name: 'surveys',
      path: '/surveys',
      component: Surveys,
    },
    {
      name: 'valid-email-extensions',
      path: '/settings/valid_email_extensions',
      component: ValidEmailExtensions,
    },
    {
      name: 'email-templates',
      path: '/settings/email_templates/agents',
      component: EmailTemplates,
    },
    {
      name: 'email-templates',
      path: '/settings/email_templates/surveys',
      component: EmailTemplates,
    },
    {
      name: 'general',
      path: '/settings/general',
      component: General,
    },
    {
      name: 'help_center',
      path: '/settings/help_center',
      component: HelpCenter,
    },
    {
      name: 'custom_domain',
      path: '/settings/help_center/custom_domain',
      component: HelpCenterCustomDomain,
    },
    {
      name: 'help_center_display',
      path: '/settings/help_center/display',
      component: HelpCenterDisplay,
    },
    {
      name: 'workspaces',
      path: '/workspaces?new=true',
      component: WorkspaceModal,
    },
    {
      name: 'workspace',
      path: '/workspaces?edit=id',
      component: WorkspaceModal,
    },
    {
      name: 'workspaces',
      path: '/workspaces',
      component: WorkspaceList,
    },
    {
      name: 'connectors',
      path: '/settings/connectors',
      component: Connectors,
    },
    {
      name: 'slack-connectors',
      path: '/settings/connectors/slack',
      component: SlackConnectors,
    },
    {
      name: 'ms-teams-connectors',
      path: '/settings/connectors/ms-teams',
      component: MsTeamsConnectors,
    },
    {
      name: 'mobile-app-store',
      path: '/settings/mobile_app_store',
      component: MobileAppStore,
    },
    {
      name: 'desktop-app',
      path: '/settings/desktop_app',
      component: DesktopAppDownload,
    },
    {
      name: 'help-tickets-blocked-mails',
      path: '/settings/blocked_mails',
      component: Blocked,
    },
    {
      name: 'help-tickets-blocked-keywords',
      path: '/settings/blocked_keywords',
      component: BlockedKeywords,
    },
    {
      name: 'new-faq',
      path: '/faqs/new',
      component: FaqNew,
    },
    {
      name: 'edit-faq',
      path: '/faqs/:id/edit',
      component: FaqEdit,
    },
    {
      name: 'show-faq',
      path: '/faqs/:id',
      component: FaqShow,
    },
    {
      name: 'faqs',
      path: '/faqs',
      component: Faqs,
    },
    {
      name: 'responses-new',
      path: '/responses/new',
      component: NewResponse,
    },
    {
      name: 'responses-edit',
      path: '/responses/:id/edit',
      component: EditResponse,
    },
    {
      name: 'responses-show',
      path: '/responses/:id',
      component: ShowResponse,
    },
    {
      name: 'responses',
      path: '/responses',
      component: Responses,
    },
    {
      name: 'tasks-new',
      path: '/tasks/new',
      component: NewTaskModal,
    },
    {
      name: 'tasks-edit',
      path: '/tasks/:id',
      component: EditTaskModal,
    },
    {
      name: 'tasks',
      path: '/tasks',
      component: Tasks,
    },
    {
      name: 'task-checklists-new',
      path: '/task_checklists/new',
      component: NewTaskChecklist,
    },
    {
      name: 'task-checklists-edit',
      path: '/task_checklists/:id',
      component: EditTaskChecklist,
    },
    {
      name: 'checklists',
      path: '/task_checklists',
      component: Checklists,
    },
    {
      name: 'scheduled-tasks',
      path: '/scheduled_tasks',
      component: ScheduledTasks,
    },
    {
      name: 'scheduled-tasks-new',
      path: '/scheduled_tasks/new',
      component: NewAndEditScheduledTasks,
    },
    {
      name: 'scheduled-tasks-edit',
      path: '/scheduled_tasks/:id',
      component: NewAndEditScheduledTasks,
    },
    {
      name: 'documents',
      path: '/documents',
      component: Documents,
    },
    {
      name: 'activities',
      path: '/ticket_activities',
      component: ActivityList,
    },
    {
      path: '/import_help_tickets',
      component: ImportHelpTickets,
      name: "import",
    },
    {
      name: 'event_log',
      path: '/settings/event_log',
      component: EventLogs,
    },
    {
      name: 'help-ticket-show',
      path: '/:id/general',
      redirect: '/:id',
    },
    {
      name: 'help-ticket-show',
      path: '/:id',
      component: HelpTicketShow,
    },
    {
      name: 'ticket-dashboard',
      path: '/dashboard',
      component: Report,
    },
    {
      name: 'help-tickets',
      path: '/',
      component: HelpTicketList,
    },
    {
      name: 'sla-policy',
      path: '/settings/sla/policies/new',
      component: SlaPolicyNewAndEdit,
    },
    {
      name: 'sla-policy',
      path: '/settings/sla/policies/:id',
      component: SlaPolicyNewAndEdit,
    },
    {
      name: 'sla-policy',
      path: '/settings/sla/policies',
      component: SlaPolicyList,

    },
    {
      name: 'business-hours',
      path: '/settings/business_hour_settings',
      component: BusinessHours,
    },
    {
      name: 'helpdesk-categories',
      path: '/settings/categories',
      component: HelpdeskCategories,
    },
    {
      name: 'reports',
      path: '/reports',
      component: Reports,
      children: [
        {
          name: 'overview',
          path: 'overview',
          component: Reports,
        },
        {
          name: 'analytics',
          path: 'analytics',
          component: Reports,
        },
        {
          name: 'saved',
          path: 'saved',
          component: Reports,
        },
        {
          name: 'scheduled',
          path: 'scheduled',
          component: Reports,
        },
        {
          name: 'show-saved-report',
          path: 'saved/:id',
          component: Reports,
        },
      ],
    },
    {
      name: 'show-report',
      path: '/reports/:id',
      component: Reports,
    },
    {
      name: 'new-report',
      path: '/reports/new',
      component: Reports,
    },
  ],

  scrollBehavior,
  hashbang: false,
  mode: 'history',
  base: '/help_tickets',
});

// Toggle visibility of management view box
router.beforeEach((to, from, next) => mspRoutingMiddleware(to, from, next, 'helpdesk'));

router.beforeEach(savePreviousScroll);
router.beforeEach((to, from, next) => sanitizeRoute(to, from, next));
router.beforeResolve((to, from, next) => trackRouteChange(to, from, next, 'helpdesk'));

export default router;
