import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      isDarkMode: false,
    };
  },
  mounted() {
    const root = document.documentElement;
    const observer = new MutationObserver(() => {
      this.isDarkMode = root.classList.contains('dark-theme');
    });

    observer.observe(root, { attributes: true });
    this.isDarkMode = root.classList.contains('dark-theme');
  },
  computed: {
    ...mapGetters(['quickViewTicketId', 'currentHelpTicketDraft']),

    ticketId() {
      if (this.quickViewTicketId) {
        return this.quickViewTicketId;
      }
      return this.$route.params.id;
    },
    enableEditing() {
      return (
        this.currentHelpTicketDraft &&
        this.currentHelpTicketDraft.companyUserId === this.$currentCompanyUserId
      );
    },
  },
};
