import { mapActions } from 'vuex';
import _cloneDeep from 'lodash/cloneDeep';

export default {
  data() {
    return {
        isDraftResolved: false,
    };
  },
  computed: {
    draftExists() {
        return (
          this.currentHelpTicketDraft &&
          this.currentHelpTicketDraft.companyUserId === this.$currentCompanyUserId &&
          this.currentHelpTicketDraft.fieldsData?.[this.field.id] !== ''
        );
    },
    draftValue() {
      if (this.currentHelpTicketDraft.companyUserId !== this.$currentCompanyUserId) {
          return null;
      }
      if (this.field.id) {
        return this.currentHelpTicketDraft?.fieldsData?.[this.field.id];
      }
      return null;
    },
  },
  watch: {
    'currentHelpTicketDraft': function (newDraft) {
      if (newDraft && newDraft?.fieldsData && newDraft.fieldsData?.[this.field.id]) {
        if (this.field.fieldAttributeType === 'rich_text') {
          this.valueStr = newDraft.fieldsData[this.field.id];
        }
        if (this.field.fieldAttributeType === 'text') {
          this.intialValue = newDraft.fieldsData[this.field.id];
        }
        if (this.field.fieldAttributeType === 'text_area') {
          this.textValue = newDraft.fieldsData[this.field.id];
        }
      }
    },
  },
  created() {
    if (this.draftExists && this.draftValue) {
      if (this.field.fieldAttributeType === 'rich_text') {
        this.valueStr = _cloneDeep(this.draftValue);
        this.isDraftResolved = false;
      }
      if (this.field.fieldAttributeType === 'text') {
        this.intialValue = _cloneDeep(this.draftValue);
      }
      if (this.field.fieldAttributeType === 'text_area') {
        this.textValue = _cloneDeep(this.draftValue);
      }
    }
  },
  methods: {
    ...mapActions([
      'fetchTicketDraft',
    ]),
    onWorkSpaceChange() {
      if (this.currentHelpTicketDraft && this.currentHelpTicketDraft?.fieldsData && this.currentHelpTicketDraft.fieldsData?.[this.field.id]) {
        if (this.field.fieldAttributeType === 'rich_text') {
          this.valueStr = this.currentHelpTicketDraft.fieldsData[this.field.id];
        }
        if (this.field.fieldAttributeType === 'text') {
          this.intialValue = this.currentHelpTicketDraft.fieldsData[this.field.id];
        }
        if (this.field.fieldAttributeType === 'text_area') {
          this.textValue = this.currentHelpTicketDraft.fieldsData[this.field.id];
        }
      }
    },
    handleDraftResolution(actionType) {
      if (actionType === 'discard') {
        if (this.field.fieldAttributeType === 'rich_text') {
          this.valueStr = this.originalValueStr;
          this.isDraftResolved = true;
        } else if (this.field.fieldAttributeType === 'text') {
          this.intialValue = this.originalValue;
        } else if (this.field.fieldAttributeType === 'text_area') {
          this.textValue = this.existingValue;
        }
        const fields = { ...this.currentHelpTicketDraft.fieldsData };
        delete fields[this.field.id];
        this.updateDraftFieldValue(fields);
      }
    },
    updateDraftFieldValue(newFieldsValue) {
      const updatedFieldData = {
        fieldsData: newFieldsValue,
        id: this.currentHelpTicketDraft.id,
        ...(this.currentHelpTicketDraft.helpTicketId || { helpTicketId: this.object.id }),
      };
      this.$store.commit('updateCurrentHelpTicketDraft', updatedFieldData);
    },
  },
};
