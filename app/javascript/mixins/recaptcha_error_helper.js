export default {
  data() {
    return {
      recaptchaRetryCount: 0,
    };
  },
  methods: {
    handleRecaptchaScriptError(error) {
      const isRecaptchaScriptError = error?.message?.includes('Cannot read properties of undefined');
      if (isRecaptchaScriptError && this.recaptchaRetryCount < 3) {
        this.recaptchaRetryCount += 1;
        this.resetCaptcha();
        return true;
      }
      return false;
    },
  },
};
