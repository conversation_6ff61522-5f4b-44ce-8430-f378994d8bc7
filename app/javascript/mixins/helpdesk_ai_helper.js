export default {
  data() {
    return {
      groupedLanguages: [
        {
          filterType: 'Popular Choices',
          filters: [
            { id: 1, name: "Filipino" },
            { id: 2, name: "French" },
            { id: 3, name: "German" },
            { id: 4, name: "Hindi" },
            { id: 5, name: "Italian" },
            { id: 6, name: "Mandarin" },
            { id: 7, name: "Polish" },
            { id: 8, name: "Russian" },
            { id: 9, name: "Spanish" },
            { id: 10, name: "Ukranian" },
          ],
        },
        {
          filterType: 'Additional Options',
          filters: [
            { id: 11, name: "Arabic" },
            { id: 12, name: "Bengali" },
            { id: 13, name: "Bulgarian" },
            { id: 14, name: "Catalan" },
            { id: 15, name: "Chinese" },
            { id: 16, name: "Croatian" },
            { id: 17, name: "Czech" },
            { id: 18, name: "Danish" },
            { id: 19, name: "Dutch" },
            { id: 20, name: "Estonian" },
            { id: 21, name: "Finnish" },
            { id: 22, name: "Greek" },
            { id: 23, name: "Hebrew" },
            { id: 24, name: "Hungarian" },
            { id: 25, name: "Indonesian" },
            { id: 26, name: "Japanese" },
            { id: 27, name: "Kannada" },
            { id: 28, name: "Korean" },
            { id: 29, name: "Latvian" },
            { id: 30, name: "Lithuanian" },
            { id: 31, name: "Malay" },
            { id: 32, name: "Malayalam" },
            { id: 33, name: "Marathi" },
            { id: 34, name: "Norwegian" },
            { id: 35, name: "Persian (Farsi)" },
            { id: 36, name: "Portuguese" },
            { id: 37, name: "Romanian" },
            { id: 38, name: "Serbian" },
            { id: 39, name: "Slovak" },
            { id: 40, name: "Slovenian" },
            { id: 41, name: "Swahili" },
            { id: 42, name: "Swedish" },
            { id: 43, name: "Tamil" },
            { id: 44, name: "Telugu" },
            { id: 45, name: "Thai" },
            { id: 46, name: "Turkish" },
            { id: 47, name: "Urdu" },
            { id: 48, name: "Vietnamese" },
          ],
        },
      ],
    };
  },
  computed: {
    isCommentBodyEmpty() {
      if (!this.haveCommentText(this.editableComment.commentBody)) {
        return true;
      }
      return false;
    },
  },
  methods: {
    haveCommentText(str) {
      if (!str) return false;

      const parser = new DOMParser();
      const doc = parser.parseFromString(str, 'text/html');
      const text = doc.body.innerText || '';
      return text.trim().length;
    },
  },
};
