module EmailResponseHelper
  def pluck_only_last_response(comment_body)
    valid_tags = ['p', 'div']
    current_tag = comment_body.at(valid_tags.join(', '))
    selected_elements = Nokogiri::HTML::DocumentFragment.parse('')
    allowed_tag_names = ['p', 'div', 'br', 'text']

    while current_tag &&
      (current_tag.text.include?('CAUTION: This email originated from outside your organization') ||
       !valid_tags.include?(current_tag.name))
      current_tag = current_tag.next_sibling
    end

    while current_tag && allowed_tag_names.include?(current_tag.name)
      selected_elements << current_tag.to_html
      current_tag = current_tag.next_sibling
    end

    Nokogiri::HTML(selected_elements.to_html)
  end

  def remove_replies(doc, limit = -1)
    delete_following = false
    replies_count = 0
    doc.css("div").each_with_index do |div, idx|
      if div.attributes["id"]&.value =~ /appendonsend/ || delete_following
        div.remove
        delete_following = true
      elsif div.attributes["class"]&.value =~ /gmail_quote/ && div.content.split("\n").join("")  =~ /On (\w+), (\w+) (\d+), (\d+) at (\d+)\:(\d+) (AM|PM) (.*) wrote/
        if replies_count >= limit
          div.remove
          delete_following = true
        end
        replies_count += 1
      end
    end

    content = doc.at('body').inner_html
    Nokogiri::HTML(content.strip)
  end
end
