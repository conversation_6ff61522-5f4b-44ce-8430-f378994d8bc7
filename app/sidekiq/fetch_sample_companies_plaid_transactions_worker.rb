class FetchSampleCompaniesPlaidTransactionsWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'low_intensity_schedule'

  def perform
    CronLock.lock("plaid_transactions:fetch_non_sample_transactions") do
      company_ids = Company.sample_companies.all(&:allow_access?).pluck(:id)
      run_fetching(company_ids, "sample")
    end
  end

  def run_fetching(company_ids, sample_type)
    Sidekiq.logger.level = Rails.logger.level
    started_at = Time.now.strftime("%Y-%m-%d.%H")
    Rails.logger.info("Started plaid_transactions:fetch_#{sample_type}_transactions-#{started_at}")
    batch_timer = 1
    company_ids.in_groups_of(50) do |id_group|
      BatchCompanyWorker.perform_in(batch_timer.minutes, id_group.compact)
      batch_timer += 2
    end
    Rails.logger.info("Completed plaid_transactions:fetch_#{sample_type}_transactions-#{started_at}")
  end
end
