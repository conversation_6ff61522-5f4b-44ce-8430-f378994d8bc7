class DeleteHelpTicketEmailsFromS3Worker
  include Sidekiq::Worker
  sidekiq_options queue: 'high_intensity_schedule'

  def perform
    s3 = Aws::S3::Resource.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )

    data = s3.bucket(Rails.application.credentials.aws[:s3][:bucket]).objects(prefix: 'helpdesk_emails/')

    if data.present?
      data.each do |obj|
        if obj.last_modified.to_date < 30.days.ago.to_date
          obj.delete
        end
      end
    end
  end
end
