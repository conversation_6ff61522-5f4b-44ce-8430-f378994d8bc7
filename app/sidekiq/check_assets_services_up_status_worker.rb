class CheckAssetsServicesUpStatusWorker
  include Sidekiq::Worker
  include RakeTaskExecutor
  sidekiq_options queue: 'low_intensity_schedule'

  def perform
    execute_rake_task("status:check_logo_status")
    execute_rake_task("status:check_dell_status")
    execute_rake_task("status:check_lenovo_warranty_v2_status")
    # execute_rake_task("status:check_hp_status")
    # execute_rake_task("status:check_lenovo_status")
  end
end
