class ShortIntegrationsDailyTasksSecondWorker
  include Sidekiq::Worker
  include RakeTaskExecutor
  sidekiq_options queue: 'integrations'

  def perform
    execute_rake_task("integrations:ubiquiti_async")
    execute_rake_task("integrations:jamf_pro_async")
    execute_rake_task("integrations:one_login_async")
    execute_rake_task("integrations:okta_sync_data")
    execute_rake_task("integrations:expensify_async")
    execute_rake_task("integrations:kandji_async")
    execute_rake_task("integrations:quickbooks_data_sync")
    execute_rake_task("integrations:google_workspace_async")
  end
end
