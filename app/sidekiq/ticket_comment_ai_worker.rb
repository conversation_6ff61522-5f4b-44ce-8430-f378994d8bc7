class TicketCommentAiWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'critical'

  def perform(ticket_id, action_type, comment_body, company_user_id, comment_id = nil, sub_action_type = "")
    return unless action_type.present?

    url = "#{Rails.application.credentials.ai_service[:url]}/edit-text/"
    uri = URI.parse(url)
    updated_comment = ""

    payload = {
      input_text: comment_body,
      type: action_type.downcase,
      company_user_id: company_user_id,
      sub_type: sub_action_type&.downcase || ""
    }.to_json

    headers = { "Content-Type" => "application/json" }

    retries ||= 0
    begin
      Net::HTTP.start(
        uri.host,
        uri.port,
        use_ssl: uri.scheme == "https",
        open_timeout: 5,
        read_timeout: 20
      ) do |http|
        request = Net::HTTP::Post.new(uri)
        request.body = payload
        request['Connection'] = 'close'

        headers.each { |k, v| request[k] = v }

        http.request(request) do |response|
          response.read_body do |chunk|
            cleaned_chunk = chunk.force_encoding('UTF-8').scrub
            updated_comment += cleaned_chunk
            ActionCable.server.broadcast(
              "ticket_comment_#{ticket_id}_#{company_user_id}_#{comment_id}",
              { message: updated_comment }
            )
          end
        end
      end
    end

    ActionCable.server.broadcast(
      "ticket_comment_#{ticket_id}_#{company_user_id}_#{comment_id}",
      { message: updated_comment }
    )

    ActionCable.server.broadcast("ticket_comment_#{ticket_id}_#{company_user_id}_#{comment_id}", { status: "done" })
  rescue => e
    if e.message.downcase.include?("failed to open tcp connection") &&
       e.message.downcase.include?("execution expired") &&
       retries < 5
      retries += 1
      sleep 2
      retry
    end
    ActionCable.server.broadcast("ticket_comment_#{ticket_id}_#{company_user_id}_#{comment_id}", { status: "error" })
    Bugsnag.notify(e.message) if Rails.env.staging? || Rails.env.production?
  end
end
