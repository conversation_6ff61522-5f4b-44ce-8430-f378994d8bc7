class SendCustomSurveyEmailWorker
  include Sidekiq::Job
  sidekiq_options queue: 'email'

  def perform(ticket_id)
    ticket = HelpTicket.find_by(id: ticket_id)
    return unless ticket
    workspace = ticket.workspace
    surveys = workspace.custom_surveys.where(visible: true, is_default: false)
    custom_form = ticket.custom_form
    surveys.each do |survey|
      # 0 means survey is for all custom forms
      next unless [0, custom_form.id].any? { |id| survey.custom_form_ids.include?(id) }

      form_fields = custom_form.custom_form_fields
      match_all = survey.trigger.match_all
      result = match_all

      survey.trigger.conditions.each do |condition|
        field = form_fields.find { |f| f.field_attribute_type == condition['start']['field_attribute_type'] && f.name == condition['start']['name']}
        res = check_condition(ticket, field, condition['middle'], condition['end'])
        if res && !match_all
          result = true
          break
        elsif !res && match_all
          result = false
          break
        end
      end
      send_email(ticket, survey) if result
    end
  end

  def send_email(ticket, survey)
    ticket_creator_emails(ticket, survey).each do |email_address|
      SurveyMailer.send_survey_mail(survey, email_address, ticket.id).deliver_now
    end
  end

  def ticket_creator_emails(ticket, survey)
    contributors = Contributor.where(id: ticket.creator_ids)
    user_contributors_ids = contributors.flat_map(&:contributor_ids_only_users).uniq

    user_contributors_ids.each do |id|
      create_template_response(id, ticket, survey)
    end

    Contributor.where(id: user_contributors_ids).map { |c| c.email }
  end

  def check_condition(ticket, field, middle, value)
    field_value = ticket_value(ticket, field)
    field_value = ActionView::Base.full_sanitizer.sanitize(field_value) if field.field_attribute_type == 'rich_text'

    case middle
    when 'is'
      field_value == value
    when 'is not'
      field_value != value
    when 'includes'
      value.is_a?(Array) ? (field_value & value).any? : field_value.downcase.include?(value.downcase)
    when 'does not include'
      value.is_a?(Array) ? (field_value & value).none? : !field_value.downcase.include?(value.downcase)
    when 'is empty'
      field_value.nil? || (field_value.respond_to?(:empty?) && field_value.empty?)
    when 'is not empty'
      !field_value.nil? && (field_value.respond_to?(:empty?) ? !field_value.empty? : true)
    else
      false
    end
  end

  def singular?(field_attribute_type)
    %w(text text_area static_text rich_text number date phone priority status list category radio_button email).include?(field_attribute_type)
  end

  def ticket_value(ticket, field)
    cfvs = ticket.custom_form_values.where(custom_form_field_id: field.id)
    singular?(field.field_attribute_type) ? cfvs.first&.value_str : cfvs.pluck(:value_int)
  end

  def create_template_response(cont_id, ticket, survey)
    CustomSurvey::Response.create!(
      name: survey.title,
      status: "unopened",
      comment: [],
      score: nil,
      contributor_id: cont_id,
      custom_survey_id: survey.id,
      company_id: ticket.company_id,
      workspace_id: ticket.workspace_id,
      help_ticket_id: ticket.id
    )
  end
end
