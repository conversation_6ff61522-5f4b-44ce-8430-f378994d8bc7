class ShortIntegrationsDailyTasksFirstWorker
  include Sidekiq::Worker
  include RakeTaskExecutor
  sidekiq_options queue: 'integrations'

  def perform
    execute_rake_task("integrations:xero_async")
    execute_rake_task("integrations:azure_ad_async")
    execute_rake_task("integrations:sync_azure_cloud_data")
    execute_rake_task("integrations:salesforce_async")
    execute_rake_task("integrations:aws_assets_sync")
    execute_rake_task("integrations:bill_data_sync")
    execute_rake_task("integrations:google_assets_sync")
  end
end
