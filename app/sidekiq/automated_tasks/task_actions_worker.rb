module AutomatedTasks
  class TaskActionsWorker
    include Sidekiq::Job
    include UnusedMemoryUtils
    include AutomatedTaskErrorLog

    sidekiq_options queue: 'at_actions'

    def perform(action_id, object_id, object_class_name, type_name)
      @action = AutomatedTasks::TaskAction.find_by(id: action_id)
      @object = object_class_name.constantize.find_by(id: object_id)
      return if GlobalEmailBlocking.last&.company_ids&.include?(@object.company.id)
      @type_name = type_name
      action&.call if @object.present? && @action.present?

      clear_unused_memory
    rescue => e
      retry_params = {
        action_id: action_id,
        object_id: object_id,
        object_class_name: object_class_name,
        type_name: type_name,
      }
      handle_task_error(e, @action.automated_task, 'TaskAction', @type_name, @object.company.id, retry_params)
    end

    def action
      "AutomatedTasks::Actions::#{@type_name}".constantize.new(@action, @object)
    end
  end
end
