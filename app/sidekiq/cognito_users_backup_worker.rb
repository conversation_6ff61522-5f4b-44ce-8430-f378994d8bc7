class CognitoUsersBackupWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'low_intensity_schedule'

  def perform
    @client = Aws::CognitoIdentityProvider::Client.new(
      region:  Rails.application.credentials.aws[:region],
      access_key_id: Rails.application.credentials.aws[:cognito][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:cognito][:secret_access_key]
    )

    @response = @client.list_users({
      user_pool_id: Rails.application.credentials.aws[:cognito][:user_pool_id],
    })

    s3 = Aws::S3::Resource.new(
      region: Rails.application.credentials.aws[:s3][:region]
    )

    file = "#{Rails.root}/public/users_data-#{Date.today}.csv"
    headers = ['Username', 'Email', 'User Attributes', 'User Create Date', 'User Last Modified Date', 'Enabled', 'User Status', 'MFA Options']

    CSV.open(file, 'w', write_headers: true, headers: headers) do |writer|
      @response.users.each do |user|
        att_arr = []
        user_email = ''
        user.attributes.map do |x|
          if x.name == 'email'
            user_email = x.value
          end
          att_arr << [x.name, x.value]
        end
        writer << [user.username, user_email, att_arr, user.user_create_date, user.user_last_modified_date, user.enabled, user.user_status, user.mfa_options]
      end
    end

    loop do
      @response = @client.list_users({
        user_pool_id: Rails.application.credentials.aws[:cognito][:user_pool_id],
        pagination_token: @response&.pagination_token
      })

      CSV.open(file, 'a+') do |writer|
        @response&.users.each do |user|
          att_arr = []
          user_email = ''
          user.attributes.map do |x|
            if x.name == 'email'
              user_email = x.value
            end
            att_arr << [x.name, x.value]
          end
          writer << [user.username, user_email, att_arr, user.user_create_date, user.user_last_modified_date, user.enabled, user.user_status, user.mfa_options]
        end
      end
      break if @response.pagination_token.blank?
    end

    obj = s3.bucket("cognito-pool-backup").object("#{Rails.env}/users_data-#{Date.today}.csv")

    File.open(file, 'rb') do |csv|
      obj.put(body: csv)
    end

    File.delete(file) if File.exist?(file)
  end
end
