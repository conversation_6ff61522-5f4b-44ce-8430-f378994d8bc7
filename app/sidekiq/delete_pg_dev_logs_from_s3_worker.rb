class DeletePgDevLogsFromS3Worker
  include Sidekiq::Worker
  sidekiq_options queue: 'high_intensity_schedule'

  def perform
    seven_days_ago = Date.today - 7.days
    s3_bucket.objects(prefix: 'pg_stat_activity_data/').each do |obj|
      next if obj.key.split('/').length < 2 || obj.last_modified.to_date >= seven_days_ago
      obj.delete
    end
  end

  private

  def aws_s3_client
    Aws::S3::Client.new(
      region: Rails.application.credentials.aws[:s3][:region],
      access_key_id: Rails.application.credentials.aws[:s3][:access_key_id],
      secret_access_key: Rails.application.credentials.aws[:s3][:secret_access_key]
    )
  end

  def s3_bucket
    Aws::S3::Resource.new(client: aws_s3_client).bucket('gogenuity-development')
  end
end
