class Integrations::MsIntuneAssets::SaveDevicesWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include MsIntuneCommonMethods
  include DiscoveredAssetLogs

  sidekiq_options queue: 'long_integrations'

  def perform(config_id, first_time_flag, starting_time, next_page_token, company_user_id = nil, asset_count = 0, discovered_asset_count = 0, managed_asset_count = 0)
    @company_user_id = company_user_id
    @asset_count = asset_count
    @config = set_read_replica_db do 
      Integrations::MsIntuneAssets::Config.find_by(id: config_id)
    end
    return unless @config

    @import_target = @config.import_type
    @new_discovered_assets_count = discovered_asset_count
    @new_managed_assets_count = managed_asset_count
    @starting_time = JSON.parse(starting_time).to_time
    @first_time_flag = first_time_flag
    refresh_access_token
    response = client.get_intune_devices(next_page_token)

    intune_devices = response["value"]
    intune_devices&.each do |device|
      @device_info = device
      @is_rooted = @device_info["isRooted"]
      serial_number = get_serial_no(@device_info["serialNumber"])
      if [@device_info["deviceName"], @device_info["wiFiMacAddress"], serial_number, @device_info["manufacturer"]].all?(&:blank?)
        next       # Skip processing devices having missing fields
      end

      @discovered_asset = find_discovered_asset(company, false, serial_number, nil, @device_info["deviceName"], nil, @device_info["manufacturer"], false)
      @discovered_asset ||= DiscoveredAsset.ms_intune.ready_for_import.new(company_id: company.id)

      managed_asset = find_managed_asset(company, serial_number, nil, @device_info["deviceName"], @device_info["manufacturer"])
      if managed_asset.present?
        @discovered_asset.status = :imported
        @discovered_asset.managed_asset_id = managed_asset.id
      end

      assign_discovered_asset_attributes
      assign_discovered_assets_hardware_details
      create_asset_software
      is_new = @discovered_asset.new_record?
      @discovered_asset.save!
      @asset_count += 1 if is_new
      @new_discovered_assets_count += 1 if is_new

      asset_source_data = {
        display_name: @device_info["deviceName"],
        machine_serial_no: @device_info["serialNumber"],
        manufacturer: @device_info["manufacturer"],
        mac_addresses: @device_info["wiFiMacAddress"],
        discovered_asset_type: asset_device_type,
        os_name: @device_info["operatingSystem"],
        source: "ms_intune"
      }
      add_source(asset_source_data)
      @discovered_asset.reload

      if @import_target == "managed_asset" && @discovered_asset.managed_asset_id.blank?
        BulkDiscoveredAssetsUpdate.new([], @discovered_asset.company, nil).move_to_managed_asset(@discovered_asset)
        @new_managed_assets_count += 1
      end
    rescue StandardError => e
      Rails.logger.error(e)
      Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Managed asset has already been taken")
      LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, @discovered_asset.to_json, "save_devices").to_json)
    end

    if next_page_link = response["@odata.nextLink"]
      next_page_token = next_page_link.split('$skiptoken=')[-1] if next_page_link&.include?('$skiptoken=')
      save_devices(config_id, first_time_flag, next_page_token, @new_discovered_assets_count, @new_managed_assets_count)
    else
      pusher(@config, true, 0.8)
      update_company_integration
      pusher(@config, true, 1, {
        new_ms_intune_discovered_assets: @new_discovered_assets_count,
        new_ms_intune_managed_assets: @new_managed_assets_count
      })
      create_logs
      intg_duration_analysis(company.id, @first_time_flag)
    end
  rescue Exception => e
    create_logs
    company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: e.message,
      company_user_id: @company_user_id,
      first_time_flag: @first_time_flag,
      failure_count: company_integration.failure_count + 1
    )
    company_integration.save!
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, "intune_integration").to_json)
    pusher(@config, false, 0)
  end

  def client
    @intune_service ||= Integrations::MsIntuneAssets::FetchData.new(company.id)
  end

  def company
    @company ||= set_read_replica_db do
      @config.company
    end
  end

  def company_integration
    @company_integration ||= set_read_replica_db do
      @config.company_integration
    end
  end

  def save_devices(config_id, first_time_flag, next_page_token, discovered_asset_count, managed_asset_count)
    Integrations::MsIntuneAssets::SaveDevicesWorker.perform_async(config_id, first_time_flag, @starting_time.to_json, next_page_token, @company_user_id, @asset_count, discovered_asset_count, managed_asset_count)
  end

  def update_company_integration
    if client.ok?
      company_integration.assign_attributes(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: 0
      )
    else
      company_integration.assign_attributes(
        sync_status: :failed,
        status: false,
        last_synced_at: DateTime.now,
        active: true,
        error_message: client.error,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: company_integration.failure_count + 1
      )
    end
    company_integration.save!
  end

  def assign_discovered_asset_attributes
    @discovered_asset.assign_attributes(
      display_name: @device_info["deviceName"],
      model: @device_info["model"],
      os_name: @device_info["operatingSystem"],
      manufacturer: @device_info["manufacturer"],
      mac_addresses: @device_info["wiFiMacAddress"],
      machine_serial_no: get_serial_no(@device_info["serialNumber"]),
      discovered_asset_type: asset_device_type,
      asset_type: get_asset_type,
      last_synced_at: DateTime.now,
      lower_precedence: is_lower_precedence
    )
  end

  def assign_discovered_assets_hardware_details
    hardware_detail = @discovered_asset.discovered_assets_hardware_detail ||= DiscoveredAssetsHardwareDetail.new
    hardware_detail.assign_attributes(
      memory: memory_in_gb(@device_info['physicalMemoryInBytes']),
      imei: @device_info['imei']
    )
  end

  def create_asset_software
    asset_softwares = @discovered_asset.asset_softwares
    return if is_lower_precedence && asset_softwares.present?

    if @discovered_asset.os_name.present?
      set_read_replica_db do
        asset_softwares.find_or_initialize_by({
          software_type: 'Operating System',
          name: @discovered_asset.os_name
        })
      end
    end
  end

  def asset_device_type
    @asset_device_type ||= :device
  end

  def get_asset_type
    @asset_device_type = "Mobile" unless @is_rooted.nil?
  end

  def event_params(error, detail, api_type)
    intg_id = set_read_replica_db do
      Integration.find_by_name("ms_intune_assets").id
    end
    {
      company_id: company.id,
      status: :error,
      error_detail: error.backtrace.join("\n"),
      class_name: self.class.name,
      integration_id: intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end
    {
      asset_sources: discovered_managed_asset_sources,
      current_source: "ms_intune_assets",
      discovered_asset: @discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.ms_intune.new
    }
  end

  def is_lower_precedence
    !is_higher_precedence?(**precedence_data)
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :ms_intune)
    end
    das.assign_attributes(
      updated_at: DateTime.now,
      asset_data: asset_source_data,
      company_integration_id: company_integration.id,
      managed_asset_id: @discovered_asset.managed_asset_id
    )
    das.save!
  end

  def get_serial_no(serial_no)
    serial_num = serial_no&.strip
    invalid_serial = ['null', 'tobefill', 'to be', 'system serial', 'default', 'not ap'].any? { |str| serial_num&.downcase&.gsub(".", "")&.include? str }
    invalid_serial ? '' : serial_num
  end
  
  def memory_in_gb(bytes)
    gb = bytes.to_f / (1024 * 1024 * 1024)
    gb.round
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count,'MS Intune', @company_user_id, company.id)
    end
  end
end
