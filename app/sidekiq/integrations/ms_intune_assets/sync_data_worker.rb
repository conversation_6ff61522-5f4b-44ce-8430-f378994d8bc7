class Integrations::MsIntuneAssets::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb
  include MsIntuneCommonMethods

  attr_accessor :config, :first_time_flag

  sidekiq_options queue: 'long_integrations'

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @starting_time = Time.current
    self.config = set_read_replica_db do 
      Integrations::MsIntuneAssets::Config.find_by(id: config_id)
    end
    return if self.config.nil?
    self.first_time_flag = first_time_flag
    @is_resyncing = is_resyncing

    pusher(config, true, 0.2)
    refresh_access_token
    pusher(config, true, 0.4)
    save_devices(config_id, first_time_flag)
    intg_duration_analysis(company.id, first_time_flag)
  rescue Exception => e
    send_notification_with_updating_status(e)
    Rails.logger.error(e)
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, 'intune_integration').to_json)
    pusher(config, false, 0)
  end

  def company
    set_read_replica_db do
      config.company
    end
  end

  def company_integration
    set_read_replica_db do
      config.company_integration
    end
  end

  def save_devices(config_id, first_time_flag)
    Integrations::MsIntuneAssets::SaveDevicesWorker.perform_async(config_id, first_time_flag, @starting_time.to_json, nil, @company_user_id, 0, 0, 0)
  end

  def client
    @intune_service ||= Integrations::MsIntuneAssets::FetchData.new(company.id)
  end

  def send_notification_with_updating_status(error)
    company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: error.message,
      company_user_id: @company_user_id,
      first_time_flag: @first_time_flag,
      failure_count: company_integration.failure_count + 1
    )
    company_integration.save!
    send_notification(company_integration) unless @first_time_flag
  end

  def event_params(error, detail, api_type)
    intg_id = set_read_replica_db do 
      Integration.find_by_name("ms_intune_assets").id
    end
    {
      company_id: company.id,
      status: :error,
      error_detail: error.backtrace.join("\n"),
      class_name: self.class.name,
      integration_id: intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end
end
