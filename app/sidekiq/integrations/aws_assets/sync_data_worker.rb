class Integrations::AwsAssets::SyncDataWorker
  include Sidekiq::Job
  include Regions
  include IntegrationsPushNotification
  include DiscoveredManagedAssetFinder
  include IntegrationsErrorNotification
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs

  sidekiq_options queue: 'integrations'

  attr_accessor :aws_assets_config, :retries, :first_time_flag

  def perform(aws_assets_config_id, first_time_flag, is_resyncing = false, company_user_id = nil, retries = 0)
    @company_user_id = company_user_id
    @asset_count = 0
    @starting_time = Time.current
    @aws_assets_config = set_read_replica_db do
      Integrations::AwsAssets::Config.find_by(id: aws_assets_config_id)
    end
    return if @aws_assets_config.nil?

    set_read_replica_db do
      @company_integration = @aws_assets_config.company_integration
      @company = @company_integration.company
    end

    @client = Integrations::AwsAssets::FetchData.new(@aws_assets_config)
    @import_target = @aws_assets_config.import_type
    @new_discovered_assets_count = 0
    @new_managed_assets_count = 0
    @first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    @retries = retries
    @worker_start_time = DateTime.current

    pusher(@aws_assets_config, true, 0.2)
    save_devices
    pusher(@aws_assets_config, true, 0.8)
    update_company_integration
    pusher(@aws_assets_config, true, 1, {
      new_aws_discovered_assets: @new_discovered_assets_count,
      new_aws_managed_assets: @new_managed_assets_count
    })
    create_logs
    intg_duration_analysis(@company.id, first_time_flag)
  rescue Exception => e
    create_logs
    send_notification_with_updating_status e
    Rails.logger.error(e)
    Bugsnag.notify(e) if (Rails.env.production? || Rails.env.staging?) && !e.message.include?("Managed asset has already been taken")
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, @company_integration.to_json, "aws_assets_integration").to_json)
    pusher(@aws_assets_config, false, 0)
  end

  private

  def save_devices
    fetch_devices&.each do |device|
      @device_info = device
      @discovered_asset = nil
      display_name = @device_info[:name]
      @asset_device_type = :device

      @discovered_asset = find_discovered_asset_for_cloud(@company, display_name, 'aws', @worker_start_time)
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: @company.id)

      @is_lower_precedence = !is_higher_precedence?(**precedence_data)
      assign_discovered_asset_attributes
      create_cloud_asset_attributes
      create_asset_software
      is_new = @discovered_asset.new_record?
      @discovered_asset.save!
      @asset_count += 1 if is_new
      @new_discovered_assets_count += 1 if is_new
      @discovered_asset.discovered_assets_hardware_detail

      asset_source_data = {
        display_name: @device_info[:name],
        asset_type: 'Virtual Machine',
        source: 'aws',
        machine_serial_no: nil,
        manufacturer: @device_info[:manufacturer],
        discovered_asset_type: @asset_device_type,
        mac_addresses: @device_info[:mac_addresses],
        ip_address: @device_info[:public_ip_address],
        os_name: @device_info[:platform_description]
      }
      add_source(asset_source_data)
      @discovered_asset.reload

      if @import_target == "managed_asset" && @discovered_asset.managed_asset_id.blank?
        BulkDiscoveredAssetsUpdate.new([], @discovered_asset.company, nil).move_to_managed_asset(@discovered_asset)
        @new_managed_assets_count += 1
      end
    rescue StandardError => e
      LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, @discovered_asset.to_json, "aws_assets_integration").to_json)
      raise e
    end
  end

  def update_company_integration
    if @client.impeccable?
      @company_integration.assign_attributes(
        sync_status: :successful,
        status: true,
        last_synced_at: DateTime.now,
        active: true,
        error_message: nil,
        notified: nil,
        company_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: 0
      )
    else
      @company_integration.assign_attributes(
        sync_status: :failed,
        status: false,
        last_synced_at: DateTime.now,
        active: true,
        error_message: @client.error,
        comany_user_id: @company_user_id,
        first_time_flag: @first_time_flag,
        failure_count: @company_integration.failure_count + 1
      )
    end
    @company_integration.save!
  end

  def fetch_devices
    running_instances.compact.flatten.map do |instance|
      {
        instance_id: instance.instance_id,
        name: instance_name(instance),
        type: instance.instance_type,
        manufacturer: 'Amazon EC2',
        architecture: instance.architecture,
        core_count: instance.cpu_options.core_count,
        harddisk: harddisk(instance),
        hypervisor: instance.hypervisor,
        launch_time: instance.launch_time.to_s,
        mac_address: instance.network_interfaces&.first&.mac_address,
        mac_addresses: mac_addresses(instance),
        platform_description: instance.image&.data&.description,
        platform_details: instance.image&.data&.platform_details,
        public_dns_name: instance.public_dns_name,
        public_ip_address: instance.public_ip_address,
        root_device_name: instance.root_device_name,
        state: instance.state.name,
        vpc_id: instance.vpc_id,
        threads_per_core: instance.cpu_options.threads_per_core,
        virtualization_type: instance.virtualization_type,
        memory: memory_information(instance.instance_type),
        availability_zone: availability_zone(instance),
        region: instance_region(availability_zone(instance)),
        security_group: instance&.security_groups&.first&.group_name,
        tags: instance_tags(instance)
      }
    end
  rescue StandardError => e
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, @discovered_asset.to_json, 'fetch_devices').to_json)
    raise e
  end

  def asset_type_id
    set_read_replica_db do
      @company.asset_types.find_by_name(@discovered_asset[:asset_type])&.id || @company.asset_types.find_by_name('Other').id
    end
  end

  def create_asset_software
    return if @is_lower_precedence && @discovered_asset.asset_softwares.present?
    set_read_replica_db do
      @discovered_asset.asset_softwares.find_or_initialize_by(asset_software_attributes)
    end
  end

  def asset_software_attributes
    {
      software_type: 'Operating System',
      name: @discovered_asset.os_name || @discovered_asset.os || ""
    }
  end

  def mac_addresses(instance)
    instance.network_interfaces.map { |network_interface| network_interface.data.mac_address.try(:upcase) }
  end

  def harddisk(instance)
    instance.volumes.inject('') { |harddisk, volume| harddisk += harddisk.empty? ? "#{volume.size}GB" : ", #{volume.size}GB" }
  end

  def memory_information(instance_type)
    mega_to_giga(instance_memory(instance_type))
  end

  def instance_memory(instance_type)
    @client.aws_client.describe_instance_types({ dry_run: false, instance_types: [instance_type] })&.instance_types&.first&.memory_info&.size_in_mi_b
  end

  def mega_to_giga(mega_bytes)
    "#{(mega_bytes / 1.0.kilobyte).round}GB" if mega_bytes.present?
  end

  def running_instances
    @client.selected_regions.map { |region| @client.region_specific_devices(region) }
  end

  def instance_name(instance)
    instance.tags&.find { |tag| tag.key == 'Name' }&.value || instance.instance_id
  end

  def availability_zone(instance)
    instance&.volumes&.first&.availability_zone
  end

  def instance_region(zone)
    Regions::AWSREGIONS.find { |region| zone.include?(region[:value]) }[:name]
  end

  def integration_location_id(location_name)
    if location_name.present?
      company_integration_location = set_read_replica_db do
        @company.integrations_locations.find_or_initialize_by(address: location_name, source: 'aws')
      end
      company_integration_location.save if company_integration_location.new_record?
      company_integration_location&.id
    end
  end

  def instance_tags(instance)
    instance.tags&.inject({}) { |hash, tag| hash.update("#{tag.key}": tag.value) }
  end

  def discovered_asset_attributes
    {
      display_name: @device_info[:name],
      source: 'aws',
      model: @device_info[:type],
      asset_type: 'Virtual Machine',
      os: @device_info[:platform_details],
      os_name: @device_info[:platform_description],
      mac_address: @device_info[:mac_address],
      manufacturer: @device_info[:manufacturer],
      discovered_asset_type: @asset_device_type,
      mac_addresses: @device_info[:mac_addresses],
      ip_address: @device_info[:public_ip_address]
    }
  end

  def assign_discovered_asset_attributes
    @discovered_asset.assign_attributes(
      **discovered_asset_attributes,
      last_synced_at: DateTime.now,
      integrations_locations_id: integration_location_id(@device_info[:region]),
      lower_precedence: @is_lower_precedence
    )

    @discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if @discovered_asset.discovered_assets_hardware_detail.blank?
    @discovered_asset.discovered_assets_hardware_detail.assign_attributes(
      memory: @device_info[:memory],
      hard_drive: @device_info[:harddisk],
      processor_cores: @device_info[:core_count]
    )
  end

  def cloud_attributes
    {
      **discovered_asset_attributes,
      state: @device_info[:state],
      region: @device_info[:region],
      memory: @device_info[:memory],
      instance_type: @device_info[:type],
      hard_drive: @device_info[:harddisk],
      hypervisor: @device_info[:hypervisor],
      instance_id: @device_info[:instance_id],
      launch_time: @device_info[:launch_time],
      processor_cores: @device_info[:core_count],
      security_group: @device_info[:security_group],
      public_dns_name: @device_info[:public_dns_name],
      root_device_name: @device_info[:root_device_name],
      threads_per_core: @device_info[:threads_per_core],
      availability_zone: @device_info[:availability_zone],
      virtualization_type: @device_info[:virtualization_type],
      **@device_info[:tags]
    }
  end

  def create_cloud_asset_attributes
    return if @is_lower_precedence && @discovered_asset.cloud_asset_attributes.present?
    cloud_attributes.each do |attribute|
      cloud_attr = set_read_replica_db do
        @discovered_asset.cloud_asset_attributes.find_or_initialize_by(key: attribute[0].to_s.gsub(/\W+/, ' ').titleize)
      end
      cloud_attr.value = attribute[1].to_s
    end
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :aws)
    end
    das.updated_at = DateTime.now
    das.asset_data = asset_source_data
    das.company_integration_id = @company_integration.id
    das.managed_asset_id = @discovered_asset.managed_asset_id
    das.save!
  end

  def event_params error, detail, api_type
    intg_id = set_read_replica_db do
      Integration.find_by_name("aws_assets").id
    end

    {
      company_id: @company.id,
      status: :error,
      error_detail: error.backtrace.join("\n"),
      class_name: self.class.name,
      integration_id: intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end

  def send_notification_with_updating_status error
    @company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      error_message: error.message
    )
    @company_integration.save!
    send_notification(@company_integration) unless @first_time_flag
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end

    {
      asset_sources: discovered_managed_asset_sources,
      current_source: "aws",
      discovered_asset: @discovered_asset,
      incoming_discovered_asset: DiscoveredAsset.aws.new
    }
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'AWS', @company_user_id, @company.id)
    end
  end
end
