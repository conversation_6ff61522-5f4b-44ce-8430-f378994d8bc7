class Integrations::Kandji::SaveDevicesWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include DiscoveredManagedAssetFinder
  include IntegrationPrecedence
  include IntegrationsAnalysis
  include ReadReplicaDb
  include DiscoveredAssetLogs
  
  sidekiq_options queue: 'integrations'

  def perform(config_id, first_time_flag, starting_time, next_link, company_user_id = nil, asset_count = 0, discovered_count = 0, managed_count = 0)
    @asset_count = asset_count
    @company_user_id = company_user_id
    @starting_time = JSON.parse(starting_time).to_time
    @config = set_read_replica_db do
      Integrations::Kandji::Config.find_by(id: config_id)
    end
    return unless @config
    
    @import_target = @config.import_type
    @new_discovered_assets_count = discovered_count
    @new_managed_assets_count = managed_count
    devices, next_link = client.list_devices(next_link)
    pusher(company_kandji_config, true, 0.4)
    save_kandji_devices(devices)
    if next_link.nil? || next_link.include?('None')
      pusher(company_kandji_config, true, 0.8)
      company_integration.assign_attributes(sync_status: :successful,
                                            status: true,
                                            last_synced_at: DateTime.now,
                                            active: true,
                                            error_message: nil,
                                            notified: nil,
                                            failure_count: 0,
                                            company_user_id: company_user_id,
                                            first_time_flag: first_time_flag
                                            )
      company_integration.save!
      pusher(company_kandji_config, true, 1, {
        new_kandji_discovered_assets: @new_discovered_assets_count,
        new_kandji_managed_assets: @new_managed_assets_count
      })
      create_logs
      intg_duration_analysis(company.id, first_time_flag)
    else
      Integrations::Kandji::SaveDevicesWorker.perform_async(@config.id, first_time_flag, @starting_time.to_json, next_link, @company_user_id, @asset_count, @new_discovered_assets_count, @new_managed_assets_count)
    end
  rescue Exception => e
    create_logs
    company_integration.assign_attributes(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: client.error || e.message,
      company_user_id: company_user_id,
      first_time_flag: first_time_flag,
      failure_count: company_integration.failure_count + 1
    )
    company_integration.save!
    Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    client.log_event(nil, 'kandji_integration', {}, e)
    pusher(company_kandji_config, false, 0)
  end

  def company
    @company ||= set_read_replica_db do
      @config.company
    end
  end

  def company_integration
    @company_integration ||= set_read_replica_db do
      @config.company_integration
    end
  end

  def client
    @kandji_service ||= Integrations::Kandji::FetchData.new(company_kandji_config)
  end

  def company_kandji_config
    @company_kandji_config ||= set_read_replica_db do
      company.kandji_config
    end
  end

  def save_kandji_devices(devices)
    devices.each do |device|
      device = device.with_indifferent_access
      mac_addresses = []
      serial_number = device[:serial_number]
      device_detail = client.get_device_details(device[:device_id]).with_indifferent_access
      os_name = os_name(device_detail[:general][:platform])
      os_version = device_detail[:general][:os_version]
      asset_tag = device_detail[:general][:asset_tag]
      asset_type = asset_type(device_detail[:general][:platform])
      display_name = device_detail[:general][:device_name]
      manufacturer = 'Apple'
      source = 'kandji'
      model = device_detail[:general][:model]
      used_by = device_used_by(device_detail[:general][:assigned_user])
      ip_address = device_detail[:network][:ip_address]
      processor_name = device_detail[:hardware_overview][:processor_name]
      processor_cores = device_detail[:hardware_overview][:total_number_of_cores]
      processor_logical_cores = device_detail[:hardware_overview][:number_of_processors]
      disk_free_space = disk_free_space(device_detail[:volumes]) if device_detail[:volumes].present?
      hard_disk = @disk_capacity
      memory = device_detail[:hardware_overview][:memory]
      model = device_detail[:hardware_overview][:model_identifier]
      mac_addresses << device_detail[:network][:mac_address] if device_detail[:network][:mac_address].present?

      @discovered_asset = find_discovered_asset(company, false, serial_number, mac_addresses, display_name, ip_address, manufacturer, false)
      @discovered_asset ||= DiscoveredAsset.ready_for_import.new(company_id: company.id)

      display_name_data = display_name.present? ? display_name : @discovered_asset.display_name
      mac_addresses_data = mac_addresses.present? ? mac_addresses : @discovered_asset.mac_addresses
      ip_address_data = ip_address.present? ? ip_address : @discovered_asset.ip_address

      is_lower_precedence = !is_higher_precedence?(**precedence_data)

      @discovered_asset.assign_attributes(
        display_name: display_name_data,
        asset_type: asset_type,
        asset_tag: asset_tag,
        machine_serial_no: serial_number,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        manufacturer: manufacturer,
        os_name: os_name,
        os: os_name.capitalize,
        os_version: os_version,
        model: model,
        source: source,
        last_synced_at: DateTime.now,
        lower_precedence: is_lower_precedence,
        discovered_asset_type: :device,
        used_by: used_by
      )

      managed_asset = find_managed_asset(company, serial_number, mac_addresses_data, display_name_data, manufacturer)

      if managed_asset.present?
        @discovered_asset.managed_asset = managed_asset
        @discovered_asset.status = :imported
      elsif @import_target == "managed_asset"
        @discovered_asset.status = :imported
      end

      @discovered_asset.discovered_assets_hardware_detail = DiscoveredAssetsHardwareDetail.new if @discovered_asset.discovered_assets_hardware_detail.blank?
      @discovered_asset.discovered_assets_hardware_detail.assign_attributes(
        memory: memory,
        hard_drive: hard_disk,
        disk_free_space: disk_free_space,
        processor: processor_name,
        processor_logical_cores: processor_logical_cores,
        processor_cores: processor_cores
      )

      is_new = @discovered_asset.new_record?
      @discovered_asset.save
      @asset_count += 1 if is_new
      @new_discovered_assets_count += 1 if is_new

      save_asset_operating_system(@discovered_asset) if device[:platform].present?
      save_asset_softwares(@discovered_asset, device)

      asset_source_data = {
        display_name: display_name,
        mac_addresses: mac_addresses_data,
        ip_address: ip_address_data,
        manufacturer: manufacturer,
        source: source
      }

      add_source(asset_source_data)
      @discovered_asset.reload

      if @import_target == "managed_asset" && @discovered_asset.managed_asset_id.blank?
        assets = Array.wrap(@discovered_asset)
        BulkDiscoveredAssetsUpdate.new(assets, @discovered_asset.company, nil).import_assets
        @new_managed_assets_count += 1
      end
    rescue Exception => e
      client.log_event(nil, 'save kandji device', {}, e)
    end
  rescue Exception => e
    client.log_event(nil, 'save_kandji_devices', {}, e)
  end

  def precedence_data
    discovered_managed_asset_sources = set_read_replica_db do
      @discovered_asset.managed_asset&.sources
    end
    {
      asset_sources: discovered_managed_asset_sources,
      current_source: 'kandji',
      discovered_asset: @discovered_asset,
    }
  end

  def asset_type(platform)
    case platform
    when 'Mac'
      'Laptop'
    when 'iMac'
      'Desktop'
    when 'AppleTV'
      'Tv'
    when 'iPad', 'iPhone'
      platform
    else
      'Apple Device'
    end
  end

  def device_used_by(assigned_user)
    assigned_user[:email] if assigned_user.present? && assigned_user[:email].present?
  end

  def disk_free_space(volumes)
    free_space = 0
    @disk_capacity = 0
    volumes.each do |volume|
      free_space += volume[:available].scan(/\d+/).first.to_i
      @disk_capacity += volume[:capacity].scan(/\d+/).first.to_i
    end
    @disk_capacity = "#{@disk_capacity}GB"
    "#{free_space}GB"
  end

  def os_name(platform)
    case platform
    when 'iPad', 'iPhone'
      'iOS'
    when 'Mac', 'iMac'
      'macOS'
    when 'AppleTv'
      'tvOS'
    end
  end

  def save_asset_operating_system(discovered_asset)
    os_name = discovered_asset.os_name
    os_name = "#{os_name} (#{discovered_asset.os_version})" if discovered_asset.os_version
    asset_operating_system = nil
    set_read_replica_db do
      asset_operating_system = discovered_asset.asset_softwares.find_or_initialize_by(
        software_type: 'Operating System',
        name: os_name
      )
    end
    asset_operating_system.save if asset_operating_system.new_record?
  rescue Exception => e
    client.log_event(nil, 'save_asset_operating_system', {}, e)
  end

  def save_asset_softwares(discovered_asset, device)
    device_softwares = client.get_device_apps(device[:device_id])
    discovered_asset_softwares = set_read_replica_db do
      discovered_asset.asset_softwares.where(software_type: 'Application')
    end
    device_softwares['apps'].each do |software|
      if software['app_name'].present?
        software = discovered_asset_softwares.find_or_initialize_by(
          name: software['app_name'],
          install_date: software['creation_date']
        )
        software.save if software.new_record?
      end
    end
    pusher(company_kandji_config, true, 0.6)
  rescue Exception => e
    client.log_event(nil, 'save_asset_softwares', {}, e)
  end

  def add_source(asset_source_data)
    das = set_read_replica_db do 
      AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :kandji)
    end
    das.asset_data = asset_source_data
    das.managed_asset_id = @discovered_asset.managed_asset_id
    das.company_integration_id = company_integration.id
    das.save
  rescue Exception => e
    client.log_event(nil, 'add_source', {}, e)
  end

  def create_logs
    if @asset_count > 0
      create_asset_history_logs(@asset_count, 'Kandji', @company_user_id, company.id)
    end
  end
end
