class Integrations::Kandji::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationsAnalysis
  include ReadReplicaDb

  sidekiq_options queue: 'integrations'

  attr_accessor :config, :first_time_flag

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @starting_time = Time.current
    self.config = set_read_replica_db do
      Integrations::Kandji::Config.find_by(id: config_id)
    end
    return if self.config.nil?
    self.first_time_flag = first_time_flag
    @is_resyncing = is_resyncing
    @intg_id = set_read_replica_db do
      Integration.find_by_name('kandji').id
    end

    if company_integration
      pusher(company_kandji_config, true, 0.1)
      list_devices

      intg_duration_analysis(company.id, first_time_flag)
    end
  rescue Exception => e
    # TODO: need revert this
    # failed_company_integration(e)
    # Rails.logger.error(e)
    # Bugsnag.notify(e) if Rails.env.production? || Rails.env.staging?
    # pusher(company.kandji_config, false, 0)
    client.log_event(nil, 'kandji_integration', {}, e)
    pusher(company_kandji_config, true, 1)
  end

  def company
    @company ||= set_read_replica_db do
      config.company
    end
  end

  def company_integration
    @company_integration ||= set_read_replica_db do
      config.company_integration
    end
  end

  def company_kandji_config
    set_read_replica_db do
      company.kandji_config
    end
  end

  def client
    @kandji_service ||= Integrations::Kandji::FetchData.new(company_kandji_config)
  end

  def failed_company_integration(excep = nil)
    company_integration.update!(
      sync_status: :failed,
      status: false,
      last_synced_at: DateTime.now,
      active: true,
      error_message: excep&.message || 'Sorry, there was an error, please try again later.',
      company_user_id: @company_user_id,
      first_time_flag: @first_time_flag,
      failure_count: company_integration.failure_count + 1
    )

    client.api_logs.each do |log|
      LogCreationWorker.perform_async('Logs::ApiEvent', log.to_json)
    end
  end

  def list_devices
    Integrations::Kandji::SaveDevicesWorker.perform_async(config.id, first_time_flag, @starting_time.to_json, nil, @company_user_id, 0, 0, 0)
  end
end
