class Integrations::AzureAdAssets::SyncDataWorker
  include Sidekiq::Job
  include IntegrationsPushNotification
  include IntegrationsErrorNotification
  include IntegrationPrecedence
  include DiscoveredManagedAssetFinder
  include IntegrationsAnalysis
  include ReadReplicaDb
  include AzureAdAssetsCommonMethods

  sidekiq_options queue: 'long_integrations'

  attr_accessor :config, :first_time_flag

  def perform(config_id, first_time_flag, is_resyncing = false, company_user_id = nil)
    @company_user_id = company_user_id
    @starting_time = Time.current
    set_read_replica_db do
      self.config = Integrations::AzureAdAssets::Config.find(config_id)
      @intg_id = Integration.find_by_name("azure_ad_assets").id
    end
    self.first_time_flag = first_time_flag
    @is_resyncing = is_resyncing

    refresh_access_token
    pusher(config, true, 0.2)
    Integrations::AzureAdAssets::SaveDevicesWorker.perform_async(config_id, first_time_flag, @starting_time.to_json, nil, @company_user_id, 0, 0, 0)
  rescue Exception => e
    error_messages = [
      "invalid_grant",
      'Access token has expired.',
      'Access token validation failure.',
      'Access token has expired or is not yet valid.',
      'Signing key is invalid.',
      'Your access token has expired. Please renew it before submitting the request.'
    ]

    if !first_time_flag && error_messages.find{ |em| e.message.downcase.include?(em.downcase) }
      send_notification(company_integration)
    end

    Rails.logger.error(e)
    if Rails.env.production? || Rails.env.staging?
      Bugsnag.notify(e) unless error_messages.find{ |em| e.message.downcase.include?(em.downcase) }
    end
    LogCreationWorker.perform_async('Logs::ApiEvent', event_params(e, company_integration.to_json, "azure_ad_assets_integration").to_json)
    pusher(config, false, 0)
  end

  def company
    @company ||= set_read_replica_db do
      config.company
    end
  end

  def company_integration
    @company_integration ||= set_read_replica_db do 
      config.company_integration
    end
  end

  def client
    @azure_ad_assets_service ||= Integrations::AzureAdAssets::FetchData.new(company.id)
  end

  def event_params(error, detail, api_type)
    {
      company_id: company.id,
      status: :error,
      error_detail: error.backtrace&.join("\n"),
      class_name: self.class.name,
      integration_id: @intg_id,
      api_type: api_type,
      error_message: error.message,
      detail: detail,
      response: "422",
      created_at: DateTime.now
    }
  end
end
