class CheckRecurringGeneralTransactionsWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'low_intensity_schedule'

  def perform
    CronLock.lock("general_transactions:check_reocurring") do
      Company.not_sample.find_each do |company|
        begin
          GeneralTransactionRecurrenceService.new(company).check_recurring_transactions
        rescue => e
          Rails.logger.warn "Issue creating a recurring transaction from original transaction: #{company.id}. Error message: #{e.message}"
          next
        end
      end
    end
  end
end
