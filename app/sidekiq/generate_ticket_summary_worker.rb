class GenerateTicketSummaryWorker
  include Sidekiq::Worker
  sidekiq_options queue: 'critical'

  def perform(ticket_id, company_user_id)
    url = "#{Rails.application.credentials.ai_service[:url]}/summary/?id=#{ticket_id}"
    user = CompanyUser.find_by(id: company_user_id).user
    url += "&user_id=#{user.id}" if user.super_admin?
    uri = URI.parse(url)
    summary_str = ""

    retries ||= 0
    begin
      Net::HTTP.start(
        uri.host,
        uri.port,
        use_ssl: uri.scheme == "https",
        open_timeout: 5,
        read_timeout: 20
      ) do |http|
        request = Net::HTTP::Get.new(uri)
        request['Connection'] = 'close'

        http.request(request) do |response|
          response.read_body do |chunk|
            cleaned_chunk = chunk.encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
            summary_str += cleaned_chunk
            ActionCable.server.broadcast(
              "ticket_summary_#{ticket_id}_#{company_user_id}",
              { message: summary_str }
            )
          end
        end
      end
    end

    ActionCable.server.broadcast(
      "ticket_summary_#{ticket_id}_#{company_user_id}",
      { message: summary_str }
    )
    ai_summary = AiSummary.find_or_initialize_by(help_ticket_id: ticket_id)
    ai_summary.summary = summary_str
    ai_summary.save!

    ActionCable.server.broadcast("ticket_summary_#{ticket_id}", { status: "done" })
  rescue => e
    if e.message.downcase.include?("failed to open tcp connection") &&
       e.message.downcase.include?("execution expired") &&
       retries < 5
      retries += 1
      sleep 2
      retry
    end
    ActionCable.server.broadcast("ticket_summary_#{ticket_id}", { status: "error" })
    Bugsnag.notify(e.message) if Rails.env.staging? || Rails.env.production?
  end
end
