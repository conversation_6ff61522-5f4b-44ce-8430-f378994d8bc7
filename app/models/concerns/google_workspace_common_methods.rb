module GoogleWorkspaceCommonMethods
  extend ActiveSupport::Concern

  included do
    def precedence_data
      {
        asset_sources: @discovered_asset.managed_asset&.sources,
        current_source: 'google_workspace',
        discovered_asset: @discovered_asset,
        incoming_discovered_asset: DiscoveredAsset.google_workspace.new
      }
    end

    def add_source(asset_source_data)
      das = AssetSource.find_or_initialize_by(discovered_asset_id: @discovered_asset.id, source: :google_workspace)
      das.asset_data = asset_source_data
      das.managed_asset_id = @discovered_asset.managed_asset_id
      das.company_integration_id = company_integration.id
      das.updated_at = DateTime.now
      das.save!
    end

    def mac_address_valid?(mac_address)
      regex = /^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$/i
      mac_address.to_s.include?("00:00:00:00:00:00") ? false : mac_address.to_s.match(regex).present?
    end
  
    def get_asset_type(res)
      asset_type = CompanyAssetType.find_by_name(res)&.name
  
      if asset_type.present?
        asset_type
      else
        asset_type = CompanyAssetType.find_by(company_id: @current_company.id, name: "Uncategorized")
        asset_type ||= CompanyAssetType.create(company_id: @current_company.id, name: "Uncategorized")
        asset_type.name
      end
    end

    def event_params(error, detail, api_type)
      intg_id = Integration.find_by_name("google_workspace").id
      {
        company_id: company.id,
        status: :error,
        error_detail: error.backtrace.join("\n"),
        class_name: self.class.name,
        integration_id: intg_id,
        api_type: api_type,
        error_message: error.message,
        detail: detail,
        response: "422",
        created_at: DateTime.now
      }
    end

    def refresh_access_token
      refresh_token_detail = client.refresh_token_call

      if refresh_token_detail["access_token"]
        expiry_time = Time.now + refresh_token_detail["expires_in"]
        @config.update_columns(
          token: refresh_token_detail["access_token"],
          expires_at: expiry_time)

        @config.reload
      end
    end
  end
end
