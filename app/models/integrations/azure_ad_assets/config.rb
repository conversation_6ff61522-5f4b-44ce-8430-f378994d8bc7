class Integrations::AzureAdAssets::Config < ApplicationRecord
  include CompanyCache

  self.table_name = 'azure_ad_assets_configs'
  attr_accessor :skip_callbacks

  belongs_to :company
  belongs_to :company_user

  has_one :company_integration, as: :integrable, dependent: :destroy

  validates :token, presence:true
  validates_uniqueness_of :company_id, message: 'azure ad already integrated'

  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: Proc.new{ |config| config.skip_callbacks }
  before_destroy :destroy_integration_data

  enum import_type: [:managed_asset, :discovered_asset]

  def create_comp_intg_and_execute_job
    azure_ad_assets = Integration.find_by(name: 'azure_ad_assets')
    comp_intg = CompanyIntegration.find_or_initialize_by(
      company_id: company_id,
      integrable_type: 'Integrations::AzureAdAssets::Config',
      integrable_id: id,
      integration_id: azure_ad_assets.id
    )

    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!
    Integrations::AzureAdAssets::SyncDataWorker.perform_async(id, true, false, company_user_id)
  end

  def destroy_integration_data
    self.company.discovered_assets.azure_ad_devices.where.not(status: :imported).destroy_all
  end
end
