class Integrations::GoogleAssets::Config < ApplicationRecord
  include CompanyCache

  self.table_name = 'google_assets_configs'
  attr_accessor :skip_callbacks

  belongs_to :company
  belongs_to :company_user

  has_one :company_integration, as: :integrable, dependent: :destroy
  has_many :projects,
           class_name: "Integrations::GoogleAssets::Project",
           foreign_key: 'google_assets_config_id',
           dependent: :destroy

  accepts_nested_attributes_for :projects

  validates_uniqueness_of :company_id, message: "google cloud is already integrated"
  validate :validate_projects

  before_validation :destroy_old_records, on: :update
  after_commit :create_comp_intg_and_execute_job, on: [:create, :update], unless: Proc.new { |config| config.skip_callbacks }
  before_destroy :destroy_integration_data

  enum import_type: [:managed_asset, :discovered_asset]

  def create_comp_intg_and_execute_job
    google_assets_int = Integration.find_by(name: 'google_assets')
    comp_intg = CompanyIntegration.find_or_initialize_by(company_id: company_id,
                                                         integrable_type: 'Integrations::GoogleAssets::Config',
                                                         integrable_id: id,
                                                         integration_id: google_assets_int.id)

    comp_intg.assign_attributes(status: true, sync_status: :pending, company_user_id: company_user_id)
    comp_intg.save!

    Integrations::GoogleAssets::SyncDataWorker.perform_async(id, true, false, company_user_id)
  end

  def destroy_old_records
    old_projects = projects.where('id IS NOT NULL')
    old_projects.destroy_all
    company.discovered_assets.google.where.not(status: :imported).destroy_all
  end

  def destroy_integration_data
    self.company.discovered_assets.google.where.not(status: :imported).destroy_all
  end

  def as_json(options = nil)
    super.merge(projects: projects)
  end

  def validate_projects
    new_projects = projects.select { |p| p.id.blank? }
    new_projects.each do |project|
      status_code = google_assets_service.authorize_project project.project_id
      next if status_code.eql?(:ok)

      case status_code
      when :forbidden
        error_message = "Access not configured or forbidden"
      when :not_found
        error_message = "Project not found"
      else
        error_message = "Access denied"
      end
      
      @projects_validation_failed = true
      project.assign_attributes(is_valid: false, error_message: error_message)
    end

    errors.add(:projects, "are not valid") if @projects_validation_failed
  end

  def google_assets_service
    @google_assets_service ||= Integrations::GoogleAssets::FetchData.new(company.id)
  end
end
