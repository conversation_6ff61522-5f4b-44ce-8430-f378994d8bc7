class Prompt < ApplicationRecord
  belongs_to :prompts_template

  validates_presence_of :name, :model, :response_format, :prompts_template_id, message: "is required"
  before_save :ensure_single_active_prompt, if: -> { will_save_change_to_is_active? }
  before_destroy :prevent_destroy_if_active

  def ensure_single_active_prompt
    if is_active
      Prompt.where.not(id: id).update_all(is_active: false)
    else
      errors.add(:base, "Cannot disable it, please make another prompt as Global default.")
      throw(:abort)
    end
  end

  def prevent_destroy_if_active
    if is_active
      errors.add(:base, "Cannot delete an active prompt. Please disable it first.")
      throw(:abort)
    end
  end
end
