class HelpTicket < ActiveRecord::Base
  include PgSearch::Model
  include Rewards
  include Events
  include Linkables
  include TicketValues
  include MsTeamsConstants
  include SlackConstants
  include AutomatedTasks::EventFiring
  include UniquelyIdentified
  include ScheduledSlas
  include DeleteHelpTicketCacheKey
  include PusherLog
  include CompanyCache

  TICKET_NUMBER_PREFIX_REGEX = /[A-Za-z]+-[1-9][0-9]*/
  TICKET_NUMBER_INT_REGEX = /[1-9][0-9]*/

  pg_search_scope :search_text,
                  against: [:help_paragraph, :ticket_number],
                  using: { :tsearch => {:prefix => true} }

  has_many :ticket_emails, dependent: :destroy
  has_many :time_spents, dependent: :destroy
  has_many :project_tasks, dependent: :destroy
  has_many :help_ticket_comments, dependent: :destroy
  has_many :merged_help_ticket_comments, dependent: :nullify, class_name: 'HelpTicketComment', foreign_key: :merged_ticket_id
  has_many :help_ticket_activities, -> { order(created_at: :desc) }, dependent: :destroy
  has_many :attachment_uploads, as: :attachable, dependent: :destroy
  has_many :custom_form_values, as: :module, dependent: :destroy, :autosave => true
  has_many :custom_form_fields, through: :custom_form_values
  has_many :stop_watch_timers, dependent: :destroy
  has_many :ticket_sessions, dependent: :destroy
  has_many :scheduled_slas, class_name: "Sla::Schedule", dependent: :destroy
  has_many :scheduled_automated_tasks, class_name: "ScheduledAutomatedTasks", dependent: :destroy
  has_many :help_ticket_task_checklists, dependent: :destroy
  has_many :task_checklists, through: :help_ticket_task_checklists
  has_many :survey_responses, class_name: "CustomSurvey::Response", foreign_key: "help_ticket_id", dependent: :destroy
  has_one  :closing_survey, dependent: :destroy
  has_many :help_ticket_drafts, dependent: :destroy
  has_one :ai_summary, dependent: :destroy

  scope :owned_by, ->(contributor_id) { select_field_int_query("created_at", contributor_id) }
  scope :open, -> { where(status: 'Open') }
  scope :in_progress, -> { where(status: 'In Progress') }
  scope :closed, -> { where(status: 'Closed') }
  scope :unclosed, -> { where.not(status: "Closed") }
  scope :unarchived, -> { where(archived: false) }
  scope :active, -> { where(archived: false).unclosed }
  scope :assigned, -> { 
      joins(custom_form_values: :custom_form_field)
      .where("custom_form_fields.field_attribute_type = ?
        and custom_form_fields.name = 'assigned_to'
        and custom_form_values.value_int is not null",
             CustomFormField.field_attribute_types[:people_list])
  }

  scope :high_priority, -> { select_field_str_query('priority', 'high') }
  scope :medium_priority, -> { select_field_str_query('priority', 'medium') }
  scope :low_priority, -> { select_field_str_query('priority', 'low') }

  scope :merged, -> { where.not(active_ticket_id: nil) }
  scope :not_merged, -> { where(active_ticket_id: nil) }

  enum priority_types: [:low, :medium, :high]
  enum source: [:manually_added, :email, :slack, :auto_generated, :ms_teams, :splitted, :mobile_app, :desktop_app]
  enum secondary_source: [:imported, :incoming_tab, :portal, :direct_email]

  belongs_to :company
  belongs_to :location
  belongs_to :custom_form

  # This might seem odd to have workspace tied to the custom form AND the help ticket, but 
  # it's necessary.  A help ticket might be moved from one workspace to another.
  belongs_to :workspace

  before_save :set_event_type

  after_update :mark_tasks_as_completed, if: :is_set_closed?

  before_commit Proc.new{ set_created_by(current_user) }, :set_users_notification_status, on: :create
  after_commit :create_intercom_event,
               :award_points_to_user, on: :create
  before_destroy :remove_event_and_points

  after_commit :create_scheduled_slas, on: :create
  after_commit :trigger_value_event, on: :create
  after_commit :send_survey_email, on: :create
  before_save :delete_cache_key
  before_save :save_ticket_number, if: :should_generate_ticket_number?
  before_destroy :delete_cache_key
  after_destroy :trigger_value_event

  validates_uniqueness_of :ticket_number, scope: [:company, :workspace]
  validates_presence_of :company
  validate :ticket_number_validation, on: [:create, :update], if: :ticket_number_changed?
  validate :no_duplicate_in_one_minute, on: :create

  attr_accessor :skip_event_trigger, :current_user, :skip_ticket_number_validation, :skip_survey_trigger, :skip_ticket_number_generation

  def ticket_number_validation
    if self.ticket_number.present? && !self.skip_ticket_number_validation
      customize_ticket_number_setting = enabled_custom_ticket_number_setting
      if customize_ticket_number_setting.present?
        regex = /^#{TICKET_NUMBER_PREFIX_REGEX}$/
        if self.ticket_number.match(regex).present?
          if self.ticket_number.split('-').first != customize_ticket_number_setting.options["custom_prefix"]
            errors.add(:ticket_number, "prefix does not match.")
          end
        else
          errors.add(:ticket_number, "has an incorrect format.")
        end
      else 
        regex = /^#{TICKET_NUMBER_INT_REGEX}$/
        if self.ticket_number.match(regex).blank?
          errors.add(:ticket_number, "has an incorrect format.")
        end
      end
    end
  end

  def save_with_ticket_number!
    ActiveRecord::Base.transaction do
      save_with_ticket_number
      ActiveRecord::Base.connection.commit_db_transaction unless Rails.env.test?
    rescue => e
      Rails.logger.error("Transaction failed: #{e.message}")
      ActiveRecord::Base.connection.execute "ROLLBACK"
    end
  end

  def update_ticket_number
    customize_ticket_number_setting = enabled_custom_ticket_number_setting
    if customize_ticket_number_setting.present?
      max_ticket_number = get_max_ticket_number(workspace)
      custom_ticket_number = customize_ticket_number_setting.options["custom_ticket_number"].to_i
      max_ticket_number = [max_ticket_number, custom_ticket_number].max
    else
      max_ticket_number = get_max_ticket_number(company)
    end

    new_ticket_number = max_ticket_number + 1
    if customize_ticket_number_setting.present?
      custom_prefix = customize_ticket_number_setting.options["custom_prefix"]
      self.ticket_number = "#{custom_prefix}-#{new_ticket_number}"
    else
      self.ticket_number = new_ticket_number.to_s
    end
  end

  def get_max_ticket_number(entity)
    entity.help_tickets
          .pluck(:ticket_number)
          .map { |num| num.split('-').last.to_i }
          .max.to_i
  end

  def enabled_custom_ticket_number_setting
    customize_ticket_number_setting = DefaultHelpdeskSetting.find_by(setting_type: 'customize_ticket_number_for_help_desk')
    customize_ticket_number_helpdesk_setting = workspace.helpdesk_settings.find_by(default_helpdesk_setting_id: customize_ticket_number_setting.id, enabled: true)
  end

  def save_ticket_number
    @retries ||= 0
    retry_limit = 5
    should_retry = false
    begin
      update_ticket_number
      self.validate!
    rescue => e
      error_messages = [
        "Validation failed: Ticket number has already been taken",
        "index_help_tickets_on_ticket_number_and_company_and_workspace"
      ]

      if error_messages.find { |em| e.message.downcase.include?(em.downcase) }
        @retries += 1
        if @retries <= retry_limit
          sleep(2)
          should_retry = true
        else
          raise "Ticket number conflict occurred, retries exceeded"
        end
      else
        raise e
      end
    end
    save_ticket_number if should_retry
  end

  def last_comment_at
    help_ticket_comments.inject(nil) do |last, comment|
      if last.blank? && comment.created_at && !comment.private_user_flag
        comment.created_at
      elsif last && comment.created_at && comment.created_at > last && !comment.private_user_flag
        comment.created_at
      else
        last
      end
    end
  end

  def due_at
    project_tasks.inject(nil) do |last, task|
      if task.completed_at.blank? && last.blank? && task.due_at
        task.due_at
      elsif task.completed_at.blank? && last && task.due_at && task.due_at < last
        task.due_at
      else
        last
      end
    end
  end

  def as_json(options = {})
    json_hash = super(options)
    json_hash = json_hash.merge({
      linkable_id: linkable&.id,
      universal_links: linkable&.source_linkable_links,
      values: custom_form_values.includes(:custom_form_attachment),
      merged_tickets: merged_help_tickets,
      merge_parent: merge_parent,
      creator_ids: !self.creators.is_a?(String) ? self.creators&.pluck(:contributor_id) : nil,
      workspace_json: workspace_json
    })
  end

  def workspace_json
    {
      id: self.workspace.id,
      name: self.workspace.name,
      company: {
        id: self.company.id,
        name: self.company.name,
      }
    }
  end

  def help_ticket_fields
    fields = []
    custom_form_values.each do |cfv|
      cff = cfv.custom_form_field.as_json
      json_cfv = cfv.as_json
      if cfv.custom_form_field.field_attribute_type == 'attachment'
        json_cfv = json_cfv.merge({attachments: cfv.custom_form_attachment.as_json})
      end
      fields << cff.merge({values: [json_cfv]})
    end
    fields
  end

  def merged_help_tickets
    merged_tickets = HelpTicket.where(active_ticket_id: self.id)
    merged = merged_tickets&.map do |t|
      {
        id: t.id,
        subject: t.subject,
        created_at: t.created_at,
        ticket_number: t.ticket_number,
      }
    end
  end

  def merge_parent
    parent = HelpTicket.find_by(id: self.active_ticket_id)
    return nil unless parent
    {
      id: parent.id,
      subject: parent.subject,
      created_at: parent.created_at,
      ticket_number: parent.ticket_number,
    }
  end

  def total_project_tasks
    {
      total_tasks_count: project_tasks.count,
      completed_tasks_count: project_tasks.where.not(completed_at: nil).count
    }
  end

  def total_time_spent
    hours = 0
    minutes = 0
    hours = self.time_spents.sum(:hours_spent)
    minutes = self.time_spents.sum(:minutes_spent)
    hours += minutes / 60
    minutes = minutes % 60
    str = ""
    str = "#{hours} hr".pluralize(hours) if hours != 0
    str << " #{minutes} min".pluralize(minutes) if hours != 0  && minutes != 0
    str = "#{minutes} min".pluralize(minutes) if minutes != 0 && str.blank?
    str = "0 hr 0 min" if str.blank?
    str
  end

  def closed?
    status == "Closed"
  end

  def is_set_closed?
    saved_changes[:status] && saved_changes[:status][1] == 'closed'
  end

  def send_notification
    HelpDesk::TicketNotification.perform_in(4.seconds, self.id)
  end

  def self.eager_load_associations_for_report
    includes(:help_ticket_activities,
             :help_ticket_comments,
             :location,
             :time_spents,
             custom_form_values: [:custom_form_field, :custom_form_attachment])
  end

  def create_help_ticket_activity data, activity_type, activity_action, current_company_user
    HelpTicketActivity.create(
      help_ticket_id: self.id,
      owner_id: current_company_user.id,
      activity_type: activity_type,
      activity_action: activity_action,
      data: data
    )
  end

  def sync_attachments(company_module_data = nil, custom_entity = nil)
    if company_module_data.present? && custom_entity.present?
      ticket_text_values = company_module_data.custom_entity.custom_form_values.includes(:custom_form_field).select { |value| value.custom_form_field.field_attribute_type == "rich_text" }
      if ticket_text_values.present?
        ticket_text_values.each do |value|
          attachment_links = URI.extract(value.value_str)
          HelpDesk::SyncTicketAttachments.new(custom_entity, attachment_links, nil, company_module_data.company_module_data).sync_attachments if attachment_links.present?
        end
      end
    end
  end

  def create_external_user_activity(company_user = nil, custom_entity = nil)
    if custom_entity.present?
      created_by_values = custom_entity.custom_form_values.select{ |val| val.custom_form_field.name === "created_by" && val.custom_form_field.field_attribute_type == "external_user"}
      contributor_values = created_by_values.map(&:value)

      contributor_values.each do |value|
        activity = {
          help_ticket_id: custom_entity.id,
          owner_id: company_user&.id,
          activity_type: "external_user",
          data: {
            previous_value: nil,
            current_value: value,
            activity_label: "Created By",
            source: custom_entity.source,
            ticket_number: custom_entity.ticket_number
          }
        }
        activity[:data][:email] = value if company_user.nil? && value.is_a?(String)
        HelpTicketActivity.create(activity)
      end
    end
  end

  def create_source_activity(automated_task = nil)
    src = self.source == 'ms_teams' ? 'Microsoft Teams' : self.source.titleize
    activity = {
      help_ticket_id: self.id,
      owner_id: self.creator_contributors.first&.company_user&.id,
      activity_type: 'source',
      data: {
        previous_value: nil,
        current_value: src,
        activity_label: 'Source',
        source: self.source,
        ticket_number: self.ticket_number,
        ticket_subject: self.subject,
        email: self.creator_contributors.first&.guest&.email,
        task_id: automated_task&.id,
        task_serial_number: automated_task&.serial_number
      },
    }
    HelpTicketActivity.create(activity)
  end

  def self.select_field_base_query(field_name)
    joins("INNER JOIN custom_form_values as #{field_name}_values ON #{field_name}_values.module_id = help_tickets.id").
      joins("INNER JOIN custom_form_fields as #{field_name}_fields on #{field_name}_fields.id = #{field_name}_values.custom_form_field_id").
      where("#{field_name}_values.module_id = help_tickets.id")
  end

  def self.select_field_int_query(field_name, value)
    select_field_base_query(field_name).where("#{field_name}_values.value_int = ?", value)
  end

  def self.select_field_str_query(field_name, value)
    select_field_base_query(field_name).where("#{field_name}_values.value_str = ?", value)
  end

  def create_activity(params, current_value, previous_value)
    HelpTicketActivity.create(params) if current_value != previous_value
  end

  def increment_unread_comments_count
    update_columns(unread_comments_count: unread_comments_count + 1)
  end
  
  def mark_all_comments_as_read
    return if unread_comments_count == 0
  
    update_columns(unread_comments_count: 0)
  end

  private

  def should_generate_ticket_number?
    (id.blank? || ticket_number.blank?) && !skip_ticket_number_generation
  end

  def no_duplicate_in_one_minute
    duplicate_tickets = HelpTicket
      .joins(custom_form_values: :custom_form_field)
      .where(
        source: self.source,
        status: self.status,
        company_id: self.company_id,
        workspace_id: self.workspace_id,
        custom_form_id: self.custom_form_id,
        assigned_user_ids: self.assigned_user_ids,
        created_at: (Time.now - 1.minute)..Time.now,
        custom_form_fields: { custom_form_id: self.custom_form_id, name: "subject" },
        custom_form_values: { value_str: self.subject }
      )

    duplicate_tickets.each do |ticket|
      if ticket.creator_contributors_ids == self.creator_contributors_ids
        errors.add(:base, "A Ticket with identical details has been submitted within the past minute.")
        break
      end
    end
  end

  def specific_activity(status)
    activities = self.help_ticket_activities.where(activity_type: 'status').where("data->'activity_label' = ?", 'Status').reverse
    activities.detect { |activity| activity.data["current_value"] == status  }
  end

  def mark_tasks_as_completed
    pending_tasks = self.project_tasks.where(completed_at: nil)
    pending_tasks.update_all(completed_at: DateTime.now)
  end

  def trigger_value_event
    self.update_columns(opened_at: DateTime.now) if !self.destroyed?
    if !self.skip_event_trigger
      PusherActions::HelpTickets::TicketUpdated.new(self).call
      update_helpdesk_dashboard
    end
  end

  def update_helpdesk_dashboard
    return if Rails.env.test?
    Pusher.trigger(
      self.company.guid,
      "helpdesk-dashboard-updated",
      { }
    )
  rescue => e
    create_pusher_log(e, 'help_ticket') if (Rails.env.staging? || Rails.env.production?) && !e.class.to_s.include?('HTTPError')
  end

  def send_survey_email
    SendCustomSurveyEmailWorker.perform_async(id) if !self.skip_survey_trigger
  end
end
